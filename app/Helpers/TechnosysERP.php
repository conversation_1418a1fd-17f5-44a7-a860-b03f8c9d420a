<?php

namespace App\Helpers;

use App\Models\FulfillmentOrder;
use App\Models\DumpErpSale;
use App\Models\OrderItem;
use App\Models\ReturnReceive;
use App\Models\ReturnReceiveItem;
use App\Service\Integration\ERP\ERPForTechnosys;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;

class TechnosysERP
{
    protected $erp_service;

    public function __construct(ERPForTechnosys $erp_service)
    {
        $this->erp_service = $erp_service;
    }

    public function createReturnOrder(array $params): void
    {
        $shipment = $params['shipment'];
        $order = $shipment->order;

        $rma = $params['rma'];

        $fulfillment_order = FulfillmentOrder::whereShipmentId($shipment->id)
            ->firstOrFail();

        if ( $return_receive_id = ReturnReceive::whereOrderId($shipment->order_id)->value('id') ) {
            $sale_line_items = [];
            $return_receive_items = ReturnReceiveItem::where('return_receive_id', $return_receive_id)->where('reason','Received')->get();

            foreach ($return_receive_items as $item) {
                $order_item = OrderItem::whereOrderId($shipment->order_id)->where('product_id', $item->product_id)->first();

                $sale_line_items[] = [
                    "Barcode" => $order_item->barcode,
                    "Quantity" => $item->quantity
                ];
    
            }

        }else if($rma){
            $rma_items = $params['rma_items'];
            $sale_line_items = $this->processLineItems($rma_items, $order);
        }else{
            $sale_line_items = $this->processLineItems($fulfillment_order->items, $order);
        }

        $sales_payload = $this->buildReturnOrderPayload(
            $fulfillment_order,
            $sale_line_items,
            $shipment->tracking_number
        );

        // Save payload to DumpErpSale for tracking
        $dump_erp_sale = DumpErpSale::create([
            'seller_id' => $shipment->seller_id,
            'order_id' => $order->id,
            'fulfillment_id' => $fulfillment_order->id,
            'shipment_id' => $shipment->id,
            'type' => 'return',
            'payload' => json_encode($sales_payload),
        ]);

        // Call the API using the ERPForTechnosys service
        $response = $this->erp_service->postReturnOrder($sales_payload);

        $this->handleApiResponse($response, $dump_erp_sale, $order->id);
    }

    private function buildReturnOrderPayload($fulfillment_order, $sale_line_items, $tracking_number): array
    {
        return [
           "EStoreId" => strtolower("estore-cli-" . $fulfillment_order->reference_id),
            "EStoreOrderId" => $fulfillment_order->reference_id,
            "ReferenceDocumentId" => $fulfillment_order->erp_fulfillment_id,
            "ReasonId" => 1, // Assuming a default reason ID
            "Comment" => "Return Order for FO ID: {$fulfillment_order->reference_id}, Tracking Number: {$tracking_number}",
            "Lines" => $sale_line_items,
        ];
    }

    private function processLineItems($fulfillment_order_items): array
    {
        $line_items = [];

        foreach ($fulfillment_order_items as $fulfillment_order_item) {
            $order_item = $fulfillment_order_item->order_item;
            $line_items[] = [
                "Barcode" => $order_item->barcode,
                "Quantity" => $order_item->quantity,
            ];
        }

        return $line_items;
    }

    private function handleApiResponse($response, $dump_erp_sale, $order_id): void
    {
        $status = $response['status_code'];
        $key = "ERP Return Sales Sync Process";
        $send_email = false;

        if ($status === 201 && isset($response['success']) && $response['success'] === true) {
            $dump_erp_sale->markAsSynced(json_encode($response['data']), $status);
            $document_number = $response['data']['detail'][0]['Id'] ?? '';
            $message = "The document number received from ERP: {$document_number}";
            $status = "Success";
            OrderComment::add(compact('order_id','message','key','status'));
        } else {
            $dump_erp_sale->markSyncFailed(json_encode($response['data'] ?? $response['message']), $status);
            $message = $response['data']['message'] ?? $response['message'] ?? 'Something went wrong';
            $status = "Failed";
            OrderComment::add(compact('order_id','message','key','status'));
            $send_email = true;
        }

        Log::info("Technosys | ERP Sync Status: {$status} | Message: {$message}");

        if ($send_email) {
            $this->sendErrorNotification($order_id, $message, $status);
        }
    }

    private function sendErrorNotification($order_id, $message, $status): void
    {
        $recipients = explode(',', env('TECHNOSYS_NOTIFICATION_EMAIL', ''));
        $unity_email = env('UNITY_NOTIFICATION_EMAIL', '');
        $subject = "Technosys - Order ID: {$order_id} - Return Order - Sync Failed - Status Code: {$status}";
        $body = "An error occurred during the ERP Sync Process.\n\nMessage: {$message}";

        Mail::raw($body, function ($m) use ($recipients, $unity_email, $subject) {
            $m->to($recipients)
                ->bcc($unity_email)
                ->subject($subject);
        });
    }
}
