<?php 

namespace App\Helpers;

use App\Traits\ExceptionTrait;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;

class Magento2
{
    use ExceptionTrait;

    public static function getToken($url,$username,$password)
    {
        $adminUrl = $url.'/rest/V1/integration/admin/token/';
        $ch = curl_init();
        $data = array("username" => $username, "password" => $password);

        $data_string = json_encode($data);
        $ch = curl_init($adminUrl);
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, "POST");
        curl_setopt($ch, CURLOPT_POSTFIELDS, $data_string);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, array(
                'Content-Type: application/json',
                'Content-Length: ' . strlen($data_string))
        );
        $token = curl_exec($ch);
        $token=  json_decode($token);
        return $token;
   
    }
}