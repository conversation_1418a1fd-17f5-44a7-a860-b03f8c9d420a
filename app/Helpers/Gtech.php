<?php 

namespace App\Helpers;

use App\Events\CreateStockOrderThroughUnityForGtechEvent;
use App\Models\Inventory;
use App\Models\Product;
use App\Models\Courier\City;
use App\Models\SellerLocation;
use Exception;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use App\Models\OrderTag;
use App\Models\Tag;
use App\Models\Setting;
use App\Models\FulfillmentOrder;
use App\Models\Order;
use App\Models\OrderItem;
use App\Models\FulfillmentOrderGtechUpdates;
use Carbon\Carbon;
use App\Models\ShipmentHistory;
use App\Models\DumpGtechPosOnline;

class Gtech
{
    protected $api_key;
    protected $url;
    protected $cambridge_pocs;
    protected $unity_pocs;

    public function __construct($api_key, $url) {
        $this->api_key = $api_key;
        $this->url = $url;
        $this->cambridge_pocs = ['<EMAIL>','<EMAIL>','<EMAIL>'];
        $this->unity_pocs = '<EMAIL>';

    }


    public function SetOrderStatusCompleted($fulfilment_order,$shipment)
    {
       
        try{
            $others = $this->cambridge_pocs;
            $unity = $this->unity_pocs;

            \Log::info("SetOrderStatusCompleted updated Gtech Class :: Started");
            $curl = curl_init();

            $seller_location = SellerLocation::find($fulfilment_order->seller_location_id);
            $type = "Retail";
            if($seller_location && $seller_location->is_ffc_enabled == 1){
                $type = "FFC";
            }

            $api_url = $this->url.'/api/post/SetOrderStatusCompleted?api='.$this->api_key.'&OrderNo='.$fulfilment_order->reference_id.'&Type='.$type;

            curl_setopt_array($curl, array(
            CURLOPT_URL => $api_url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 0,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => 'POST',
            ));
            curl_setopt($curl, CURLOPT_HTTPHEADER, array('Content-Length: 0'));


            $response = curl_exec($curl);

            curl_close($curl);
            \Log::info("SendDeliveredShipmentDetailsToGTECHEvent : response is");
            \Log::info("SendDeliveredShipmentDetailsToGTECHEvent : ".$response);

            $text_response_message = $response;
            if (is_object(json_decode($response))) 
            { 
                $decoded_response = json_decode($response,true);
                $text_response_message = isset($decoded_response['error']) ? $decoded_response['error'] : (isset($decoded_response['response']) ? $decoded_response['response'] : null);
            }

            if($text_response_message == "Order Status Updated Successfully"){
                \Log::info("SendDeliveredShipmentDetailsToGTECHEvent : Status check worked");
                // $shipment->erp_sync = 1;
                // $shipment->save();
                FulfillmentOrderGtechUpdates::updateOrCreate(['fulfilment_order_id' =>$fulfilment_order->id], ['order_marked_as_delivered' =>  true ]);
            }else{
                \Log::info("SendDeliveredShipmentDetailsToGTECHEvent : Status check failed");
                \Log::info("SendDeliveredShipmentDetailsToGTECHEvent : Fulfilment Order".$fulfilment_order->id);
                $unity = $this->unity_pocs;
                $subject = "GTECH <".$fulfilment_order->reference_id."> - <SetOrderStatusCompleted> - Sync Failed";
                Mail::raw($response.' | GTECH SetOrderStatusCompleted : Failed', function ($m)  use($unity,$subject) {
                    $m->to($unity)
                        ->subject($subject);
                });
            }
        } catch (\Throwable $th) {
            \Log::info("GTECH SendDeliveredShipmentDetailsToGTECHEvent : Failed ". $fulfilment_order->id);
            \Log::info($th);    
            $subject = "GTECH <".$fulfilment_order->reference_id."> - <SetOrderStatusCompleted> - Sync Failed - Catch Block";
            Mail::raw($th.' | GTECH SetOrderStatusCompleted : Catch', function ($m)  use($unity,$subject) {
                $m->to($unity)
                    ->subject($subject);
            });
        }
        
    }

    public function posOnline($fulfilment_order,$shipment)
    {
        try {
            $curl = curl_init();
            $api_url = $this->url.'/api/post/posonline';
            \Log::info($api_url);
            \Log::info($fulfilment_order);
            \Log::info($shipment);
            $order = $shipment->order;
            \Log::info($order);
            $others = $this->cambridge_pocs;
            $unity = $this->unity_pocs;
            $sellerLocation = SellerLocation::find($shipment->return_received_at_location); // where shipment was received
            // $sellerLocation = SellerLocation::find($fulfilment_order->seller_location_id);

            $discount_per_item = $order->discount / count($fulfilment_order->items);
            $shipping_per_item = $order->shipping_fee / count($fulfilment_order->items);
            $grand_total = 0;
            $discount = 0;
            $shipping_charges = 0;
            $items_array = [];

            if(count($fulfilment_order->items) > 1){
                $count = 0;
                foreach($fulfilment_order->items as $item){
                    $item_discount = 0;
                    $order_item = OrderItem::find($item->order_item_id);
                    $product = Product::find($order_item->product_id);
                    $order_grand_total = $order_item->actual_price * $order_item->quantity; 

                    \Log::info($order_grand_total);

                    if( $order_item->discount > 0){
                        $order_grand_total = $order_grand_total - $order_item->discount; 
                        $item_discount =  $order_item->discount / $order_item->quantity;
                    }


                    $curent_stock = 0;
                    $inventory = Inventory::where("product_id",$product->id)->where("seller_location_id",$fulfilment_order->seller_location_id)->first();
                    if($inventory){
                        $curent_stock = $inventory->stock;
                    }
                    $items_array[$count]['BarCode'] = $order_item->barcode;
                    $items_array[$count]['ItemName'] = $order_item->SKU."-".$order_item->product_name."-".$order_item->description;
                    $items_array[$count]['Qty'] = -$order_item->quantity;
                    $selling_price = $order_item->actual_price / (1 + $order_item->tax_rate);
                    $items_array[$count]['ActPrice'] = $selling_price;
                    $items_array[$count]['GSTP'] = $order_item->tax_rate * 100;
                    $items_array[$count]['SellingPrice'] = $order_item->actual_price;
                    $discount_percentage = 0;
                    if($order_item->actual_price != null || $order_item->actual_price != 0){
                        $discount_percentage = ( $item_discount / $order_item->actual_price) * 100;
                    }
                    $items_array[$count]['DiscPer'] = $discount_percentage;
                    $barcode_discount = ($selling_price * $order_item->quantity) * ( $discount_percentage / 100);
                    $items_array[$count]['BarcodeDiscount'] = -$barcode_discount;
                    $retail = ($selling_price * $order_item->quantity) - $barcode_discount;
                    $items_array[$count]['Retail'] = $retail;
                    $gst = $retail * $order_item->tax_rate;
                    $items_array[$count]['GST'] = $gst;
                    $items_array[$count]['GrossSales'] =  -$retail;
                    $items_array[$count]['DiscAmount'] = 0;
                    $items_array[$count]['NetSale'] = - ($retail + $gst);
                    $items_array[$count]['Stock'] = $curent_stock;
                    $items_array[$count]['PctCode'] = $product->hs_code;
                    $items_array[$count]['FBRDesc'] = $product->hs_code;

                    $count++;


                    $grand_total = $grand_total + $order_item->sub_total + $order_item->tax;
                    // $grand_total = $grand_total + $order_grand_total;
                    $discount =  $discount +  $discount_per_item;
                    $shipping_charges =  $shipping_charges +  $shipping_per_item;
                }
                
            }else{
                $order_item = OrderItem::find($fulfilment_order->items[0]->order_item_id);
                $product = Product::find($order_item->product_id);

                $order_grand_total = $order_item->actual_price * $order_item->quantity; 
                \Log::info($order_grand_total);
                $item_discount = 0;


                if( $order_item->discount > 0){
                    $order_grand_total = $order_grand_total - $order_item->discount; 
                    $item_discount =  $order_item->discount / $order_item->quantity;
                }

                $curent_stock = 0;
                $inventory = Inventory::where("product_id",$product->id)->where("seller_location_id",$fulfilment_order->seller_location_id)->first();
                if($inventory){
                    $curent_stock = $inventory->stock;
                }
                $items_array[0]['BarCode'] = $order_item->barcode;
                $items_array[0]['ItemName'] = $order_item->SKU."-".$order_item->product_name."-".$order_item->description;
                $items_array[0]['Qty'] = -$order_item->quantity;
                $selling_price = $order_item->actual_price / (1 + $order_item->tax_rate );
                $items_array[0]['ActPrice'] = $selling_price;
                $items_array[0]['GSTP'] = $order_item->tax_rate * 100;
                $selling_price = $order_item->actual_price / (1 + $order_item->tax_rate );
                $items_array[0]['SellingPrice'] = $order_item->actual_price;
                $discount_percentage = 0;
                if($order_item->actual_price != null || $order_item->actual_price != 0){
                    $discount_percentage = ( $item_discount / $order_item->actual_price) * 100;
                }
                $items_array[0]['DiscPer'] = $discount_percentage;
                $barcode_discount = ($selling_price * $order_item->quantity) * ( $discount_percentage / 100);
                $items_array[0]['BarcodeDiscount'] = -$barcode_discount;
                $retail = ($selling_price * $order_item->quantity) - $barcode_discount;
                $items_array[0]['Retail'] = $retail;
                $gst = $retail * $order_item->tax_rate;
                $items_array[0]['GST'] = $gst;
                $items_array[0]['GrossSales'] =  -$retail;
                $items_array[0]['DiscAmount'] = 0;
                $items_array[0]['NetSale'] = - ($retail + $gst);
                $items_array[0]['Stock'] = $curent_stock;
                $items_array[0]['PctCode'] = ( $product->hs_code == null ? "" : $product->hs_code);
                $items_array[0]['FBRDesc'] =( $product->hs_code == null ? "" : $product->hs_code);

                $grand_total = $grand_total + $order_item->sub_total + $order_item->tax;
                // $grand_total = $grand_total + $order_grand_total;
                $discount =  $discount +  $discount_per_item;
                $shipping_charges =  $shipping_charges +  $shipping_per_item;

            }
            if($shipment->tracking_number == $order->shipping_fee_charged){
                $shipping_charges  =  $order->shipping_fee;
            }else{
                $shipping_charges  =  0;
            }

            \Log::info($grand_total);
            \Log::info($shipping_charges);
            \Log::info($discount);
            // $order_grand_total =  ($grand_total + $shipping_charges) - $discount;
            $order_grand_total =  $grand_total + $shipping_charges;

            \Log::info($order_grand_total);

            $payment_method_ids = [49,50,51,54]; // cambridge payment method ids
            $account_id = "";
            $terminal = "";
            $account_name = "";

            if ( in_array($order->seller_payment_method_id,$payment_method_ids) ) {
                $account_id = "0090028";
                $account_name = "LIAB Ag Coupon";
                $terminal = $account_id;
            }else{
                $account_id = config('enum.gtech_erp_courier_account_ids')[$shipment->courier_id];
                $account_name = config('enum.gtech_erp_courier_account_name')[$shipment->courier_id];
                $terminal = "";

            }

            $data['BillNo']  =  $fulfilment_order->reference_id."-RTRN";
            $data['BillDate']  =  Carbon::parse($shipment->updated_at)->format('m-d-Y');
            $data['SalesMan']  =  "0000";
            $data['Terminal']  =  $terminal; //shall be blank in case of cod else account id
            $data['CardNo']  = "";
            $data['OnAccount']  =  $account_id;
            $data['Description']  = $account_name;
            $data['CashAmount']  = 0;
            $data['NetReceivableAmount']  = -($order_grand_total + $order->fbr_tax);
            $data['DRCRAmount']  =  0; // will be blank as per latest document
            $data['OnAccountAmount']  = -($order_grand_total +  $order->fbr_tax);  //This will be same as "NetReceivableAmount"
            $data['ReturnAmount']  =  0;
            $data['BalanceAmount']  =  0;
            $data['Customer']  =  $order->customer_name;
            $data['ContactNo']  =  $order->customer_number;
            $data['Email']  =  $order->customer_email;
            $data['Address']  =  ( $order->shipping_address == null ? "" : $order->shipping_address);
            $data['Location']  = $sellerLocation->seller_reference_id;
            $data['DiscountType']  =  "";
            $data['SlipTime']  =  Carbon::parse($shipment->updated_at)->format('m-d-Y H:i:s');
            $data['WebOrderNo']  = $fulfilment_order->reference_id;
            $data['FreightAmount']  =  0;
            $data['FBRINVNo']  =  ( $shipment->fbr_return_invoice_no == null ? "" : $shipment->fbr_return_invoice_no);
            $data['UserId']  =  "system";
            $data['BuyerCNic']  = 0;
            $data['ReturnNo']  =  "";
            if($shipment->tracking_number == $order->shipping_fee_charged){
                $data['AddOtherCharges']  =  $order->shipping_fee;
            }else{
                $data['AddOtherCharges']  =  0;
            }
            $data['SalesType']  =  "Return";
            $data['CNNumber']  = $shipment->tracking_number;
            $data['CreditMemono']  =  ( $order->is_voucher_order_wise == 0 && $order->voucher != null ?  $order->voucher : "");
            $data['CreditmemoAmount']  =  ( $order->is_voucher_order_wise == 0 && $order->voucher != null ?  $discount : 0);
            $data['CreditMemoOrderNo']  = ( $order->is_voucher_order_wise == 0 && $order->voucher != null ?  $order->voucher : "");
            $data['ServiceFee']  =  $order->fbr_tax;

          
            
            $data['items_detail'] = $items_array;

            $dump_gtech_pos_online = [];
            $dump_gtech_pos_online['dump_gtech_pos_onlines'] = $data;
            $dump_gtech_pos_online['order'] = $order;
            $dump_gtech_pos_online['fulfillment_order'] = $fulfilment_order;
            $dump_gtech_pos_online['shipment'] = $shipment;
            $dump_gtech_pos_online['sales_type'] = 2;
            DumpGtechPosOnline::organizeAndCreateRecord($dump_gtech_pos_online);
            
            \Log::info($data);
            \Log::info(json_encode($data));
            // \Log::info(json_encode($data));

            $curl = curl_init();

            curl_setopt_array($curl, array(
            CURLOPT_URL => $api_url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 0,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => 'POST',
            CURLOPT_POSTFIELDS => json_encode($data),
            CURLOPT_HTTPHEADER => array(
                'Content-Type: application/json'
            ),
            ));

            $response = curl_exec($curl);
            \Log::critical("The Response of POS Online insert is ::");
            \Log::critical($response);

            curl_close($curl);

            \Log::info("GTECH posOnline : response is");
            \Log::info("GTECH posOnline : ".$response);
            $expected_response = "Bill # ".$fulfilment_order->reference_id."-RTRN Saved Successfully ";
            if($response == $expected_response){
                \Log::info("GTECH posOnline :  Data Saved Successfully");
                $shipment_history = ShipmentHistory::where('shipment_id',$shipment->id)->where('status','Return Received')->where('erp_sync',0)->first();
                if($shipment_history){
                    $shipment_history->erp_sync = 1;
                    $shipment_history->erp_sync_at = Carbon::now()->toDateTimeString();
                    $shipment_history->save();
                }
                FulfillmentOrderGtechUpdates::updateOrCreate(['fulfilment_order_id' =>$fulfilment_order->id], ['order_marked_as_returned' =>  true ]);
                DumpGtechPosOnline::whereSellerId($shipment->seller_id)->where('bill_no',$fulfilment_order->reference_id."-RTRN")->update(['is_synced' => true]);

            }else{
                \Log::info("GTECH posOnline : Failed ". $fulfilment_order->id);
                \Log::info("GTECH posOnline : ".$response);

                $subject = "GTECH <".$fulfilment_order->reference_id."> - <posOnline> - Sync Failed";
                Mail::raw($response.' | GTECH posOnline (Return)  : Failed', function ($m)  use($others,$unity,$subject) {
                    $m->to($others)
                        ->bcc($unity)
                        ->subject($subject);
                });
                
            }
        } catch (\Throwable $th) {
            \Log::info("GTECH posOnline (Return) : Try Catch  ". $fulfilment_order->id);
            \Log::info($th);

            $subject = "GTECH <".$fulfilment_order->reference_id."> - <posOnline> - Sync Failed - Catch Block";
            Mail::raw($th.' | GTECH posOnline (Return)  : Catch', function ($m)  use($unity,$subject) {
                $m->to($unity)
                ->subject($subject);
            });
            
        }
        
    }

    public function createTransferOrder($items)
    {
        try{
            \Log::info("createTransferOrder Gtech Class :: Started");
            $curl = curl_init();

            $params = array("api" => $this->api_key , "data" => $items);
            \Log::info(json_encode($params));
            $api_url = $this->url.'/api/post/TransferIssueOmni';

            curl_setopt_array($curl, array(
            CURLOPT_URL => $api_url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 0,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => 'POST',
            CURLOPT_POSTFIELDS => json_encode($params),
            CURLOPT_HTTPHEADER => array(
                'Content-Type: application/json'
            ),
            ));

            $response = curl_exec($curl);

            curl_close($curl);
            \Log::info("createTransferOrder : response is");
            \Log::info("createTransferOrder : ".$response);
            if($response == "Data Save Successfully"){
                \Log::info("createTransferOrder : Status check worked for :: ".$items[0]['URReferenceId']);

            }else{
                \Log::info("createTransferOrder : Status check failed");
                \Log::info("createTransferOrder : Fulfilment Order".$items[0]['URReferenceId']);
                $stock_reference = $items[0]['URReferenceId'];
                $message = "createTransferOrder : Status check failed | The response is | ".$response;

                $others = $this->cambridge_pocs;
                $unity = $this->unity_pocs;
                $subject = "GTECH <".$stock_reference."> - <createTransferOrder> - Sync Failed";

                Mail::raw($message, function ($m)  use($others,$unity,$subject) {
                    $m->to($unity)
                        ->subject($subject);
                });
            }
        } catch (\Throwable $th) {
            \Log::info("GTECH createTransferOrder : Failed ". $items[0]['URReferenceId']);
            \Log::info($th);     
            $message = "createTransferOrder : Catch | ".$th;
            $stock_reference = $items[0]['URReferenceId'];
            
            $subject = "GTECH <".$stock_reference."> - <createTransferOrder> - Sync Failed - Catch Block";
            Mail::raw($message, function ($m) use ($subject) {
                $m->to('<EMAIL>')->bcc('<EMAIL>')
                ->subject($subject);
            });
        }
        
    }
    
    public function posOnlineForRMA($fulfilment_order,$shipment,$rma_items,$original_shipment)
    {

        \Log::info("posOnlineForRMA  :: Started");
        \Log::info($rma_items);
        try {
            $curl = curl_init();
            $api_url = $this->url.'/api/post/posonline';
            \Log::info($api_url);
            \Log::info($fulfilment_order);
            \Log::info($shipment);
            $order = $shipment->order;
            \Log::info($order);
            $others = $this->cambridge_pocs;
            $unity = $this->unity_pocs;
            $sellerLocation = SellerLocation::find($shipment->return_received_at_location); // where shipment was received
            // $sellerLocation = SellerLocation::find($fulfilment_order->seller_location_id);

            $discount_per_item = $order->discount / count($rma_items);
            $shipping_per_item = $order->shipping_fee / count($rma_items);
            $grand_total = 0;
            $discount = 0;
            $shipping_charges = 0;
            $items_array = [];

            if(count($rma_items) > 1){
                $count = 0;
                foreach($rma_items as $item){
                    $item_discount = 0;
                    $order_item = OrderItem::find($item->order_items_id);
                    $product = Product::find($order_item->product_id);
                    $order_grand_total = $order_item->actual_price * $item->quantity; 

                    \Log::info($order_grand_total);
                    
                    if( $order_item->discount > 0){
                        $order_grand_total = $order_grand_total - $order_item->discount; 
                        $item_discount =  $order_item->discount / $item->quantity;
                    }


                    $curent_stock = 0;
                    $inventory = Inventory::where("product_id",$product->id)->where("seller_location_id",$fulfilment_order->seller_location_id)->first();
                    if($inventory){
                        $curent_stock = $inventory->stock;
                    }
                    $items_array[$count]['BarCode'] = $order_item->barcode;
                    $items_array[$count]['ItemName'] = $order_item->SKU."-".$order_item->product_name."-".$order_item->description;
                    $items_array[$count]['Qty'] = -$item->quantity;
                    $selling_price = $order_item->actual_price / (1 + $order_item->tax_rate);
                    $items_array[$count]['ActPrice'] = $selling_price;
                    $items_array[$count]['GSTP'] = $order_item->tax_rate * 100;
                    $items_array[$count]['SellingPrice'] = $order_item->actual_price;
                    $discount_percentage = 0;
                    if($order_item->actual_price != null || $order_item->actual_price != 0){
                        $discount_percentage = ( $item_discount / $order_item->actual_price) * 100;
                    }
                    $items_array[$count]['DiscPer'] = $discount_percentage;
                    $barcode_discount = ($selling_price * $item->quantity) * ( $discount_percentage / 100);
                    $items_array[$count]['BarcodeDiscount'] = -$barcode_discount;
                    $retail = ($selling_price * $item->quantity) - $barcode_discount;
                    $items_array[$count]['Retail'] = $retail;
                    $gst = $retail * $order_item->tax_rate;
                    $items_array[$count]['GST'] = $gst;
                    $items_array[$count]['GrossSales'] =  -$retail;
                    $items_array[$count]['DiscAmount'] = 0;
                    $items_array[$count]['NetSale'] = - ($retail + $gst);
                    $items_array[$count]['Stock'] = $curent_stock;
                    $items_array[$count]['PctCode'] = $product->hs_code;
                    $items_array[$count]['FBRDesc'] = $product->hs_code;

                    $count++;


                    $grand_total = $grand_total + $order_item->sub_total + $order_item->tax;
                    // $grand_total = $grand_total + $order_grand_total;
                    $discount =  $discount +  $discount_per_item;
                    $shipping_charges =  $shipping_charges +  $shipping_per_item;
                }
                
            }else{
                $order_item = OrderItem::find($rma_items[0]->order_items_id);
                $product = Product::find($order_item->product_id);

                $order_grand_total = $order_item->actual_price * $rma_items[0]->quantity; 
                \Log::info($order_grand_total);
                $item_discount = 0;


                if( $order_item->discount > 0){
                    $order_grand_total = $order_grand_total - $order_item->discount; 
                    $item_discount =  $order_item->discount / $rma_items[0]->quantity;
                }

                $curent_stock = 0;
                $inventory = Inventory::where("product_id",$product->id)->where("seller_location_id",$fulfilment_order->seller_location_id)->first();
                if($inventory){
                    $curent_stock = $inventory->stock;
                }
                $items_array[0]['BarCode'] = $order_item->barcode;
                $items_array[0]['ItemName'] = $order_item->SKU."-".$order_item->product_name."-".$order_item->description;
                $items_array[0]['Qty'] = -$rma_items[0]->quantity;
                $selling_price = $order_item->actual_price / (1 + $order_item->tax_rate );
                $items_array[0]['ActPrice'] = $selling_price;
                $items_array[0]['GSTP'] = $order_item->tax_rate * 100;
                $selling_price = $order_item->actual_price / (1 + $order_item->tax_rate );
                $items_array[0]['SellingPrice'] = $order_item->actual_price;
                $discount_percentage = 0;
                if($order_item->actual_price != null || $order_item->actual_price != 0){
                    $discount_percentage = ( $item_discount / $order_item->actual_price) * 100;
                }
                $items_array[0]['DiscPer'] = $discount_percentage;
                $barcode_discount = ($selling_price * $rma_items[0]->quantity) * ( $discount_percentage / 100);
                $items_array[0]['BarcodeDiscount'] = -$barcode_discount;
                $retail = ($selling_price * $rma_items[0]->quantity) - $barcode_discount;
                $items_array[0]['Retail'] = $retail;
                $gst = $retail * $order_item->tax_rate;
                $items_array[0]['GST'] = $gst;
                $items_array[0]['GrossSales'] =  -$retail;
                $items_array[0]['DiscAmount'] = 0;
                $items_array[0]['NetSale'] = - ($retail + $gst);
                $items_array[0]['Stock'] = $curent_stock;
                $items_array[0]['PctCode'] = ( $product->hs_code == null ? "" : $product->hs_code);
                $items_array[0]['FBRDesc'] =( $product->hs_code == null ? "" : $product->hs_code);

                $grand_total = $grand_total + $order_item->sub_total + $order_item->tax;
                // $grand_total = $grand_total + $order_grand_total;
                $discount =  $discount +  $discount_per_item;
                $shipping_charges =  $shipping_charges +  $shipping_per_item;

            }

            if($original_shipment->tracking_number == $order->shipping_fee_charged){
                $shipping_charges  =  $order->shipping_fee;
            }else{
                $shipping_charges  =  0;
            }

            \Log::info($grand_total);
            \Log::info($shipping_charges);
            \Log::info($discount);
            // $order_grand_total =  ($grand_total + $shipping_charges) - $discount;
            $order_grand_total =  $grand_total + $shipping_charges;

            \Log::info($order_grand_total);

            $payment_method_ids = [49,50,51,54]; // cambridge payment method ids
            $account_id = "";
            $terminal = "";
            $account_name = "";

            if ( in_array($order->seller_payment_method_id,$payment_method_ids) ) {
                $account_id = config('enum.gtech_erp_payment_gateway_account_ids')[$order->seller_payment_method_id];
                $account_name = config('enum.gtech_erp_payment_gateway_account_name')[$order->seller_payment_method_id];
                $terminal = $account_id;
            }else{
                $account_id = config('enum.gtech_erp_courier_account_ids')[$shipment->courier_id];
                $account_name = config('enum.gtech_erp_courier_account_name')[$shipment->courier_id];
                $terminal = "";

            }

            $data['BillNo']  =  $fulfilment_order->reference_id."-RTRN";
            $data['BillDate']  =  Carbon::parse($shipment->updated_at)->format('m-d-Y');
            $data['SalesMan']  =  "0000";
            $data['Terminal']  =  $terminal; //shall be blank in case of cod else account id
            $data['CardNo']  = "";
            $data['OnAccount']  = "0090028";
            $data['Description']  = "LIAB Ag Coupon";
            $data['CashAmount']  = 0;
            $data['NetReceivableAmount']  = -($order_grand_total + $order->fbr_tax);
            $data['DRCRAmount']  =  0; // will be blank as per latest document
            $data['OnAccountAmount']  = -($order_grand_total +  $order->fbr_tax);  //This will be same as "NetReceivableAmount"
            $data['ReturnAmount']  =  0;
            $data['BalanceAmount']  =  0;
            $data['Customer']  =  $order->customer_name;
            $data['ContactNo']  =  $order->customer_number;
            $data['Email']  =  $order->customer_email;
            $data['Address']  =  ( $order->shipping_address == null ? "" : $order->shipping_address);
            $data['Location']  = $sellerLocation->seller_reference_id;
            $data['DiscountType']  =  "";
            $data['SlipTime']  =  Carbon::parse($shipment->updated_at)->format('m-d-Y H:i:s');
            $data['WebOrderNo']  =  $fulfilment_order->reference_id;
            $data['FreightAmount']  =  0;
            $data['FBRINVNo']  =  ( $shipment->fbr_return_invoice_no == null ? "" : $shipment->fbr_return_invoice_no);
            $data['UserId']  =  "system";
            $data['BuyerCNic']  = 0;
            $data['ReturnNo']  =  "";
            if($shipment->tracking_number == $order->shipping_fee_charged){
                $data['AddOtherCharges']  =  $order->shipping_fee;
            }else{
                $data['AddOtherCharges']  =  0;
            }
            $data['SalesType']  =  "Return";
            $data['CNNumber']  = $shipment->tracking_number;
            $data['CreditMemono']  =  ( $order->is_voucher_order_wise == 0 && $order->voucher != null ?  $order->voucher : "");
            $data['CreditmemoAmount']  =  ( $order->is_voucher_order_wise == 0 && $order->voucher != null ?  $discount : 0);
            $data['CreditMemoOrderNo']  = ( $order->is_voucher_order_wise == 0 && $order->voucher != null ?  $order->voucher : "");
            $data['ServiceFee']  =  $order->fbr_tax;

          
            
            $data['items_detail'] = $items_array;

            $dump_gtech_pos_online = [];
            $dump_gtech_pos_online['dump_gtech_pos_onlines'] = $data;
            $dump_gtech_pos_online['order'] = $order;
            $dump_gtech_pos_online['fulfillment_order'] = $fulfilment_order;
            $dump_gtech_pos_online['shipment'] = $shipment;
            $dump_gtech_pos_online['sales_type'] = 2;
            DumpGtechPosOnline::organizeAndCreateRecord($dump_gtech_pos_online);

            
            \Log::info($data);
            \Log::info(json_encode($data));
            // \Log::info(json_encode($data));

            $curl = curl_init();

            curl_setopt_array($curl, array(
            CURLOPT_URL => $api_url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 0,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => 'POST',
            CURLOPT_POSTFIELDS => json_encode($data),
            CURLOPT_HTTPHEADER => array(
                'Content-Type: application/json'
            ),
            ));

            $response = curl_exec($curl);
            \Log::critical("The Response of POS Online insert is ::");
            \Log::critical($response);

            curl_close($curl);

            \Log::info("GTECH posOnlineForRMA : response is");
            \Log::info("GTECH posOnlineForRMA : ".$response);
            $expected_response = "Bill # ".$fulfilment_order->reference_id."-RTRN Saved Successfully ";
            if($response == $expected_response){
                \Log::info("GTECH posOnlineForRMA :  Data Saved Successfully");
                $shipment_history = ShipmentHistory::where('shipment_id',$shipment->id)->where('status','Return Received')->where('erp_sync',0)->first();
                if($shipment_history){
                    $shipment_history->erp_sync = 1;
                    $shipment_history->erp_sync_at = Carbon::now()->toDateTimeString();
                    $shipment_history->save();
                }
                FulfillmentOrderGtechUpdates::updateOrCreate(['fulfilment_order_id' =>$fulfilment_order->id], ['order_marked_as_returned' =>  true ]);
                DumpGtechPosOnline::whereSellerId($shipment->seller_id)->where('bill_no',$fulfilment_order->reference_id."-RTRN")->update(['is_synced' => true]);

            }else{
                \Log::info("GTECH posOnlineForRMA : Failed ". $fulfilment_order->id);
                \Log::info("GTECH posOnlineForRMA : ".$response);


                $subject = "GTECH <".$fulfilment_order->reference_id."> - <posOnlineForRMA> - Sync Failed";
                Mail::raw($response.' | GTECH posOnlineForRMA  (Return - RMA)  : Failed', function ($m)  use($others,$unity,$subject) {
                    $m->to($others)
                        ->bcc($unity)
                        ->subject($subject);
                });
                
            }
        } catch (\Throwable $th) {
            \Log::info("GTECH posOnlineForRMA : Try Catch  ". $fulfilment_order->id);
            \Log::info($th);

            $subject = "GTECH <".$fulfilment_order->reference_id."> - <posOnlineForRMA> - Sync Failed - Catch Block";
            Mail::raw($th.' | GTECH posOnline  (Return - RMA)  : Catch', function ($m)  use($unity,$subject) {
                $m->to($unity)
                ->subject($subject);
            });
            
        }
        
    }

    public function SetOrderStatusCancelled($fulfilment_order)
    {
       
        try{
            $others = $this->cambridge_pocs;
            $unity = $this->unity_pocs;

            \Log::info("SetOrderStatusCancelled updated Gtech Class :: Started");
            $curl = curl_init();

            $api_url = $this->url.'/api/post/OrderStatusUpdate?api='.$this->api_key.'&OrderNo='.$fulfilment_order->reference_id;

            curl_setopt_array($curl, array(
            CURLOPT_URL => $api_url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 0,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => 'POST',
            ));
            curl_setopt($curl, CURLOPT_HTTPHEADER, array('Content-Length: 0'));


            $response = curl_exec($curl);

            curl_close($curl);
            \Log::info("SetOrderStatusCancelled : response is");
            \Log::info("SetOrderStatusCancelled : ".$response);

            $text_response_message = $response;
            if (is_object(json_decode($response))) 
            { 
                $decoded_response = json_decode($response,true);
                $text_response_message = isset($decoded_response['error']) ? $decoded_response['error'] : (isset($decoded_response['response']) ? $decoded_response['response'] : null);
            }

            if($text_response_message == "Order Status Updated Successfully"){

                \Log::info("SetOrderStatusCancelled : Status check worked");
                // $shipment->erp_sync = 1;
                // $shipment->save();
                // FulfillmentOrderGtechUpdates::updateOrCreate(['fulfilment_order_id' =>$fulfilment_order->id], ['order_marked_as_delivered' =>  true ]);
            }else{
                \Log::info("SetOrderStatusCancelled : Status check failed");
                \Log::info("SetOrderStatusCancelled : Fulfilment Order".$fulfilment_order->id);
                $others = $this->cambridge_pocs;
                $unity = $this->unity_pocs;
                $subject = "GTECH <".$fulfilment_order->reference_id."> - <SetOrderStatusCancelled> - Sync Failed";
                Mail::raw($response.' | GTECH SetOrderStatusCancelled : Failed', function ($m)  use($unity,$subject) {
                    $m->to($unity)
                        ->subject($subject);
                });
            }
        } catch (\Throwable $th) {
            \Log::info("GTECH SetOrderStatusCancelled : Failed ". $fulfilment_order->id);
            \Log::info($th);    
            $subject = "GTECH <".$fulfilment_order->reference_id."> - <SetOrderStatusCancelled> - Sync Failed - Catch Block";
            Mail::raw($th.' | GTECH SetOrderStatusCancelled : Catch', function ($m)  use($unity,$subject) {
                $m->to($unity)
                    ->subject($subject);
            });
        }
    }

    public function createStockOrder($seller_location)
    {
        try{
            Log::info("createStockOrder Gtech Class :: Started");
            Log::info($seller_location);
            $date_now =  Carbon::now()->format('m/d/Y');
            $last_day =  Carbon::now()->subDays(1)->format('m/d/Y');
    
            $curl = curl_init();
            $api_url = $this->url.'/api/post/ReceiveFinish?api='.$this->api_key.'&DateFrom='.$last_day.'&DateTo='.$date_now.'&Location='.$seller_location->seller_reference_id;

            Log::info($api_url);
            curl_setopt_array($curl, array(
            CURLOPT_URL => $api_url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 0,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => 'GET',
            ));
    
            $response = curl_exec($curl);
            \Log::info("createStockOrder : response is");
            \Log::info("createStockOrder : ".$response);

            $response_array = [];
            if($response){
                curl_close($curl);
                Log::info($response);
                $response = json_decode($response,true);
                foreach($response as $item){
                    $temp_arr = [];
                    $temp_arr['RecDate'] =  $item['RecDate'];
                    $temp_arr['Location'] =  $item['Location'];
                    $temp_arr['Barcode'] =  $item['Barcode'];
                    $temp_arr['DesignNo'] =  $item['DesignNo'];
                    $temp_arr['ItemName'] =  $item['ItemName'];
                    $temp_arr['Qty'] =  $item['Qty'];
                    $response_array[$item['RecNo']][] =  $temp_arr;
                }
    
                foreach($response_array as $key => $value){
                    $line_items = [];
                    
                    foreach($value as $v){
                        $temp_arr = [];
                        $temp_arr['barcode'] = $v['Barcode'];
                        $temp_arr['sku'] = $v['DesignNo'];
                        $temp_arr['qty'] = $v['Qty'];
                        $line_items[] = $temp_arr;
                        $location = $v['Location'];
                        $arival_date = $v['RecDate'];
                    }
                    $date = strtotime(trim($arival_date));
                    $stock_order_created_date = date('Y-m-d', $date);

                    $params = ['stock_order_id' => $key, 'location_to' => $location,'arrival_date' => $stock_order_created_date,
                    'stock_order_created_date' => $stock_order_created_date  , 'line_items' => $line_items , 'receive_so' => true ,'seller_id' => $seller_location->seller_id ];   
                    Log::info($params);
                    event(new CreateStockOrderThroughUnityForGtechEvent($params));
                             
                }                           
    
            }else{
                \Log::info("createStockOrder : None Found");
            }

        } catch (\Throwable $th) {
            \Log::info("GTECH createStockOrder : Failed ");
            \Log::info($th);     
        }
        
    }

    public function receiveFinishUpdate($stock_reference_id,$location_reference)
    {
       
        try{
            $unity = $this->unity_pocs;

            \Log::info("ReceiveFinishUpdate updated Gtech Class :: Started");

            $curl = curl_init();

            $api_url = $this->url.'/api/post/ReceiveFinishUpdate?api='.$this->api_key.'&RecNo='.$stock_reference_id.'&Location='.$location_reference;

            curl_setopt_array($curl, array(
            CURLOPT_URL => $api_url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 0,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => 'POST',
            ));
            curl_setopt($curl, CURLOPT_HTTPHEADER, array('Content-Length: 0'));


            $response = curl_exec($curl);

            curl_close($curl);
            \Log::info("ReceiveFinishUpdate : response is");
            \Log::info("ReceiveFinishUpdate : ".$response);
            $response_json_encoded = $response;
            $response = json_decode($response,true);

            if(isset($response['response']) && $response['response'] == "Receive Finish Fetch Status Updated Successfully"){
                \Log::info("ReceiveFinishUpdate : Status check worked");
            }else{
                \Log::info("ReceiveFinishUpdate : Status check failed");
                \Log::info("ReceiveFinishUpdate : ".$stock_reference_id);
                
                $unity = $this->unity_pocs;
                $subject = "GTECH <".$stock_reference_id."> - <ReceiveFinishUpdate> - Sync Failed";
                Mail::raw($response_json_encoded.' | GTECH ReceiveFinishUpdate : Failed', function ($m)  use($unity,$subject) {
                    $m->to($unity)
                        ->subject($subject);
                });
            }
        } catch (\Throwable $th) {
            \Log::info("GTECH ReceiveFinishUpdate : Failed ". $stock_reference_id);
            \Log::info($th);    
            $subject = "GTECH <".$stock_reference_id."> - <ReceiveFinishUpdate> - Sync Failed - Catch Block";
            Mail::raw($th.' | GTECH ReceiveFinishUpdate : Catch', function ($m)  use($unity,$subject) {
                $m->to($unity)
                    ->subject($subject);
            });
        }
    }

}