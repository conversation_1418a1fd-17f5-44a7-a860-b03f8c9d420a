<?php

namespace App\Helpers;

use App\Events\CreateStockOrderThroughUnityForGtechEvent;
use App\Models\{SellerLocation, FulfillmentOrder, FulfillmentOrderItem, SellerPaymentMethod, OrderComments, OrderItem, DumpErpSale};
use App\Models\DumpDynamicGrnPosting;
use App\Models\DumpDynamicTransferPicking;
use App\Models\ReturnReceive;
use App\Models\ReturnReceiveItem;
use App\Service\Integration\ERP\ERPForZubaidas;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;

class ZubaidasERP
{
    protected $erp_service;
    protected $grand_total;

    public function __construct(ERPForZubaidas $erp_service)
    {
        $this->erp_service = $erp_service;
    }

    public function createReturnOrder(array $params): void
    {
        $order_id = $params['internal_params']['order_id'];
        $shipment = $params['shipment'];
        $seller_id = $params['internal_params']['seller_id'];
        $order = $shipment->order;
        $rma = $params['rma'];

        $fulfillment_order = FulfillmentOrder::with(['items.order_item', 'location'])
            ->whereShipmentId($shipment->id)
            ->firstOrFail();

        if ($return_receive_id = ReturnReceive::whereOrderId($shipment->order_id)->value('id')) {
            $sale_line_items = [];
            $return_receive_items = ReturnReceiveItem::where('return_receive_id', $return_receive_id)->get();
            $discount_item_wise = $order->discount / count($return_receive_items);

            foreach ($return_receive_items as $item) {
                $order_item = OrderItem::whereOrderId($shipment->order_id)->where('product_id', $item->product_id)->first();

                $sale_line_items[] = [
                    "sku" => $order_item->SKU,
                    "item" => $order_item->barcode,
                    "itemname" => $order_item->product_name,
                    "quantity" => $item->quantity,
                    "price" => $order_item->unit_price,
                    "discountamount" => $discount_item_wise,
                    "amount" => $order_item->sub_total,
                ];

                $this->grand_total = $this->grand_total + $order_item->sub_total;
            }

        } else if ($rma) {
            $rma_items = $params['rma_items'];
            $sale_line_items = $this->processLineItems($rma_items, $order);
        } else {
            $sale_line_items = $this->processLineItems($fulfillment_order->items, $order);
        }

        $sales_payload = $this->buildSalesPayload(
            $fulfillment_order,
            $sale_line_items,
        );

        $dump_erp_sale = DumpErpSale::create([
            'seller_id' => $seller_id,
            'order_id' => $order_id,
            'fulfillment_id' => $fulfillment_order->id,
            'shipment_id' => $shipment->id,
            'type' => 'return',
            'payload' => json_encode($sales_payload),
        ]);

        $this->handleSaleOrderApiResponse($order_id, $dump_erp_sale, $sales_payload, $fulfillment_order);
    }

    private function buildSalesPayload($fulfillment_order, $sale_line_items): array
    {
        return [
            "docno" => "",
            "docdate" => Carbon::now()->format('Y-m-d'),
            "orderid" => $fulfillment_order->reference_id,
            "grosstotal" => $this->grand_total,
            'items' => $sale_line_items,
        ];
    }

    private function handleSaleOrderApiResponse($order_id, $dump_erp_sale, $sales_payload, $fulfillment_order): void
    {
        $response = $this->erp_service->postReturnOrder($sales_payload);
        $status = $response['status_code'];
        $key = "ERP Return Sales Sync Process";
        // Send email in case of error
        $recipients = explode(',', env('ZUBAIDAS_NOTIFICATION_EMAIL', ''));
        $unity_email = env('UNITY_NOTIFICATION_EMAIL', '');
        $subject = "Zubaidas - Order ID: {$order_id} - Return Order - Sync Failed - Status Code: {$status}";
        $send_email = false;

        if ($status === 200) {
            if (isset($response['data']['success']) && $response['data']['success'] === true) {
                $dump_erp_sale->markAsSynced(json_encode($response['data']), $status);
                $document_number = $response['data']['documentNumber'] ?? '';
                $message = "The document number recieved from ERP : {$document_number}";
                $status = "Success";
                OrderComment::add(compact('order_id', 'message', 'key', 'status'));

            } else {
                $dump_erp_sale->markSyncFailed(json_encode($response['data']), $status);
                $message = $response['data']['message'] ?? $response['data'];
                $status = "Failed";
                OrderComment::add(compact('order_id', 'message', 'key', 'status'));
                $body = "An error occurred during the ERP Sync Process.\n\nResponse Data: " . json_encode($response['data'] ?? $response['data']);
                $send_email = true;
            }
        } else {
            $dump_erp_sale->markSyncFailed(json_encode($response['message']), $status);
            $message = $response['message'] ?? 'Something went wrong';
            $status = "Failed";
            OrderComment::add(compact('order_id', 'message', 'key', 'status'));
            $body = "An error occurred during the ERP Sync Process.\n\nResponse Message: " . json_encode($response['message']);
            $send_email = true;
        }

        if ($send_email) {
            Mail::raw($body, function ($m) use ($recipients, $unity_email, $subject) {
                $m->to($recipients)
                    ->bcc($unity_email)
                    ->subject($subject);
            });
        }
    }

    private function processLineItems($fulfillment_order_items, $order): array
    {
        $line_items = [];
        $discount_item_wise = $order->discount / count($fulfillment_order_items);

        foreach ($fulfillment_order_items as $fulfillment_order_item) {
            $order_item = $fulfillment_order_item->order_item;

            $line_items[] = [
                "sku" => $order_item->SKU,
                "item" => $order_item->barcode,
                "itemname" => $order_item->product_name,
                "quantity" => $fulfillment_order_item->quantity,
                "price" => $order_item->unit_price,
                "discountamount" => $discount_item_wise,
                "amount" => $order_item->sub_total,
            ];

            $this->grand_total = $this->grand_total + $order_item->sub_total;
        }

        return $line_items;
    }

    public function getStockOrders($location, $seller_id)
    {
        $params = [
            "plocationid" => $location,
        ];

        $response = $this->erp_service->getStockTransferOrders($params);
        $status = $response['status_code'];
        $send_email = false;

        if ($status === 200) {

            foreach ($response['data'] as $stock_order) {

                $line_items = [];

                foreach ($stock_order['items'] as $v) {

                    $temp_arr = [];
                    $temp_arr['barcode'] = $v['item'];
                    $temp_arr['sku'] = $v['item'];
                    $temp_arr['qty'] = $v['Quantity'];

                    $line_items[] = $temp_arr;
                }

                $date = strtotime($stock_order['docdate']);
                $stock_order_created_date = date('Y-m-d', $date);

                $params = [
                    'stock_order_id' => $stock_order['docno'],
                    'location_to' => $stock_order['LocationToID'],
                    'location_from' => $stock_order['LocationFromID'],
                    'arrival_date' => $stock_order_created_date,
                    'stock_order_created_date' => $stock_order_created_date,
                    'line_items' => $line_items,
                    'seller_id' => $seller_id
                ];

                Log::info(' Zubaidas getStockOrders creation params  | '.json_encode($params));

                event(new CreateStockOrderThroughUnityForGtechEvent($params));
            }

        } else {

            $body = "An error occurred during the ERP Sync Process.\n\nResponse Message: " . json_encode($response['message']);
            $send_email = true;
        }

        /// Send Email in case of error
        if ($send_email) {

            $recipients = explode(',', env('ZUBAIDAS_NOTIFICATION_EMAIL', ''));
            $unity_email = env('UNITY_NOTIFICATION_EMAIL', '');
            $subject = "Zubaidas - Location ID: {$location} - Get Stock Transfer Order - Sync Failed - Status Code: {$status}";

            Mail::raw($body, function ($m) use ($recipients, $unity_email, $subject) {
                $m->to($recipients)
                    ->bcc($unity_email)
                    ->subject($subject);
            });
        }

    }

    public function getGRNPostedOrders($stock_order,$items,$grn_posting_receipt,$seller_id)
    {
        $data = [];
        $message = "Zubaidas ERP getGRNPostedOrders | ";
        $recipients = explode(',', env('ZUBAIDAS_NOTIFICATION_EMAIL', ''));
        $unity_email = env('UNITY_NOTIFICATION_EMAIL', '');
        $stock_order_id = $stock_order->id;
        $stock_reference_id = $stock_order->reference_id;

        try {

            // Log::info($stock_reference_id);
            // Log::info(json_encode($items));

            $destination_location_reference = SellerLocation::where('id', $stock_order->location_id)->value('seller_reference_id');


            $params = [
                'docno' => $stock_reference_id,
                'docdate' => $stock_order->arrival_date,
                'cocod' => $destination_location_reference,
                'items' => $items['items_arr'],
            ];
            Log::info(' Zubaidas getGRNPostedOrders params  | '.json_encode($params));

            // $data = [];
            // $data['stock_order_id'] = $stock_order_id;
            // $data['stock_reference_id'] = $stock_reference_id;
            // $data['seller_id'] = $seller_id;
            // $data['items'] = $items['items_arr_temp'];

            // if(count($grn_posting_receipt) > 0) {

            //     $seller_location = SellerLocation::find($stock_order->location_id);
            //     $data['destination_location'] = $seller_location->seller_reference_id;
            //     $data['origin_location'] = $stock_order->supplier_name;
            //     DumpDynamicGrnPosting::organizeAndCreateRecord($data);

            // } else {

            //     $destination_seller_location = SellerLocation::find($stock_order->destination_location_id);
            //     $origin_seller_location = SellerLocation::find($stock_order->seller_location_id);
            //     $data['destination_location'] = $destination_seller_location->seller_reference_id;
            //     $data['origin_location'] = $origin_seller_location->seller_reference_id;
            //     DumpDynamicTransferPicking::organizeAndCreateRecord($data);
            // }

            $response = $this->erp_service->postGRNPostedOrders($params);
            
            Log::info(' Zubaidas getGRNPostedOrders response  | '.json_encode($response));

            // if(count($grn_posting_receipt) > 0) {

            //     foreach($items['items_arr_temp'] as $item) {
            //         DumpDynamicGrnPosting::whereSellerId($seller_id)->where('stock_order_id',$stock_order_id)->where('sku',$item['sku'])->where('barcode',$item['Barcode'])->update(['response_from_api' => $response]);
            //     }

            // } else {
            //     DumpDynamicTransferPicking::whereSellerId($seller_id)->where('stock_order_id',$stock_order_id)->update(['response_from_api' => $response]);
            // }


            if ($response['success']) {

                // if(count($grn_posting_receipt) > 0) {

                //     foreach($items as $item) {
                //         DumpDynamicGrnPosting::whereSellerId($seller_id)->where('stock_order_id',$stock_order_id)->where('sku',$item['sku'])->where('barcode',$item['Barcode'])->update(['is_synced_dynamics' => true]);
                //     }

                // } else {
                //     DumpDynamicTransferPicking::whereSellerId($seller_id)->where('stock_order_id',$stock_order_id)->update(['is_synced_dynamics' => true]);
                // }


                /// Updating GRN Posting on Erp in Unity
                if(count($grn_posting_receipt) > 0) {

                    foreach($grn_posting_receipt as $item) {

                        $item->grn_receipt = 1;
                        $item->grn_receipt_date = Carbon::now()->toDateTimeString();
                        $item->save();
                    }
                }

                $stock_order = array("stock_order_id" => $stock_reference_id , "line_items" => $items);

            } else {

                $message .= "failure | ".json_encode($response);

                Mail::raw($message, function ($m) use ($recipients,$unity_email,$stock_reference_id) {
                    $m->to($recipients)
                        ->bcc($unity_email)
                        ->subject('Stock Reference :: '.$stock_reference_id.' - Zubaidas ERP getGRNPostedOrders : failure');
                });
            }
        
        } catch (\Throwable $th) {

            Log::info("Zubaidas getGRNPostedOrders : Catch ", $th->getTrace());  

            $message .= " Catch | ". $th->getMessage();

            Mail::raw($message, function ($m) use ($recipients,$unity_email,$stock_reference_id) {
                    $m->to($recipients)
                    ->bcc($unity_email)
                    ->subject('Stock Reference :: '.$stock_reference_id.' - Zubaidas ERP getGRNPostedOrders : failure');
            });   
        }
    }

}
