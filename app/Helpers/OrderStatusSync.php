<?php 

namespace App\Helpers;

use Exception;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;

class OrderStatusSync
{
    public static function update($data)
    {
        ///// Creating Order Comment in khaadi //////

        try {

            $curl = curl_init();

            $post_data = array("statusHistory" => array("comment" => $data['message'] , "status" => $data['status']) );

            curl_setopt_array($curl, array(
            CURLOPT_URL => env('KHAADI_URL')."/rest/V1/orders/".$data['entity_id']."/comments",
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => "",
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 60,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => "POST",
            CURLOPT_POSTFIELDS => json_encode($post_data),
            CURLOPT_HTTPHEADER => array(
                "Authorization: Bearer ".env('KHAADI_TOKEN'),
                "Content-Type: application/json"
            ),
            ));

            $response = curl_exec($curl);

            $httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
            curl_close($curl);

            if ($httpCode == '200' && $response == 'true') {
                Log::info('Khaadi Order Status Api Request Success');
                return array('error' => 0, 'message' => '<b>'.$data['status'].'</b> Status has been successfully synced with storefront'); 
            } if ($httpCode == '200' && json_encode($response) == '"\ntrue"') {
                Log::info('Khaadi Order Status Api Request Success');
                return array('error' => 0, 'message' => '<b>'.$data['status'].'</b> Status has been successfully synced with storefront'); 
            } else {
                Mail::raw('<b>'.$data['status'].'</b> Status failed to synced with storefront  | '.$response, function ($m)  {
                    $m->to('<EMAIL>')->subject('Order Status Sync With Storefront');
                });
                return array('error' => 1, 'message' => '<b>'.$data['status'].'</b> Status failed to synced with storefront'); 
            }
            
        } catch (Exception $e) {
            Log::info('Order Status Sync With Storefront '.$e->getMessage());
            // Mail::raw($e->getMessage().' | '.json_encode($data), function ($m)  {
            //     $m->to('<EMAIL>')->subject('Order Status Sync With Storefront');
            // });
            return array('error' => 1, 'message' => '<b>'.$data['status'].'</b> Status failed to synced with storefront'); 
        }
    }
}