<?php 

namespace App\Helpers;

use App\Models\Inventory;
use App\Models\Product;
use App\Models\Courier\City;
use App\Models\ReturnReceiveItem;
use App\Models\SellerLocation;
use Exception;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use App\Models\OrderTag;
use App\Models\Tag;
use App\Models\Setting;
use App\Models\FulfillmentOrder;
use App\Models\Order;
use App\Models\OrderItem;
use App\Models\FulfillmentOrderGtechUpdates;
use GuzzleHttp\Client;
use Carbon\Carbon;
use App\Events\CreateStockOrderThroughUnityEvent;
use App\Events\PostGRNOnUnityEvent;
use App\Events\SendOrderOriginJdotEvent;
use App\Helpers\OrderComment;
use App\Models\DumpDynamicGrnPosting;
use App\Models\DumpDynamicTransferPicking;
use App\Models\GRNPostingReceipt;
use App\Models\FulfillmentOrderItem;
use App\Models\SellerPaymentMethod;
use App\Models\Shipment;
use App\Models\ShopifyApp;
use App\Models\DumpDynamicsCreateSales as ModelsDumpDynamicsCreateSales;
use App\Models\StockOrder;

class JdotERP
{
    protected $token;
    protected $jdot_users;
    protected $unity;

    public function __construct($seller_id) {
        $this->token = $this->getToken($seller_id);
        $this->jdot_users = ['<EMAIL>'];
        $this->unity = '<EMAIL>';

    }

    protected function getToken($seller_id)
    {
        try{
            $client_id = "";
            $client_secret = "";
            $resource = "";

            if($seller_id == env('JDOT_MAGENTO_SELLER_ID')){
                $client_id = env('JDOT_CLIENT_ID');
                $client_secret = env('JDOT_CLIENT_SECRET');
                $resource = env('JDOT_RESOURCE');
            }

            if($seller_id == env('JDOT_SHOPIFY_SELLER_ID')){
                $client_id = env('ALMIRAH_CLIENT_ID');
                $client_secret = env('ALMIRAH_CLIENT_SECRET');
                $resource = env('ALMIRAH_RESOURCE');
            }

            Log::info("JDOT DYNAMIC ACCESS REQUEST TOKEN DETAILS : ".$client_id."-------".$client_secret."---------".$resource);

            $curl = curl_init();

            curl_setopt_array($curl, array(
            CURLOPT_URL => env('JDOT_GENERATE_TOKEN_API_URL'),
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 0,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => 'POST',
            CURLOPT_POSTFIELDS => 'client_id='.$client_id.'&client_secret='.$client_secret.'&resource='.$resource.'&grant_type=client_credentials',
            CURLOPT_HTTPHEADER => array(
                'Content-Type: application/x-www-form-urlencoded',
                'Cookie: fpc=AqC8L_Dx92xDh2bvd5ju2sBzpZXyAgAAAK9PktsOAAAA; stsservicecookie=estsfd; x-ms-gateway-slice=estsfd'
            ),
            ));
            $response = curl_exec($curl);
            $response = json_decode($response,true);
            $httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);

            curl_close($curl);

            if ($httpCode == '200') {
                Log::info('got token');
                if(isset($response['access_token'])){
                    return array('error' => 0, 'token' => $response['access_token']); 
                }
                else{
                    Log::info("JDOT DYNAMIC ACCESS TOKEN : response failiure");
                    Log::info($response);
                    return array('error' => 1); 
                }
                
            } else {
                $message = 'Token API Response Code | '.$httpCode;
                Log::info("JDOT DYNAMIC ACCESS TOKEN : failiure");
                Log::info($response);
                return array('error' => 1); 
            }
        } catch (\Throwable $th) {
            Log::info("JDOT DYNAMIC ACCESS TOKEN : Failed ");
            Log::info($th);     
            return array('error' => 1); 
        }
        
    }

    public function generateNewToken($seller_id){
        $this->token = $this->getToken($seller_id);
    }

    public function getStockOrders($location,$seller_id)
    {
        $token = $this->token;
        $data = [];
        $message = "getStockOrders | ";
        $jdot_users = $this->jdot_users;
        $unity = $this->unity;

        if($token['error'] == 0){
            try{
                Log::info("getStockOrders :: The token was generated sucessfully");
                Log::info($token['token']);


                $curl = curl_init();
                $params = '{
                    "inventLocationId":"'.$location.'"
                }';

                curl_setopt_array($curl, array(
                  CURLOPT_URL => env('JDOT_RESOURCE').'/api/services/CfzInventorySyncServiceGroup/CfzInventorySyncService/cfzGetInventorySync',
                  CURLOPT_RETURNTRANSFER => true,
                  CURLOPT_ENCODING => '',
                  CURLOPT_MAXREDIRS => 10,
                  CURLOPT_TIMEOUT => 0,
                  CURLOPT_FOLLOWLOCATION => true,
                  CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                  CURLOPT_CUSTOMREQUEST => 'POST',
                  CURLOPT_POSTFIELDS =>$params,
                  CURLOPT_HTTPHEADER => array(
                    'Authorization: Bearer '.$token['token'],
                    'Content-Type: application/json'
                  ),
                ));

               
                
                $response = curl_exec($curl);
                Log::info($response);
                $response = json_decode($response,true);
                $httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
                Log::info($httpCode);
                Log::info($response);
                curl_close($curl);

               

                if ($httpCode == '200') {
                    Log::info('got 200');

                    if(isset($response['responseType']) && $response['responseType'] == 200){
                        if(isset($response['CfzInventorySyncHeaderContract'])){
                            foreach($response['CfzInventorySyncHeaderContract'] as $stock_order){
                                Log::info($stock_order['transferOrderId']);
                                event(new CreateStockOrderThroughUnityEvent($stock_order,$seller_id));
                            }
                        }
                    }else{
                        Log::info("JDOT DYNAMICS getStockOrders | Stock Orders were not found for Location :: ".$location);
                    }
                    
                } else {
                    $message = 'JDOT DYNAMICS getStockOrders | '.$httpCode.' | ';
                    Log::info("JDOT DYNAMICS getStockOrders : failiure");
                    Log::info($response);
                    $message .= "failiure |".json_encode($response);

                    Mail::raw($message, function ($m) use ($jdot_users,$unity,$seller_id) {
                        $m->to($jdot_users)
                            ->bcc($unity)
                            ->subject('Seller :: '.$seller_id.' - DYNAMICS getStockOrders : failiure');
                    });
                    return array('error' => 1); 
                }
                return $data;
            
            } catch (\Throwable $th) {
                Log::info("JDOT DYNAMICS getStockOrders : Catch ");
                $message .= "Catch |". $th->getMessage();

                Mail::raw($message, function ($m) use ($jdot_users,$unity,$seller_id) {
                    $m->to($jdot_users)
                        ->bcc($unity)
                        ->subject('Seller :: '.$seller_id.' - DYNAMICS getStockOrders : Catch');
                });
                return $data;
            }
        }else{
            
            Log::info("JDOT DYNAMICS getStockOrders :: The token was not generated");
            $message .= "The token was not generated";
            Mail::raw($message, function ($m) use ($jdot_users,$unity,$seller_id) {
                $m->to($jdot_users)
                    ->bcc($unity)
                    ->subject('Seller :: '.$seller_id.' - DYNAMICS getStockOrders : Catch');
            });
            return $data;
        }

        
    }

    public function getGRNPostedOrders($stock_order,$items,$grn_posting_receipt,$seller_id)
    {
        $token = $this->token;
        $data = [];
        $message = "JDOT DYNAMICS getGRNPostedOrders | ";
        $jdot_users = $this->jdot_users;
        $unity = $this->unity;
        $stock_order_id = $stock_order->id;
        $stock_reference_id = $stock_order->reference_id;

    
        if($token['error'] == 0){
            try{
                Log::info("getGRNPostedOrders :: The token was generated sucessfully");
                Log::info($token['token']);

                Log::info($stock_reference_id);
                Log::info($items);

                $curl = curl_init();
                $params = array("StockId" => $stock_reference_id , "Items" => $items);
                $params = array("PostGRN" => $params);
                $params = json_encode($params);
                Log::info($params);

                $data = [];
                $data['stock_order_id'] = $stock_order_id;
                $data['stock_reference_id'] = $stock_reference_id;
                $data['seller_id'] = $seller_id;
                $data['items'] = $items;

                if(count($grn_posting_receipt) > 0){
                    $seller_location = SellerLocation::find($stock_order->location_id);
                    $data['destination_location'] = $seller_location->seller_reference_id;
                    $data['origin_location'] = $stock_order->supplier_name;
                    DumpDynamicGrnPosting::organizeAndCreateRecord($data);
                }else{
                    $destination_seller_location = SellerLocation::find($stock_order->destination_location_id);
                    // $origin_stock_order = StockOrder::find($stock_order->stock_order_id);
                    $origin_seller_location = SellerLocation::find($stock_order->seller_location_id);
                    $data['destination_location'] = $destination_seller_location->seller_reference_id;
                    $data['origin_location'] = $origin_seller_location->seller_reference_id;
                    DumpDynamicTransferPicking::organizeAndCreateRecord($data);
                }

                curl_setopt_array($curl, array(
                  CURLOPT_URL => env('JDOT_RESOURCE').'/api/services/CfzPostGRNApiServiceGroup/PostGRNApiService/PostGRNApi',
                  CURLOPT_RETURNTRANSFER => true,
                  CURLOPT_ENCODING => '',
                  CURLOPT_MAXREDIRS => 10,
                  CURLOPT_TIMEOUT => 0,
                  CURLOPT_FOLLOWLOCATION => true,
                  CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                  CURLOPT_CUSTOMREQUEST => 'POST',
                  CURLOPT_POSTFIELDS =>$params,
                  CURLOPT_HTTPHEADER => array(
                    'Authorization: Bearer '.$token['token'],
                    'Content-Type: application/json'
                  ),
                ));

               
                Log::info("JDOT DYNAMICS getGRNPostedOrders | The response is ");
     

                $response = curl_exec($curl);
                Log::info($response);

                if(count($grn_posting_receipt) > 0){
                    foreach($items as $item){
                        DumpDynamicGrnPosting::whereSellerId($seller_id)->where('stock_order_id',$stock_order_id)->where('sku',$item['sku'])->where('barcode',$item['Barcode'])->update(['response_from_api' => $response]);
                    }
                }else{
                    DumpDynamicTransferPicking::whereSellerId($seller_id)->where('stock_order_id',$stock_order_id)->update(['response_from_api' => $response]);
                }
                
                $response = json_decode($response,true);
                $httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
                Log::info($httpCode);
                Log::info($response);
                curl_close($curl);

                $message .= $httpCode.' | ';

                if ($httpCode == '200') {
                    Log::info('got 200');
                    // $response = '{"$id":"1","IsSuccess":true,"Message":"Purchanse Order Posted Successfully."}';
                    // $response = json_decode($response,true);
                    if(isset($response['IsSuccess']) && $response['IsSuccess'] == true){
                        Log::info("Yes I am inside Sucesss");

                        if(count($grn_posting_receipt) > 0){
                            foreach($items as $item){
                                DumpDynamicGrnPosting::whereSellerId($seller_id)->where('stock_order_id',$stock_order_id)->where('sku',$item['sku'])->where('barcode',$item['Barcode'])->update(['is_synced_dynamics' => true]);
                            }
                        }else{
                            DumpDynamicTransferPicking::whereSellerId($seller_id)->where('stock_order_id',$stock_order_id)->update(['is_synced_dynamics' => true]);
                        }

                        // $grn_posting_receipt = GRNPostingReceipt::where("stock_order_id",$stock_order_id)->get();
                        if(count($grn_posting_receipt) > 0){
                            Log::info("Updating GRN Posting on Unity");
                            foreach($grn_posting_receipt as $item){
                                $item->grn_receipt = 1;
                                $item->grn_receipt_date = Carbon::now()->toDateTimeString();
                                $item->save();
                            }
                        }

                        $stock_order = array("stock_order_id" => $stock_reference_id , "line_items" => $items);
                        //event(new PostGRNOnUnityEvent($stock_order));
                    }else{
                        Log::info("JDOT DYNAMICS getGRNPostedOrders | GRN was not posted for  :: ".$stock_reference_id);
                        $message .= "Is success check failiure |".json_encode($response);

                        // Mail::raw($message, function ($m) use ($stock_reference_id) {
                        //     $m->to('<EMAIL>')->subject('Stock Reference :: '.$stock_reference_id.' - DYNAMICS getGRNPostedOrders : Success check failiure');
                        // });
                    }
                    
                } else {
                    Log::info("JDOT DYNAMICS getGRNPostedOrders : failiure");
                    Log::info($response);
                    $message .= "failiure |".$response;

                    Mail::raw($message, function ($m) use ($jdot_users,$unity,$stock_reference_id) {
                        $m->to($jdot_users)
                            ->bcc($unity)
                            ->subject('Stock Reference :: '.$stock_reference_id.' - DYNAMICS getGRNPostedOrders : failiure');
                    });
                }
            
            } catch (\Throwable $th) {
                Log::info("JDOT DYNAMICS getGRNPostedOrders : Catch ");
                Log::info($th);  

                $message .= "Catch |". $th->getMessage();

                Mail::raw($message, function ($m) use ($jdot_users,$unity,$stock_reference_id) {
                        $m->to($jdot_users)
                        ->bcc($unity)
                        ->subject('Stock Reference :: '.$stock_reference_id.' - DYNAMICS getGRNPostedOrders : failiure');
                });   
            }
        }else{
            Log::info("JDOT DYNAMICS getGRNPostedOrders :: The token was not generated");
            $message .= "The token was not generated";
            Mail::raw($message, function ($m) use ($jdot_users,$unity,$stock_reference_id) {
                $m->to($jdot_users)
                ->bcc($unity)
                ->subject('Stock Reference :: '.$stock_reference_id.' - DYNAMICS getGRNPostedOrders : failiure');
            });
        }

        
    }

    public function getOrderAssignedLocations($order_array,$seller_id)
    {
        $token = $this->token;
        $data = [];
        $message = "getOrderAssignedLocations | ";

        if($token['error'] == 0){
            try{
                Log::info("getOrderAssignedLocations :: The token was generated sucessfully");
                Log::info($token['token']);

                Log::info($order_array);
                $order_array = Order::whereIn('id', $order_array)->where('status', 'Pending')->get(['id','marketplace_reference_id']);
                $order_marketplace_reference_ids = $order_array->pluck('marketplace_reference_id');
                if(count($order_marketplace_reference_ids) > 0) {

                    $curl = curl_init();
                    $params = array("OrderIds" => $order_marketplace_reference_ids);
                    $params = json_encode($params);
                    Log::info($params);

                    curl_setopt_array($curl, array(
                    CURLOPT_URL => env('JDOT_RESOURCE').'/api/services/CfzGetLocationApiServiceGroup/CfzGetLocationApiService/cfzGetLocationAPI',
                    CURLOPT_RETURNTRANSFER => true,
                    CURLOPT_ENCODING => '',
                    CURLOPT_MAXREDIRS => 10,
                    CURLOPT_TIMEOUT => 0,
                    CURLOPT_FOLLOWLOCATION => true,
                    CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                    CURLOPT_CUSTOMREQUEST => 'POST',
                    CURLOPT_POSTFIELDS =>$params,
                    CURLOPT_HTTPHEADER => array(
                        'Authorization: Bearer '.$token['token'],
                        'Content-Type: application/json'
                    ),
                    ));

                
                    Log::info("JDOT DYNAMICS getOrderAssignedLocations | The response is ");

                    $response = curl_exec($curl);
                    Log::info($response);
                    $response = json_decode($response,true);
                    $httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
                    Log::info($httpCode);
                    Log::info($response);
                    curl_close($curl);


                    if ($httpCode == '200') {
                        Log::info('got 200');
                        if(isset($response['responseType']) && $response['responseType'] == 200){
                            Log::info("Yes I am inside Sucesss");
                            if(isset($response['CfzGetLocationAPIHeaderResponseContract'])){
                                foreach($response['CfzGetLocationAPIHeaderResponseContract'] as $stock_order){
                                    Log::info($stock_order);
                                    $order_id = Order::where('seller_id',$seller_id)->where('marketplace_reference_id',$stock_order['OrderId'])->where('status', 'Pending')->pluck('id');
                                    if($order_id){
                                        $items_location_assigned = [];
                                        foreach($stock_order['CfzGetLocationAPIResponseContract'] as $item){
                                            $temp_item = OrderItem::where('order_id',$order_id)->where('SKU',$item['sku'])->first();
                                            if($temp_item){
                                                $items_location_assigned[] = $temp_item->id;
                                            }
                                        }
                                        event(new SendOrderOriginJdotEvent($stock_order['OrderId'],$stock_order['locationId'],$items_location_assigned,$seller_id));
                                    }
                                    
                                }
                            }
                        }else{
                            Log::info("JDOT DYNAMICS getOrderAssignedLocations | location was not received for the orders  :: ");
                            $message .= "Is success check failiure |".$response;

                            // Mail::raw($message, function ($m) use ($seller_id) {
                            //     $m->to('<EMAIL>')->subject('Stock Reference :: '.$seller_id.' - DYNAMICS getOrderAssignedLocations : Success check failiure');
                            // });
                        }
                        
                    } else {
                        Log::info("JDOT DYNAMICS getOrderAssignedLocations : failiure");
                        Log::info($response);
                        $message .= "failiure |".json_encode($response);

                        Mail::raw($message, function ($m) use ($seller_id) {
                            $m->to('<EMAIL>')->subject('Seller ID :: '.$seller_id.' - DYNAMICS getOrderAssignedLocations : failiure');
                        });
                        }
                }else{
                    Log::info("JDOT DYNAMICS getOrderAssignedLocations :: No Order Found");
                }

            
            } catch (\Throwable $th) {
                Log::info("JDOT DYNAMICS getOrderAssignedLocations : Catch ");
                Log::info($th);    
                $message .= "Catch |". $th->getMessage();

                Mail::raw($message, function ($m) use ($seller_id) {
                    $m->to('<EMAIL>')->subject('Seller ID :: '.$seller_id.' - DYNAMICS getOrderAssignedLocations : Catch');
                });
            }
        }else{
            Log::info("JDOT DYNAMICS getOrderAssignedLocations :: The token was not generated");
            $message .= "The token was not generated";
            Mail::raw($message, function ($m) use ($seller_id) {
                $m->to('<EMAIL>')->subject('Seller ID :: '.$seller_id.' - DYNAMICS getOrderAssignedLocations : failiure');
            });
        }
   

        
    }

public function rejectFulfilment($fufulfilment_order,$order,$location_reference,$items)
    {
        $token = $this->token;
        $data = [];

        if($token['error'] == 0){
            try{
                Log::info("rejectFulfilment :: The token was generated sucessfully");
                Log::info($token['token']);

                Log::info($order);
                Log::info($items);

                $curl = curl_init();
                $params = array("OrderId" => $order , "Location" => $location_reference , "Items" => $items);
                $params = array("RejectFulfilment" => $params);
                $params = json_encode($params);
                Log::info($params);

                curl_setopt_array($curl, array(
                  CURLOPT_URL => env('JDOT_RESOURCE').'/api/services/CfzRejectFulfilmentPlanApiServiceGroup/CfzRejectFulfilmentPlanApiService/RejectFulfilmentApi',
                  CURLOPT_RETURNTRANSFER => true,
                  CURLOPT_ENCODING => '',
                  CURLOPT_MAXREDIRS => 10,
                  CURLOPT_TIMEOUT => 0,
                  CURLOPT_FOLLOWLOCATION => true,
                  CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                  CURLOPT_CUSTOMREQUEST => 'POST',
                  CURLOPT_POSTFIELDS =>$params,
                  CURLOPT_HTTPHEADER => array(
                    'Authorization: Bearer '.$token['token'],
                    'Content-Type: application/json'
                  ),
                ));

               
                Log::info("JDOT DYNAMICS rejectFulfilment | The response is ");

                $response = curl_exec($curl);
                Log::info($response);
                $response = json_decode($response,true);
                $httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
                Log::info($httpCode);
                Log::info($response);
                curl_close($curl);


                if ($httpCode == '200') {
                    Log::info('got 200');
                    // $response = '{"$id":"1","IsSuccess":true,"Message":"Purchanse Order Posted Successfully."}';
                    // $response = json_decode($response,true);
                    if(isset($response['IsSuccess']) && $response['IsSuccess'] == true){
                        Log::info("Yes I am inside Sucesss");
                        $f_order = FulfillmentOrder::find($fufulfilment_order->id);
                        $f_order->erp_sync = config('enum.fulfillment_erp_sync_status')['REJECTED_SYNCED'];
                        $f_order->save();
                    }else{
                        Log::info("JDOT DYNAMICS rejectFulfilment | Order was not found   :: ".$order);
                    }
                    
                } else {
                    $message = 'JDOT DYNAMICS rejectFulfilment | '.$httpCode;
                    Log::info("JDOT DYNAMICS rejectFulfilment : failiure");
                    Log::info($response);
                }
            
            } catch (\Throwable $th) {
                Log::info("JDOT DYNAMICS rejectFulfilment : Catch ");
                Log::info($th);     
            }
        }else{
            Log::info("JDOT DYNAMICS rejectFulfilment :: The token was not generated");
        }

        
    }
    public function createPaymentJournal($data)
    {
        $token = $this->token;
        $message = "createPaymentJournal | ";
        $seller_id = $data['seller_id'];
        $jdot_users = $this->jdot_users;
        $unity = $this->unity;

        if($token['error'] == 0){
            try{
                Log::info("createPaymentJournal :: The token was generated sucessfully");
                Log::info($token['token']);

                Log::info($data);

                $curl = curl_init();

                $bank_account =  1;

                if($data['seller_id'] == env('JDOT_MAGENTO_SELLER_ID')){
                    $bank_account =  config('enum.jdot_magento_erp_bank_account')[$data['courier_id']];
                }

                if($data['seller_id'] == env('JDOT_SHOPIFY_SELLER_ID')){
                    $bank_account =  config('enum.jdot_shopify_erp_bank_account')[$data['courier_id']];
                }


                // $bank_account = config('enum.jdot_erp_bank_account')[$data['courier_id']];
                $params = array("invoiceId" => "invoiceId" , "consignmentNumber" => $data['cn'] , "bankAccount" => $bank_account, "settlementDate" => $data['date']);
                $params = json_encode($params);
                Log::info($params);

                curl_setopt_array($curl, array(
                  CURLOPT_URL => env('JDOT_RESOURCE').'/api/services/CFZCODSettledServiceGroup/CFZCODSettledService/createPaymentJournal',
                  CURLOPT_RETURNTRANSFER => true,
                  CURLOPT_ENCODING => '',
                  CURLOPT_MAXREDIRS => 10,
                  CURLOPT_TIMEOUT => 0,
                  CURLOPT_FOLLOWLOCATION => true,
                  CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                  CURLOPT_CUSTOMREQUEST => 'POST',
                  CURLOPT_POSTFIELDS =>$params,
                  CURLOPT_HTTPHEADER => array(
                    'Authorization: Bearer '.$token['token'],
                    'Content-Type: application/json'
                  ),
                ));

               
                Log::info("JDOT DYNAMICS createPaymentJournal | The response is ");

                $response = curl_exec($curl);
                Log::info($response);
                $response = json_decode($response,true);
                $httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
                Log::info($httpCode);
                Log::info($response);
                curl_close($curl);


                if ($httpCode == '200') {
                    Log::info('got 200');
                    if(isset($response['responseText']) && $response['responseText'] == 'Success'){
                        Log::info("JDOT DYNAMICS createPaymentJournal | Yes it was success");
                        Log::info($response['responseMessage']);
                        if(count($response['consignmentList']) > 0){
                            foreach($response['consignmentList'] as $val){
                                Log::info("The CN is ::");
                                Log::info($val);
                                $order_id = $data['cn_mapping'][$val];
                                $message = $response['responseMessage'];
                                $key = "D365 Payment Journal";
                                $status = "Success";
                                OrderComment::add(compact('order_id','message','key','status'));

                            }
                        }
                        
                    }else{
                        Log::info("JDOT DYNAMICS createPaymentJournal | CNs were not found   :: ");
                    }
                    
                } else {
                    $message = 'JDOT DYNAMICS createPaymentJournal | '.$httpCode;
                    Log::info("JDOT DYNAMICS createPaymentJournal : failiure");
                    Log::info($response);
                    $message .= "Failure |".json_encode($response);

                    Mail::raw($message, function ($m) use ($jdot_users,$unity,$seller_id) {
                        $m->to($jdot_users)
                            ->bcc($unity)
                            ->subject('Seller ID :: '.$seller_id.' - DYNAMICS createPaymentJournal : Failure');
                    }); 
                }
            
            } catch (\Throwable $th) {
                Log::info("JDOT DYNAMICS createPaymentJournal : Catch ");
                Log::info($th);    
                $message .= "Catch |". $th->getMessage();


                Mail::raw($message, function ($m) use ($jdot_users,$unity,$seller_id) {
                        $m->to($jdot_users)
                            ->bcc($unity)
                            ->subject('Seller ID :: '.$seller_id.' - DYNAMICS createPaymentJournal : Catch');
                }); 
            }
        }else{
            Log::info("JDOT DYNAMICS createPaymentJournal :: The token was not generated");
            $message .= "The token was not generated";
            Mail::raw($message, function ($m) use ($jdot_users,$unity,$seller_id) {
                $m->to($jdot_users)
                    ->bcc($unity)
                    ->subject('Seller ID :: '.$seller_id.' - DYNAMICS getOrderAssignedLocations : failiure');
            });
        }

        
    }

    public function returnSalesApi($data)
    {
        $token = $this->token;
        $message = "returnSalesApi | ";
        $cn_number = $data['cn'];
        $jdot_users = $this->jdot_users;
        $unity = $this->unity;

        if($token['error'] == 0){
            try{
                Log::info("returnSalesApi :: The token was generated sucessfully");
                Log::info($token['token']);

                Log::info($data);
                
                $curl = curl_init();
                $params = array("ConsignmentNumber" => $data['cn'] , "Location" => $data['location'] , "FBRInvoiceID" => $data['fbr_return_invoice_no'] );
                $params = array("ReturnSale" => $params);
                $params = json_encode($params);
                Log::info($params);

                curl_setopt_array($curl, array(
                  CURLOPT_URL => env('JDOT_RESOURCE').'/api/services/CfzReturnSaleApiServiceGroup/CfzReturnSaleApiService/ReturnSalesApi',
                  CURLOPT_RETURNTRANSFER => true,
                  CURLOPT_ENCODING => '',
                  CURLOPT_MAXREDIRS => 10,
                  CURLOPT_TIMEOUT => 0,
                  CURLOPT_FOLLOWLOCATION => true,
                  CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                  CURLOPT_CUSTOMREQUEST => 'POST',
                  CURLOPT_POSTFIELDS =>$params,
                  CURLOPT_HTTPHEADER => array(
                    'Authorization: Bearer '.$token['token'],
                    'Content-Type: application/json'
                  ),
                ));

               
                Log::info("JDOT DYNAMICS returnSalesApi | The response is ");
                sleep(3);
                $response = curl_exec($curl);
                Log::info($response);
                $response = json_decode($response,true);
                $httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
                Log::info($httpCode);
                Log::info($response);
                curl_close($curl);


                if ($httpCode == '200') {
                    Log::info('got 200');
                    if(isset($response['IsSuccess']) && $response['IsSuccess'] == true){
                        Log::info("JDOT DYNAMICS returnSalesApi | Success");
                        if(isset($response['Message'])){
                            Log::info($response['Message']);
                            $order_id = $data['order_id'];
                            $message = $response['Message'];
                            $key = "D365 Return Sale";
                            $status = "Success";
                            OrderComment::add(compact('order_id','message','key','status'));
                        }
                    }else{
                        Log::info("JDOT DYNAMICS returnSalesApi | Failure");
                        if(isset($response['Message'])){
                            Log::info($response['Message']);
                        }
                    }
                    
                } else {
                    $message = 'JDOT DYNAMICS returnSalesApi | '.$httpCode;
                    Log::info("JDOT DYNAMICS returnSalesApi : failiure");
                    Log::info($response);
                    $message .= "Failure |".json_encode($response);

                    Mail::raw($message, function ($m) use ($jdot_users,$unity,$cn_number) {
                        $m->to($jdot_users)
                            ->bcc($unity)
                            ->subject('CN No :: '.$cn_number.' - DYNAMICS returnSalesApi : Failure');
                    }); 
                }
            
            } catch (\Throwable $th) {
                Log::info("JDOT DYNAMICS returnSalesApi : Catch ");
                Log::info($th);   
                $message .= "Catch |". $th->getMessage();

                Mail::raw($message, function ($m) use ($jdot_users,$unity,$cn_number) {
                    $m->to($jdot_users)
                        ->bcc($unity)
                        ->subject('CN No :: '.$cn_number.' - DYNAMICS returnSalesApi : Catch');
                });   
            }
        }else{
            Log::info("JDOT DYNAMICS returnSalesApi :: The token was not generated");
            $message .= "The token was not generated";

            Mail::raw($message, function ($m) use ($jdot_users,$unity,$cn_number) {
                $m->to($jdot_users)
                    ->bcc($unity)
                    ->subject('CN No :: '.$cn_number.' - DYNAMICS returnSalesApi : failiure');
            });
        }

        
    }

    public function createSalesOrderServiceForRetrurn($params, $return_receive_id)
    {
        $token = $this->token;
        $message = "createSalesOrderServiceForRetrurn | ";
        $data = [];
        $shipment = $params['shipment'];
        $params_unchangable = $params;
        $jdot_users = $this->jdot_users;
        $unity = $this->unity;

        if($token['error'] == 0){
            try{
                $fulfillment_order = FulfillmentOrder::whereShipmentId($shipment->id)->first();
                Log::info("fulfillment_order is ");
                Log::info($fulfillment_order);
                $fulfilment_order_items = FulfillmentOrderItem::where('fulfillment_order_id',$fulfillment_order->id)->get();
                $item_arr = [];


                if($return_receive_id){
                    $return_receive_items = ReturnReceiveItem::where('return_receive_id', $return_receive_id)->get();
                    foreach($return_receive_items as $item){

                        if ($item->reason != 'Received') {
                            continue;
                        }

                        $temp_arr = [];
                        $order_item = OrderItem::whereOrderId($shipment->order_id)->where('product_id', $item->product_id)->first();
                        $temp_arr['Barcode'] = $order_item->barcode;
                        $temp_arr['Sku'] = $order_item->SKU;
                        $temp_arr['Quantity'] = $item->quantity;
                        $item_arr[] = $temp_arr;
                    }
                } else if(count($fulfilment_order_items) > 1){
                    foreach($fulfilment_order_items as $fulfilment_order_item){
                        $temp_arr = [];
                        $order_item = OrderItem::find($fulfilment_order_item->order_item_id);
                        $temp_arr['Barcode'] = $order_item->barcode;
                        $temp_arr['Sku'] = $order_item->SKU;
                        $temp_arr['Quantity'] = $order_item->quantity;
                        // $temp_arr['UnitPrice'] = $unit_price;
                        // $temp_arr['NetAmount'] = ($order_item->actual_price * $order_item->quantity) - $order_item->discount;
                        $item_arr[] = $temp_arr;
                    }
                }else{
                    $temp_arr = [];
                    $order_item = OrderItem::find($fulfilment_order_items[0]->order_item_id);
                    $temp_arr['Barcode'] = $order_item->barcode;
                    $temp_arr['Sku'] = $order_item->SKU;
                    $temp_arr['Quantity'] = $order_item->quantity;
                    // $temp_arr['UnitPrice'] = $unit_price;
                    // $temp_arr['NetAmount'] = ($order_item->actual_price * $order_item->quantity) - $order_item->discount;
                    $item_arr[] = $temp_arr;
                }
                $customer_id =  1;

                \Log::info($shipment->order->seller_payment_method_id);
                if($shipment->seller_id == env('JDOT_MAGENTO_SELLER_ID')){
                    if($shipment->order->seller_payment_method_id){
                        if($shipment->order->seller_payment_method_id == config('enum.jdot_cod_payment_method_id')){
                            $customer_id =  config('enum.jdot_magento_erp_courier_id')[$shipment->courier_id];
                        }else{
                            $customer_id = config('enum.jdot_pament_method_erp_account_id')[$shipment->order->seller_payment_method_id];
                        }
                    }
                }

                if($shipment->seller_id == env('JDOT_SHOPIFY_SELLER_ID')){
                    if($shipment->order->seller_payment_method_id){
                        if($shipment->order->seller_payment_method_id == config('enum.almirah_cod_payment_method_id')){
                            $customer_id =  config('enum.jdot_shopify_erp_courier_id')[$shipment->courier_id];
                        }else{
                            $customer_id = config('enum.almirah_pament_method_erp_account_id')[$shipment->order->seller_payment_method_id];
                        }
                    }
                }

                $seller_payment_method = SellerPaymentMethod::find($shipment->order->seller_payment_method_id);
                $machine_name = "";
                if($seller_payment_method){
                    $machine_name = $seller_payment_method->machine_name;
                }
                

                Log::info("Jdot ERP :: The seller ID is ::".env('JDOT_MAGENTO_SELLER_ID'));
                Log::info("Jdot ERP :: The customer ID is ::".$customer_id);
           
                $shipping_charges = 0;
                if($shipment->tracking_number == $shipment->order->shipping_fee_charged){
                    $shipping_charges =  (string) $shipment->order->shipping_fee;
                }

                if($fulfillment_order->id == $shipment->order->shipping_fee_charged){
                    $shipping_charges =  (string) $shipment->order->shipping_fee;
                }
                
                $sellerLocation = null;
                if ($shipment->return_received_at_location) {
                    $sellerLocation = SellerLocation::find($shipment->return_received_at_location);
                } else {
                    $sellerLocation = SellerLocation::find($shipment->seller_location_id);
                }


                $api_params = [
                    'OrderId' => $shipment->order->marketplace_reference_id,
                    'OrderType' => "Return Order",
                    'PaymentMethod' => $machine_name,
                    'locationId' => $sellerLocation->seller_reference_id,
                    'ShippingCharges' => $shipping_charges,
                    'CustomerName' => $shipment->order->customer_name,
                    'CustomerAddress' => $shipment->order->shipping_address,
                    'City' => $shipment->order->destination_city,
                    'Country' => $shipment->order->country,
                    'CustomerContactNumber' => $shipment->order->customer_number,
                    'CustomerEmail' => $shipment->order->customer_email,
                    'InvoiceAccount' => $customer_id,
                    'LogisticsPartner' => $shipment->courier->name,
                    'ConsignmentNumber' => $shipment->tracking_number,
                    'FBRInvoiceId' => "FBR-1",

                    'Items' => $item_arr,
                ];



                Log::info("createSalesOrderServiceForRetrurn :: The token was generated sucessfully");
                Log::info($token['token']);
                Log::info("createSalesOrderServiceForRetrurn :: The params are ::");
                $params = array("createSalesOrderHeaderContract" => $api_params);

                Log::info($params);

                $dump_dynamics_create_sales = [];
                $dump_dynamics_create_sales['dump_sales'] = $api_params;
                $dump_dynamics_create_sales['order'] = $shipment->order;
                $dump_dynamics_create_sales['fulfillment_order'] = $fulfillment_order;
                $dump_dynamics_create_sales['shipment'] = $shipment;
                $dump_dynamics_create_sales['sales_type'] = 1;

                ModelsDumpDynamicsCreateSales::organizeAndCreateRecord($dump_dynamics_create_sales);

                $curl = curl_init();
                
                Log::info($params);

                curl_setopt_array($curl, array(
                  CURLOPT_URL => env('JDOT_RESOURCE').'/api/services/cfzCreateOrderInvServiceGroup/CFZCreateSalesOrderService/createSalesOrder',
                  CURLOPT_RETURNTRANSFER => true,
                  CURLOPT_ENCODING => '',
                  CURLOPT_MAXREDIRS => 10,
                  CURLOPT_CONNECTTIMEOUT => 10,   // Max time to establish connection (in seconds)
                  CURLOPT_TIMEOUT => 60,          // Max time to wait for full response (in seconds)
                  CURLOPT_FOLLOWLOCATION => true,
                  CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                  CURLOPT_CUSTOMREQUEST => 'POST',
                  CURLOPT_POSTFIELDS =>json_encode($params),
                  CURLOPT_HTTPHEADER => array(
                    'Authorization: Bearer '.$token['token'],
                    'Content-Type: application/json'
                  ),
                ));

               
                Log::info("JDOT DYNAMICS createSalesOrderServiceForRetrurn | The response is ");

                $response = curl_exec($curl);

                ModelsDumpDynamicsCreateSales::whereSellerId($shipment->seller_id)->where('fulfillment_id',$fulfillment_order->id)->where('order_type','Return Order')->update(['response_from_api' => $response]);

            
                Log::info($response);
                $response = json_decode($response,true);
                $httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
                Log::info($httpCode);
                Log::info($response);
                curl_close($curl);

                if ($httpCode == '200') {
                    Log::info('got 200');

                    if(isset($response['IsSuccess']) && $response['IsSuccess'] == true){
                        Log::info("JDOT DYNAMICS createSalesOrderServiceForRetrurn | Success");
                        if(isset($response['responseMessage'])){
                            Log::info($response['responseMessage']);
                            ModelsDumpDynamicsCreateSales::whereSellerId($shipment->seller_id)->where('fulfillment_id',$fulfillment_order->id)->update(['is_synced_dynamics' => true]);

                            $order_id = $shipment->order->id;
                            $message = $response['responseMessage']." - The Sales Id is :: ".$response['SalesId'];
                            $key = "D365 Return Sale";
                            $status = "Success";
                            OrderComment::add(compact('order_id','message','key','status'));
                            $shipment_writable = Shipment::where('id', $shipment->id)->first();
                            $shipment_writable->erp_sync = 1;
                            $shipment_writable->save();
                        }
                    }else{
                        Log::info("JDOT DYNAMICS createSalesOrderServiceForRetrurn | Failure");
                        if(isset($response['responseMessage'])){
                            Log::info($response['responseMessage']);
                            $message .= "failiure |".$response['responseMessage'];

                            $seller_id = $shipment->seller_id;

                            $subject = "D365 <".$seller_id."> <".$shipment->order->marketplace_reference_id."> - <createSalesOrderServiceForRetrurn> - Sync Failed - IsSuccess is False";
                            $unity = '<EMAIL>';
                            // Mail::raw($message, function ($m) use ($jdot_users,$unity,$subject) {
                            //     $m->to($jdot_users)
                            //         ->bcc($unity)
                            //         ->subject($subject);
                            // });
                        }else{
                            $message .= "failiure |".$response;
                            $seller_id = $shipment->seller_id;

                            $subject = "D365 <".$seller_id."> <".$shipment->order->marketplace_reference_id."> - <createSalesOrderServiceForRetrurn> - Sync Failed - Failure Response";
                            $unity = '<EMAIL>';
                            Mail::raw($message, function ($m) use ($jdot_users,$unity,$subject) {
                                $m->to($jdot_users)
                                    ->bcc($unity)
                                    ->subject($subject);
                            });
                        }
                    }

                } else {
                    $message = 'JDOT DYNAMICS createSalesOrderServiceForRetrurn | '.$httpCode;
                    Log::info("JDOT DYNAMICS createSalesOrderServiceForRetrurn : failiure");
                    Log::info($response);


                    
                    $seller_id = $shipment->seller_id;


                    if($httpCode == "401"){
                        Log::info("JDOT DYNAMICS createSalesOrderServiceForRetrurn : Generating New Token ");
                        $this->generateNewToken($seller_id);
                        Log::info("JDOT DYNAMICS createSalesOrderServiceForRetrurn : New Token is ::");
                        Log::info($this->token);
                        $this->createSalesOrderServiceForRetrurn($params_unchangable, $return_receive_id);
                        
                    }else{
                        $subject = "D365 <".$seller_id."> <".$shipment->order->marketplace_reference_id."> - <createSalesOrderServiceForRetrurn> - Sync Failed - Response Code (".$httpCode.")";
                        $unity = '<EMAIL>';
                        Mail::raw($message, function ($m)  use($jdot_users,$unity,$subject) {
                            $m->to($jdot_users)
                                ->bcc($unity)
                                ->subject($subject);
                        });
                    }


     

                    // return array('error' => 1, 'retry' => 1); 
                }
            
            } catch (\Throwable $th) {
                Log::info("JDOT DYNAMICS createSalesOrderServiceForRetrurn : Catch ");
                Log::info($th);    
                
                $message .= "Catch |". $th->getMessage();
                $seller_id = $shipment->seller_id;

                $subject = "D365 <".$seller_id."> <".$shipment->order->marketplace_reference_id."> - <createSalesOrderServiceForRetrurn> - Sync Failed - Catch Block";
                $unity = '<EMAIL>';
                Mail::raw($message, function ($m)  use($jdot_users,$unity,$subject) {
                    $m->to($jdot_users)
                        ->bcc($unity)
                        ->subject($subject);
                });
 
                // return array('error' => 1, 'retry' => 1); 
            }
        }else{
            Log::info("JDOT DYNAMICS createSalesOrderServiceForRetrurn :: The token was not generated");
            // return array('error' => 1, 'retry' => 1); 
        }

        
    }

    public function createSalesOrderServiceForRMA($params,$return_receive_id,$original_shipment)
    {
        $token = $this->token;
        $message = "createSalesOrderServiceForRMA | ";
        $data = [];
        $shipment = $params['shipment'];
        $params_unchangable = $params;
        $jdot_users = $this->jdot_users;
        $unity = $this->unity;

        if($token['error'] == 0){
            try{
                
                $fulfillment_order = FulfillmentOrder::whereShipmentId($original_shipment->id)->first();
                Log::info("fulfillment_order is ");
                Log::info($fulfillment_order);
                $fulfilment_order_items = FulfillmentOrderItem::where('fulfillment_order_id',$fulfillment_order->id)->get();
                $item_arr = [];
                $shopify = new ShopifyApp();

                if($return_receive_id){
                    $return_receive_items = ReturnReceiveItem::where('return_receive_id', $return_receive_id)->get();
                    if(count($return_receive_items) > 1){

                        foreach($return_receive_items as $item){

                            if ($item->reason != 'Received') {
                                continue;
                            }

                            $temp_arr = [];
                            $order_item = OrderItem::whereOrderId($shipment->order_id)->where('product_id', $item->product_id)->first();
                            
                            $unit_price = $order_item->actual_price;
                            if($shipment->seller_id == env('JDOT_SHOPIFY_SELLER_ID')){
                                $unit_price = $order_item->cost;

                                // $product = Product::find($item->product_id);
                                // $result = $shopify->getCompareAtPrice($product->marketplace_variant_id, $shipment->seller_id);

                                // if($result['error'] == 0){
                                //     $unit_price = $result['compare_at_price'];
                                // }else{
                                //     Log::critical("Error response during compare at price");
                                //     Log::critical($result);
                                // }

                                $charges_code = $this->getChargesCodeForAlmirah($shopify,$order_item,$shipment->seller_id);
                                $charges_amount = $order_item->actual_price * $item->quantity;

                            }

                            if($shipment->seller_id == env('JDOT_MAGENTO_SELLER_ID')){
                                $charges_code = $this->getChargesCodeForJdot($order_item);
                                $charges_amount = $order_item->sub_total;

                            }

                            $temp_arr['Barcode'] = $order_item->barcode;
                            $temp_arr['Sku'] = $order_item->SKU;
                            $temp_arr['Quantity'] = - ( $item->quantity );
                            $temp_arr['UnitPrice'] = 0;
                            $temp_arr['NetAmount'] = 0;
                            $temp_arr['ChargesCode'] = $charges_code;
                            $temp_arr['ChargesAmount'] = - ( $charges_amount );
                            $item_arr[] = $temp_arr;
                        }
                    } else{
                        $temp_arr = [];
                        $order_item = OrderItem::whereOrderId($shipment->order_id)->where('product_id', $return_receive_items[0]->product_id)->first();

                        $unit_price = $order_item->actual_price;
                        if($shipment->seller_id == env('JDOT_SHOPIFY_SELLER_ID')){
                            $unit_price = $order_item->cost;
                            // $product = Product::find($order_item->product_id);
                            // $result = $shopify->getCompareAtPrice($product->marketplace_variant_id, $shipment->seller_id);

                            // if($result['error'] == 0){
                            //     $unit_price = $result['compare_at_price'];
                            // }else{
                            //     Log::critical("Error response during compare at price");
                            //     Log::critical($result);
                            // }

                            $charges_code = $this->getChargesCodeForAlmirah($shopify,$order_item,$shipment->seller_id);
                            $charges_amount = $order_item->actual_price * $return_receive_items[0]->quantity;

                        }

                        if($shipment->seller_id == env('JDOT_MAGENTO_SELLER_ID')){
                            $charges_code = $this->getChargesCodeForJdot($order_item);
                            $charges_amount = $order_item->sub_total;

                        }

                        $temp_arr['Barcode'] = $order_item->barcode;
                        $temp_arr['Sku'] = $order_item->SKU;
                        $temp_arr['Quantity'] = - ( $return_receive_items[0]->quantity );
                        $temp_arr['UnitPrice'] = 0;
                        $temp_arr['NetAmount'] = 0;
                        $temp_arr['ChargesCode'] = $charges_code;
                        $temp_arr['ChargesAmount'] = - ($charges_amount);

                        $item_arr[] = $temp_arr;
                    }
                }
                $customer_id =  1;

                \Log::info($shipment->order->seller_payment_method_id);
                if($shipment->seller_id == env('JDOT_MAGENTO_SELLER_ID')){
                    //commenting the following code on request of haroon where in case of RMA account id shall be hardcoded
                    // if($shipment->order->seller_payment_method_id){
                    //     if($shipment->order->seller_payment_method_id == config('enum.jdot_cod_payment_method_id')){
                    //         $customer_id =  config('enum.jdot_magento_erp_courier_id')[$shipment->courier_id];
                    //     }else{
                    //         $customer_id = config('enum.jdot_pament_method_erp_account_id')[$shipment->order->seller_payment_method_id];
                    //     }
                    // }
                    $customer_id =  config('enum.jdot_rma_customer_account_id');
                }

                if($shipment->seller_id == env('JDOT_SHOPIFY_SELLER_ID')){
                    //commenting the following code on request of haroon where in case of RMA account id shall be hardcoded
                    // if($shipment->order->seller_payment_method_id){
                    //     if($shipment->order->seller_payment_method_id == config('enum.almirah_cod_payment_method_id')){
                    //         $customer_id =  config('enum.jdot_shopify_erp_courier_id')[$shipment->courier_id];
                    //     }else{
                    //         $customer_id = config('enum.almirah_pament_method_erp_account_id')[$shipment->order->seller_payment_method_id];
                    //     }
                    // }
                    $customer_id =  config('enum.almirah_rma_customer_account_id');
                }

                $seller_payment_method = SellerPaymentMethod::find($shipment->order->seller_payment_method_id);
                $machine_name = "";
                if($seller_payment_method){
                    $machine_name = $seller_payment_method->machine_name;
                }
                

                Log::info("Jdot ERP :: The seller ID is ::".env('JDOT_MAGENTO_SELLER_ID'));
                Log::info("Jdot ERP :: The customer ID is ::".$customer_id);
           
                $shipping_charges = 0;
                if($original_shipment->tracking_number == $shipment->order->shipping_fee_charged){
                    $shipping_charges  =  $shipment->order->shipping_fee;
                }

                $sellerLocation = SellerLocation::find($shipment->return_received_at_location);

                $api_params = [
                    'OrderId' => $shipment->order->marketplace_reference_id,
                    'OrderType' => "Partial Return",
                    'PaymentMethod' => $machine_name,
                    'locationId' => $sellerLocation->seller_reference_id,
                    'ShippingCharges' => 0, // shipping charges as 0 because as per discussion with shoaib in case of RMA customer bears the charges
                    'CustomerName' => $shipment->order->customer_name,
                    'CustomerAddress' => $shipment->order->shipping_address,
                    'City' => $shipment->order->destination_city,
                    'Country' => $shipment->order->country,
                    'CustomerContactNumber' => $shipment->order->customer_number,
                    'CustomerEmail' => $shipment->order->customer_email,
                    'InvoiceAccount' => $customer_id,
                    'LogisticsPartner' => $shipment->courier->name,
                    'ConsignmentNumber' => $shipment->tracking_number,
                    'FBRInvoiceId' => "FBR-1",

                    'Items' => $item_arr,
                ];



                Log::info("createSalesOrderServiceForRMA :: The token was generated sucessfully");
                Log::info($token['token']);
                Log::info("createSalesOrderServiceForRMA :: The params are ::");
                $params = array("createSalesOrderHeaderContract" => $api_params);

                Log::info($params);

                $dump_dynamics_create_sales = [];
                $dump_dynamics_create_sales['dump_sales'] = $api_params;
                $dump_dynamics_create_sales['order'] = $shipment->order;
                $dump_dynamics_create_sales['fulfillment_order'] = $fulfillment_order;
                $dump_dynamics_create_sales['shipment'] = $shipment;
                $dump_dynamics_create_sales['sales_type'] = 1;

                ModelsDumpDynamicsCreateSales::organizeAndCreateRecord($dump_dynamics_create_sales);

                $curl = curl_init();
                
                Log::info($params);

                curl_setopt_array($curl, array(
                  CURLOPT_URL => env('JDOT_RESOURCE').'/api/services/cfzCreateOrderInvServiceGroup/CFZCreateSalesOrderService/createSalesOrder',
                  CURLOPT_RETURNTRANSFER => true,
                  CURLOPT_ENCODING => '',
                  CURLOPT_MAXREDIRS => 10,
                  CURLOPT_TIMEOUT => 0,
                  CURLOPT_FOLLOWLOCATION => true,
                  CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                  CURLOPT_CUSTOMREQUEST => 'POST',
                  CURLOPT_POSTFIELDS =>json_encode($params),
                  CURLOPT_HTTPHEADER => array(
                    'Authorization: Bearer '.$token['token'],
                    'Content-Type: application/json'
                  ),
                ));

               
                Log::info("JDOT DYNAMICS createSalesOrderService | The response is ");

                $response = curl_exec($curl);
                Log::info($response);

                ModelsDumpDynamicsCreateSales::whereSellerId($shipment->seller_id)->where('fulfillment_id',$fulfillment_order->id)->where('order_type','Partial Return')->update(['response_from_api' => $response]);

                $response = json_decode($response,true);
                $httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
                Log::info($httpCode);
                Log::info($response);
                curl_close($curl);

                if ($httpCode == '200') {
                    Log::info('got 200');

                    if(isset($response['IsSuccess']) && $response['IsSuccess'] == true){
                        Log::info("JDOT DYNAMICS returnSalesApi | Success");
                        if(isset($response['responseMessage'])){
                            Log::info($response['responseMessage']);
                            ModelsDumpDynamicsCreateSales::whereSellerId($shipment->seller_id)->where('fulfillment_id',$fulfillment_order->id)->update(['is_synced_dynamics' => true]);

                            $order_id = $shipment->order->id;
                            $message = $response['responseMessage']." - The Sales Id is :: ".$response['SalesId'];
                            $key = "D365 Partial Return Sale";
                            $status = "Success";
                            OrderComment::add(compact('order_id','message','key','status'));
                            $shipment_writable = Shipment::where('id', $shipment->id)->first();
                            $shipment_writable->erp_sync = 1;
                            $shipment_writable->save();
                        }
                    }else{
                        Log::info("JDOT DYNAMICS createSalesOrderServiceForRMA | Failure");
                        if(isset($response['responseMessage'])){
                            Log::info($response['responseMessage']);
                            $message .= "failiure |".$response['responseMessage'];

                            
                            $seller_id = $shipment->seller_id;

                            $subject = "D365 <".$seller_id."> <".$shipment->order->marketplace_reference_id."> - <createSalesOrderServiceForRMA> - Sync Failed - IsSuccess is False";
                            $unity = '<EMAIL>';
                            Mail::raw($message, function ($m)  use($jdot_users,$unity,$subject) {
                                $m->to($jdot_users)
                                    ->bcc($unity)
                                    ->subject($subject);
                            });
                        }else{
                            $message .= "failiure |".$response;
                            $seller_id = $shipment->seller_id;

                            $subject = "D365 <".$seller_id."> <".$shipment->order->marketplace_reference_id."> - <createSalesOrderServiceForRMA> - Sync Failed - Failure Response";
                            $unity = '<EMAIL>';
                            Mail::raw($message, function ($m)  use($jdot_users,$unity,$subject) {
                                $m->to($jdot_users)
                                    ->bcc($unity)
                                    ->subject($subject);
                            });
                        }
                    }

                } else {
                    $message = 'JDOT DYNAMICS createSalesOrderServiceForRMA | '.$httpCode;
                    Log::info("JDOT DYNAMICS createSalesOrderServiceForRMA : failiure");
                    Log::info($response);
                   
                    $seller_id = $shipment->seller_id;
                   
                    if($httpCode == "401"){
                        Log::info("JDOT DYNAMICS createSalesOrderServiceForRMA : Generating New Token ");
                        $this->generateNewToken($seller_id);
                        Log::info("JDOT DYNAMICS createSalesOrderServiceForRMA : New Token is ::");
                        Log::info($this->token);
                        $this->createSalesOrderServiceForRMA($params_unchangable,$return_receive_id,$original_shipment);
                        
                    }else{
                        $subject = "D365 <".$seller_id."> <".$shipment->order->marketplace_reference_id."> - <createSalesOrderServiceForRetrurn> - Sync Failed - Response Code (".$httpCode.")";
                        $unity = '<EMAIL>';
                        Mail::raw($message, function ($m)  use($jdot_users,$unity,$subject) {
                            $m->to($jdot_users)
                                ->bcc($unity)
                                ->subject($subject);
                        });
                    }

                    // return array('error' => 1, 'retry' => 1); 
                }
            
            } catch (\Throwable $th) {
                Log::info("JDOT DYNAMICS createSalesOrderServiceForRMA : Catch ");
                Log::info($th);     

              
                $message .= "Catch |". $th->getMessage();

                $seller_id = $shipment->seller_id;
                $subject = "D365 <".$seller_id."> <".$shipment->order->marketplace_reference_id."> - <createSalesOrderServiceForRMA> - Sync Failed - Catch Block";
                $unity = '<EMAIL>';
                Mail::raw($message, function ($m)  use($jdot_users,$unity,$subject) {
                    $m->to($jdot_users)
                        ->bcc($unity)
                        ->subject($subject);
                });
 

                // return array('error' => 1, 'retry' => 1); 
            }
        }else{
            Log::info("JDOT DYNAMICS createSalesOrderServiceForRMA :: The token was not generated");
            // return array('error' => 1, 'retry' => 1); 
        }

        
    }

    private function getChargesCodeForAlmirah($shopify,$order_item,$seller_id)
    {
        $charges_code = "";
        $product = Product::find($order_item->product_id);
        $result = $shopify->getProductMetafields($product->marketplace_product_id, $seller_id);
        if($result['error'] == 0){
            $metafields = $result['metafields'];
            if(count($metafields) > 0){
                foreach($metafields as $metafield){
                    Log::info($metafield);
                    if(isset($metafield['key']) && $metafield['key'] == 'charge_code' ){
                        $charges_code = $metafield['value'];
                    }
                }
            }
        }else{
            Log::critical("Error response during product metafields fetching");
            Log::critical($result);
        }

        return $charges_code;
    }
    
    private function getChargesCodeForJdot($order_item)
    {
        $product = Product::find($order_item->product_id);
        $category = $product ? $product->category : null;

        $category_mapping = [
            "C&C Men"               => "MENS",
            "C&C Women"             => "WOMENS",
            "Default"               => "N/A",
            "Fragrances"            => "PERFUME",
            "Grooms"                => "MENS",
            "Innerwear"             => "MENS",
            "Jewelry"               => "WOMENS",
            "Kids Boys"             => "MENS",
            "Kids Girls"            => "WOMENS",
            "Ladies Bags"           => "WOMENS",
            "Ladies Footwear"       => "WOMENS",
            "Ladies Kurti"          => "WOMENS",
            "Ladies Stole"          => "WOMENS",
            "Ladies Trouser"        => "WOMENS",
            "Makeup"                => "COSMETICS",
            "Men Caps"              => "MENS",
            "Men Scarf"             => "MENS",
            "Men Sweaters"          => "MENS",
            "Men Waistcoat"         => "MENS",
            "Mens Footwear"         => "MENS",
            "Mens & Kids Bottom"    => "MENS",
            "Mens Kameez Shalwar"   => "MENS",
            "Mens Kurta"            => "MENS",
            "Mens Unstitched"       => "MENS",
            "Migration_Default"     => "N/A",
            "Migration_Dresses"     => "N/A",
            "Miscellaneous"         => "N/A",
            "Shawl"                 => "MENS",
            "Teen Boys"             => "MENS",
            "Teen Girls"            => "WOMENS",
            "Teens Sweater"         => "WOMENS",
            "Women"                 => "WOMENS",
            "Women Stitched"        => "WOMENS",
            "Women Unstitched"      => "WOMENS",
        ];

        $charges_code = $category_mapping[$category] ?? 'N/A';
        return $charges_code;
    }
    
    

}