<?php 

namespace App\Helpers;

use App\Models\BundledEmail;
use App\Models\Order;
use App\Models\Shipment;
use Illuminate\Support\Facades\Log;

class BundledEmailFill
{
    public static function add(array $data)
    {
        $seller_id = null;
        // dd($data);
        if($data['order_id'] == null){
            $seller_id = Shipment::whereId($data['shipment_id'])->value('seller_id');
        } else{
            $seller_id = Order::whereId($data['order_id'])->value('seller_id');
        }
        $entry = new BundledEmail();
        $entry->seller_id = $seller_id;
        $entry->key = $data['key'];
        $entry->order_id = $data['order_id'];
        $entry->shipment_id = $data['shipment_id'];
        $entry->marketplace_reference_id = $data['marketplace_reference_id'];
        $entry->tracking_number = $data['tracking_number'];
        $entry->details = $data['details'];
        $entry->save();
    }
}