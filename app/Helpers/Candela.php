<?php 

namespace App\Helpers;

use App\Models\Product;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use App\Models\Order;
use App\Models\OrderItem;
use App\Models\SellerLocation;
use Carbon\Carbon;
use App\Models\ShopifyApp;

class Candela
{
    protected $api_key;
    protected $url;
    protected $unity_pocs;
    protected $api_id;

    public function __construct($api_key, $api_id ,$url) {
        $this->api_key = $api_key;
        $this->api_id = $api_id;
        $this->url = $url;
        $this->unity_pocs = '<EMAIL>';
    }



    public function postOrder($fulfiment_order,$event)
    {
        try {
            $curl = curl_init();
            $api_url = $this->url.'/api/orders/PostOrder';
            \Log::info($api_url);
            $order = Order::find($fulfiment_order->order_id);
            $unity = $this->unity_pocs;
            $discount_per_item = $order->discount / count($fulfiment_order->items);
            $items_array = [];
            $shopify = new ShopifyApp();
            $courier_name =  config('enum.candela_erp_courier_name')[$event->courier_id];

            if(count($fulfiment_order->items) > 1){
                $count = 0;
                foreach($fulfiment_order->items as $item){
                    $order_item = OrderItem::find($item->order_item_id);
                    $product = Product::find($order_item->product_id);
                    $result = $shopify->getCompareAtPrice($product->marketplace_variant_id, $order->seller_id);

                    if($result['error'] == 0){
                        $unit_price = $result['compare_at_price'];
                    }else{
                        Log::info("Error response during compare at price :: ".$fulfiment_order->reference_id);
                        Log::info($result);
                        $unit_price = $order_item->actual_price;
                    }

                    $compare_at_price_discount = 0;
                    if($unit_price > $order_item->actual_price){
                        $compare_at_price_discount = $unit_price - $order_item->actual_price;
                    }

                    $items_array[$count]['ProductCode'] = $order_item->barcode;
                    $items_array[$count]['Qty'] = - ($order_item->quantity);
                    $items_array[$count]['DiscountPerc'] =  ( ($compare_at_price_discount + $discount_per_item) / $unit_price) * 100;
                    $items_array[$count]['ItemAmount'] =  $unit_price;
                    $items_array[$count]['ItemTotal'] =  $unit_price * $order_item->quantity;
                }
                
            }else{
                $order_item = OrderItem::find($fulfiment_order->items[0]->order_item_id);
                $product = Product::find($order_item->product_id);
                $result = $shopify->getCompareAtPrice($product->marketplace_variant_id, $order->seller_id);

                if($result['error'] == 0){
                    $unit_price = $result['compare_at_price'];
                }else{
                    Log::info("Error response during compare at price :: ".$fulfiment_order->reference_id);
                    Log::info($result);
                    $unit_price = $order_item->actual_price;
                }

                $compare_at_price_discount = 0;
                if($unit_price > $order_item->actual_price){
                    $compare_at_price_discount = $unit_price - $order_item->actual_price;
                }

                $items_array[0]['ProductCode'] = $order_item->barcode;
                $items_array[0]['Qty'] = - ($order_item->quantity);
                $items_array[0]['DiscountPerc'] = ( ($compare_at_price_discount + $discount_per_item) / $unit_price) * 100;
                $items_array[0]['ItemAmount'] =  $unit_price;
                $items_array[0]['ItemTotal'] =  $unit_price * $order_item->quantity;
            }

            $data['AppId']  = $this->api_id;
            $data['AppKey']  =  $this->api_key;
            $data['OrderId']  =  $fulfiment_order->reference_id;
            $data['shopId']  =  "2";
            $data['OrderDate']  =  $order->created_date->format('m-d-Y H:i:s');
            $data['FirstName']  =  $order->customer_name;
            $data['CustomerEmai']  =  $order->customer_email;
            $data['Address']  =  ( $order->shipping_address == null ? "" : $order->shipping_address);
            $data['City']  =  $order->destination_city;
            $data['Country']  =   $order->country;
            $data['State']  =  "";
            $data['Telephone']  =  $order->customer_number;
            $data['Status']  = "Processing";
            $data['CustomerNo']  =  "";
            $data['comments']  =  "";
            $data['CourierCompany']  =  $courier_name;
            $data['CourierNumber']  =  $event->tracking_number;
            $data['ReedemedPoints']  =  "";
            $data['ProvisionalPoints']  =  "";
            $data['Weight']  =  $order->items->sum('weight');
            $data['Locality']  =  "Local";

            \Log::info("Post Order");
            Log::info($fulfiment_order->id."======".$order->shipping_fee_charged);

            $data['ShippingCost'] = "0";

            if($fulfiment_order->id == $order->shipping_fee_charged){
                $data['ShippingCost'] =  (string) $order->shipping_fee;
            }
            $data['Products'] = $items_array;
            
            \Log::info($data);
            \Log::critical($data);


            $curl = curl_init();

            curl_setopt_array($curl, array(
                CURLOPT_URL => 'http://***************:96/api/orders/PostOrder',
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_ENCODING => '',
                CURLOPT_MAXREDIRS => 10,
                CURLOPT_TIMEOUT => 0,
                CURLOPT_FOLLOWLOCATION => true,
                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                CURLOPT_CUSTOMREQUEST => 'POST',
                CURLOPT_POSTFIELDS => json_encode($data),
                CURLOPT_HTTPHEADER => array(
                  'Content-Type: application/json'
                ),
              ));


            $response = curl_exec($curl);
            \Log::critical("The Response of Candela postOrder is ::");
            \Log::critical(json_encode($response));
            curl_close($curl);

            $response = json_decode($response,true);


            if(isset($response['code']) && $response['code'] == "11" && isset($response['msg']) && $response['msg'] == "Saved Successfully"){          
            
                \Log::info("Candela postOrder :  Saved Successfully");
                $order_id = $order->id;
                $message = $response['msg'] .' - '.$fulfiment_order->reference_id;
                $key = 'Candela Post Order Process';
                $status = "Success";
                OrderComment::add(compact('order_id','message','key','status'));

            }else{
                $error_message = "Review Error in Log File";
                if(isset($response['msg'])){          
                    $order_id = $order->id;
                    $message = $response['msg'];
                    $key = 'Candela Post Order Process';
                    $status = "Failed";
                    OrderComment::add(compact('order_id','message','key','status'));
                    $error_message = $response['msg'];
                }
                \Log::info("Candela postOrder : Failed ". $fulfiment_order->id);
                $subject = "Candela <".$fulfiment_order->reference_id."> - <postOrder> - Sync Failed after shipment Booked";
                Mail::raw($error_message.' | Candela postOrder after Return Received : Failed', function ($m)  use($unity,$subject) {
                    $m->to($unity)
                        ->bcc($unity)
                        ->subject($subject);
                });
            }
        } catch (\Throwable $th) {
            \Log::info("Candela postOrder : Try Catch  ". $fulfiment_order->id);
            \Log::info($th);
            $subject = "Candela <".$fulfiment_order->reference_id."> - <postOrder> - Sync Failed after shipment Booked - Catch Block";
            Mail::raw($th.' | Candela postOrder after Return Received : Catch', function ($m)  use($unity,$subject) {
                $m->to($unity)
                ->subject($subject);
            });
        }
        
    }

    public function SetOrderStatusCancelled($fulfilment_order)
    {
       
        try{
            $unity = $this->unity_pocs;

            \Log::info("SetOrderStatusCancelled updated Candela Class :: Started");
            $curl = curl_init();

            $sellerLocation = SellerLocation::find($fulfilment_order->seller_location_id);

            $api_url = $this->url.'/api/Orders/CancelOrder?AppId='.$this->api_id.'&AppKey='.$this->api_id.'&OrderId='.$fulfilment_order->reference_id.'&ShopId='.$sellerLocation->seller_reference_id;

       
            curl_setopt_array($curl, array(
                CURLOPT_URL => $api_url,
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_ENCODING => '',
                CURLOPT_MAXREDIRS => 10,
                CURLOPT_TIMEOUT => 0,
                CURLOPT_FOLLOWLOCATION => true,
                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                CURLOPT_CUSTOMREQUEST => 'POST',
              ));
  
                
            $response = curl_exec($curl);

            curl_close($curl);
            \Log::info("Candela SetOrderStatusCancelled : response is");
            \Log::critical(json_encode($response));

            if(isset($response['code']) && $response['code'] == "11" && isset($response['msg']) && $response['msg'] == "Order Cancelled Successfully"){          
                \Log::info("Candela SetOrderStatusCancelled : Status check worked");
                // $shipment->erp_sync = 1;
                // $shipment->save();
                // FulfillmentOrderGtechUpdates::updateOrCreate(['fulfilment_order_id' =>$fulfilment_order->id], ['order_marked_as_delivered' =>  true ]);
            }else{
                \Log::info("Candela SetOrderStatusCancelled : Status check failed");
                \Log::info("Candela SetOrderStatusCancelled : Fulfilment Order".$fulfilment_order->id);
                $error_message = "Review Error in Log File";

                \Log::info("Candela SetOrderStatusCancelled : Failed ". $fulfilment_order->id);
                $subject = "Candela <".$fulfilment_order->reference_id."> - <SetOrderStatusCancelled> - Sync Failed";
                Mail::raw($error_message.' | Candela SetOrderStatusCancelled : Failed', function ($m)  use($unity,$subject) {
                    $m->to($unity)
                        ->bcc($unity)
                        ->subject($subject);
                });
                
            }
        } catch (\Throwable $th) {
            \Log::info("Candela SetOrderStatusCancelled : Failed ". $fulfilment_order->id);
            \Log::info($th);    

            $subject = "Candela <".$fulfilment_order->reference_id."> - <SetOrderStatusCancelled> - Sync Failed";
            Mail::raw($th.' | Candela SetOrderStatusCancelled : Failed', function ($m)  use($unity,$subject) {
                $m->to($unity)
                    ->bcc($unity)
                    ->subject($subject);
            });
        }
    }

}