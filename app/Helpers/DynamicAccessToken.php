<?php 

namespace App\Helpers;

use App\Traits\ExceptionTrait;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;

class DynamicAccessToken
{
    use ExceptionTrait;

    public static function getToken()
    {
        try {
            $request_url = null;
            $request_data = null;
            $response_data = null;
            $message = '';
            $error = 1;

            $unity = explode(',',env('UNITY_POC'));
            $others = explode(',',env('KHAADI_POC'));

            

            Log::info('request token');
            $curl = curl_init();

            $request_url = env('D365_ACCESS_TOKEN_URL');
            $request_data = "client_id=".env('D365_CLIENT_ID')."&client_secret=".env('D365_CLIENT_SECRET')."&resource=".env('D365_URL')."&grant_type=client_credentials";

            curl_setopt_array($curl, array(
            CURLOPT_URL => $request_url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => "",
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 60,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => "GET",
            CURLOPT_POSTFIELDS => $request_data,
            CURLOPT_HTTPHEADER => array(
                "Content-Type: application/x-www-form-urlencoded",
                "Cookie: x-ms-gateway-slice=prod; stsservicecookie=ests; fpc=ApzlCiChoEZHrANWIUOEKbgTfnkuAQAAAMMlR9cOAAAA"
            ),
            ));

            $token = json_decode(curl_exec($curl),true);
            $response_data = $token;
            $httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);

            if ($httpCode == '200') {
                Log::info('got token');
                if(isset($token['access_token'])){
                    session()->put('d365_token',$token['access_token']);
                    return array('error' => 0, 'token' => $token['access_token']); 
                }
                else{
                    $message .= 'Did not get api token'; 
                }
                
            } else {
                $message .= 'Token API Response Code | '.$httpCode;
                if($others[0] != ""){
                    Mail::raw($message.' | Didnt get D365 API Token', function ($m)  use($others,$unity) {
                        $m->to($others)
                            ->bcc($unity)
                            ->subject('D365 - Get Access Token Process');
                    });
                }
                else{
                    Mail::raw($message.' | Didnt get D365 API Token', function ($m)  use($unity) {
                        $m->to($unity)
                            ->subject('D365 - Get Access Token Process');
                    });
                }
                // Mail::raw($message.' | Didnt get D365 API Token', function ($m)  {
                //     $m->to(['<EMAIL>','<EMAIL>'])
                //         ->bcc('<EMAIL>') 
                //         ->subject('D365 - Get Access Token Process');
                // });
            }

            curl_close($curl);

        } catch(\Exception $e){
            $message .= 'Get D365 Access Token Process error | '.$e->getMessage();
            $activity_id = activity()
            ->withProperties(['response' => $e->getMessage() , 'dump' => $e->getTraceAsString()])
            ->log('Get D365 Access Token Process');

            // Mail::raw($message.' | '.$e->getMessage(), function ($m)  {
            //     $m->to(['<EMAIL>','<EMAIL>'])
            //         ->bcc('<EMAIL>') 
            //         ->subject('D365 - Get Access Token Process');
            // });

            if($others[0] != ""){
                Mail::raw($message.' | '.$e->getMessage(), function ($m)  use($others,$unity) {
                    $m->to($others)
                        ->bcc($unity)
                        ->subject('D365 - Get Access Token Process');
                });
            }
            else{
                Mail::raw($message.' | '.$e->getMessage(), function ($m)  use($unity) {
                    $m->to($unity)
                        ->subject('D365 - Get Access Token Process');
                });
            }
            
            Log::info('Get D365 Access Token process error '.$e->getMessage());
        }

        if ($error == 1) {

            self::addException( 
                119,
                config('enum.exception_types')['DYNAMICSTOKENAPI'],
                $message,
                'Order',
                null,
                null,
                json_encode(['request_url' => $request_url, 'query_data' => null]),
                ( $request_data ? $request_data : null),
                ( $response_data ? json_encode($response_data) : null)
            );


            return ['error' => $error, 'message' => $message];
        }

        
    }
}