<?php

namespace App\Http\Controllers;

use App\Models\Order;
use App\Models\Shipment;
use Exception;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use App\Models\SellerLocation;
use App\Models\StockOrder;
use App\Models\StockOrderItem;
use App\Models\Product;
use App\Models\FulfillmentOrder;
use App\Models\FulfillmentOrderItem;
use App\Models\OrderItem;
use Carbon\Carbon;
use App\Events\SendCancelledStatusToCandelaEvent;
use App\Models\Setting;
use App\Events\UpdateOrderTagOnShopify;
use App\Events\UpdateOrderTagOnShopifyEvent;
use App\Models\GRNPostingReceipt;
use App\Helpers\Candela;
use App\Models\RMARequest;
use App\Models\ShipmentHistory;
use App\Events\SendCancelledStatusToGtechEvent;
use App\Events\SendReturnShipmentDetailsToCandelaEvent;

class CandelaController extends Controller
{


    public function sendReturnShipmentDetailsToCandela($seller_id){
        Log::info("Seller id ::".$seller_id);
        Log::info('Candela :: sendReturnShipmentDetailsToCandela Started');
        
        $last_hour_date =  Carbon::now()->subMinutes(15)->toDateTimeString();
        $shipments = Shipment::on('unity-read')->whereSellerId($seller_id)
                    ->where('updated_at', '>=' , $last_hour_date)
                    ->where('status', "Return Received")
                    ->get();

        if($shipments->count() > 0) {
            foreach($shipments as $shipment) {
                Log::info($shipment);
                $shipment_history = ShipmentHistory::where('shipment_id',$shipment->id)->where('status','Return Received')->where('created_at', '>=' , $last_hour_date)->where('erp_sync',0)->first();
                if($shipment_history){
                    Log::info($shipment_history);
                    if($shipment->type == "reverse"){
                        Log::info("Reverse Shipment");
                        $rma_shipment = RMARequest::where('return_shipment_id',$shipment->id)->first();  
                        // $fulfilment_order = FulfillmentOrder::where('shipment_id',$rma_shipment->shipment_id)->select('id','reference_id','seller_location_id')->first();
    
                    }else{
                        Log::info("Forward Shipment");
                        // $fulfilment_order = FulfillmentOrder::where('shipment_id',$shipment->id)->select('id','reference_id','seller_location_id')->first();
                    }
                    $sellerLocation = SellerLocation::find($shipment->return_received_at_location);
                    if($sellerLocation && $sellerLocation->is_ffc_enabled == 1){
                        event(new SendReturnShipmentDetailsToCandelaEvent($shipment));

                    }
                }
            }

        }

        Log::info('Send Candela Return Shipment Details Process Completed');
        echo "Send Candela Return Shipment Details Process Completed";
    }


    public function sendCancelledShipmentDetailsToCandela($seller_id){
        Log::info("Seller id ::".$seller_id);
        Log::info('GTech :: sendCancelledShipmentDetailsToCandela Started');
        
        $last_hour_date =  Carbon::now()->subMinutes(5)->toDateTimeString();
        $shipments = Shipment::on('unity-read')->whereSellerId($seller_id)
                    ->where('updated_at', '>=' , $last_hour_date)
                    ->where('status', "Cancelled")
                    ->get();

        if($shipments->count() > 0) {

            foreach($shipments as $shipment) {
                Log::info($shipment);
                event(new SendCancelledStatusToCandelaEvent($shipment));
            }

        }

        Log::info('Send Candela Cancelled Shipment Details Process Completed');
        echo "Send Candela Cancelled Shipment Details Process Completed";
    }



}
