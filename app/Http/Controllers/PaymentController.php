<?php

namespace App\Http\Controllers;

use App\Helpers\BundledEmailEmpty;
use Carbon\Carbon;
use Exception;
use Illuminate\Support\Facades\Log;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Storage;
use Maatwebsite\Excel\Facades\Excel;

class PaymentController extends Controller
{
    public function index()
    {
        echo "Payment Controller";
    }

    public function paymentDetailsSwichRuntime(Request $request)
    {
        Log::info("Swich runtime");
        Log::info($request->all());

        return "Request Received";
    }

   
}
