<?php

namespace App\Http\Controllers;

use App\Models\Order;
use App\Models\Shipment;
use Exception;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use App\Helpers\JdotERP;
use App\Models\SellerLocation;
use App\Models\StockOrder;
use App\Models\StockOrderItem;
use App\Models\Product;
use App\Models\FulfillmentOrder;
use App\Models\FulfillmentOrderItem;
use App\Models\OrderItem;
use Carbon\Carbon;
use App\Events\CreatePaymentJournalEvent;
use App\Models\StockTransferFulfillmentOrder;
use App\Models\SellerOrderConfirmation;
use App\Models\Setting;
use App\Events\UpdateOrderTagOnShopify;
use App\Events\UpdateOrderTagOnShopifyEvent;
use App\Models\GRNPostingReceipt;
use App\Events\SendCompleteStatusToGtechEvent;
use App\Events\SendReturnShipmentDetailsToGTECHEvent;
use App\Helpers\Gtech;
use App\Models\RMARequest;
use App\Models\ShipmentHistory;
use App\Events\SendCancelledStatusToGtechEvent;

class GTechController extends Controller
{

    public function index()
    {
       
    }

    public function sendDeliveredShipmentDetailsToGtech($seller_id){
        Log::info("Seller id ::".$seller_id);
        Log::info('GTech :: sendCambridgeDeliveredShipmentDetailsToGtech Started');
        
        $last_hour_date =  Carbon::now()->subMinutes(5)->toDateTimeString();
        $shipments = Shipment::on('unity-read')->whereSellerId($seller_id)
                    ->where('updated_at', '>=' , $last_hour_date)
                    ->where('status', "Delivered")
                    ->where('erp_sync', 0)
                    ->get();

        if($shipments->count() > 0) {

            foreach($shipments as $shipment) {
                Log::info($shipment);
                event(new SendCompleteStatusToGtechEvent($shipment));
            }

        }

        Log::info('Send GTech Delivered Shipment Details Process Completed');
        echo "Send GTech Delivered Shipment Details Process Completed";
    }

    public function sendReturnShipmentDetailsToGtech($seller_id){
        Log::info("Seller id ::".$seller_id);
        Log::info('GTech :: sendReturnShipmentDetailsToGtech Started');
        
        $last_hour_date =  Carbon::now()->subMinutes(15)->toDateTimeString();
        $shipments = Shipment::on('unity-read')->whereSellerId($seller_id)
                    ->where('updated_at', '>=' , $last_hour_date)
                    ->where('status', "Return Received")
                    ->get();

        if($shipments->count() > 0) {
            foreach($shipments as $shipment) {
                Log::info($shipment);
                $shipment_history = ShipmentHistory::where('shipment_id',$shipment->id)->where('status','Return Received')->where('created_at', '>=' , $last_hour_date)->where('erp_sync',0)->first();
                if($shipment_history){
                    Log::info($shipment_history);
                    if($shipment->type == "reverse"){
                        Log::info("Reverse Shipment");
                        $rma_shipment = RMARequest::where('return_shipment_id',$shipment->id)->first();  
                        // $fulfilment_order = FulfillmentOrder::where('shipment_id',$rma_shipment->shipment_id)->select('id','reference_id','seller_location_id')->first();
    
                    }else{
                        Log::info("Forward Shipment");
                        // $fulfilment_order = FulfillmentOrder::where('shipment_id',$shipment->id)->select('id','reference_id','seller_location_id')->first();
                    }
                    $sellerLocation = SellerLocation::find($shipment->return_received_at_location);
                    if($sellerLocation && $sellerLocation->is_ffc_enabled == 1){
                        event(new SendReturnShipmentDetailsToGTECHEvent($shipment));
                    }
                }
            }

        }

        Log::info('Send GTech Return Shipment Details Process Completed');
        echo "Send GTech Return Shipment Details Process Completed";
    }

    public function sendReturnShipmentDetailsToGtechInBulk(){
        Log::info('GTech :: sendReturnShipmentDetailsToGtechInBulk Started');
        
       $shipments = [2639359,2639679,2639689,2639694,2639719,2639765,2639960,2639976,2640048,2640063,2641135,2641425,2641453,2641841,2642119,2642275,2642510,2642541,2643021,2644299,2644343,2644424,2644549,2644737,2644813,2644817,2645345,2645418,2645600,2645637,2645727,2645831,2646677,2647057,2647099,2647102,2647105,2647114,2647225,2647231,2647459,2647647,2647789,2648395,2648399,2648424,2648425,2648437,2649039,2649568,2649612,2650048,2650051,2650063,2650068,2650069,2650626,2650643,2650691,2650701,2650821,2650875,2650908,2650993,2651019,2651034,2651049,2651132,2651149,2651195,2651232,2651323,2651327,2651349,2651376,2651618,2652256,2652360,2653029,2653133,2653159,2653344,2653424,2653692,2654071,2654117,2654179,2654378,2655066,2655084,2655099,2655358,2655371,2656000,2656204,2656208,2656373,2656501,2656731,2656759,2656843,2656900,2656905,2657002,2657106,2657594,2657669,2657840,2657969,2658263,2658593,2658740,2659439,2659474,2660388,2660432,2661015,2661790,2662152,2662854,2662876,2662972,2663119,2664322,2667222,2667381,2668973,2669956,2673976];
        
        foreach($shipments as $shipment){
            $shipment = Shipment::find($shipment);
            if($shipment) {
                $fulfilment_order = FulfillmentOrder::where('shipment_id',$shipment->id)->select('id','reference_id','seller_location_id')->first();
                if($fulfilment_order){
                    $sellerLocation = SellerLocation::find($fulfilment_order->seller_location_id);
                    if($sellerLocation && $sellerLocation->is_ffc_enabled == 1){
                        event(new SendReturnShipmentDetailsToGTECHEvent($shipment));
                    }
                }
            }
        }

        Log::info('Send GTech Return Shipment Details Process Completed');
        echo "Send GTech Return Shipment Details Process Completed";
    }


    public function getTransferOrders($seller_id){
        Log::info("Seller id ::".$seller_id);
        Log::info('getTransferOrders :: process started');

        $time_to_match =  Carbon::now()->subMinutes(5)->toDateTimeString();
        $stock_transfer_orders = StockTransferFulfillmentOrder::on('unity-read')->where('seller_id',$seller_id)->where('created_at', '>=' , $time_to_match)->get();
        Log::info($stock_transfer_orders);
        if($stock_transfer_orders->count() > 0){
            $api_key = Setting::where('seller_id',$seller_id)->where('key', config('enum.api_keys')['GTECH_SYSTEM_API_KEY'])->first();
            $url = Setting::where('seller_id',$seller_id)->where('key', config('enum.api_keys')['GTECH_SYSTEM_API_URL'])->first();
            if($api_key && $url){
                $gtech = new Gtech($api_key->value,$url->value);
                foreach($stock_transfer_orders as $stock_transfer_order){
                    Log::info($stock_transfer_order->reference_id);
                    $stock_order = StockOrder::where("id",$stock_transfer_order->stock_order_id)->first();
                    $origin_location_reference = SellerLocation::where('id',$stock_transfer_order->seller_location_id)->value('seller_reference_id');
                    $destination_location_reference = SellerLocation::where('id',$stock_order->location_id)->value('seller_reference_id');
                    $stock_order_items = StockOrderItem::where("stock_order_id",$stock_order->id)->get();
                    $item_arr = [];
                    foreach($stock_order_items as $item){
                        $temp_arr = [];
                        $product = Product::find($item->product_id);
                        $temp_arr['Date'] = Carbon::parse($stock_transfer_order->created_at)->toAtomString();
                        $temp_arr['URReferenceId'] = $stock_transfer_order->reference_id;
                        $temp_arr['LocationFrom'] = $origin_location_reference;
                        $temp_arr['LocationTo'] = $destination_location_reference;
                        $temp_arr['Barcode'] = $product->barcode;
                        $temp_arr['Qty'] = $item->qty;
                        $item_arr[] = $temp_arr;
                    
                    }
                    Log::info($item_arr);
    
                    $gtech->createTransferOrder($item_arr);
                    
                }
            }else{
                \Log::info("SendReturnShipmentDetailsToGTECHEvent : API KEY or URL is not configured for seller :: ".$seller_id);
            }
        }
        Log::info('getTransferOrders :: process completed');
        return "Stock Transfer Orders Fetching completed";
    }
    
    public function getSpecificTransferOrders($seller_id){
        Log::info('getSpecificTransferOrders :: process started');

        $ids = [];

        $stock_transfer_orders = StockTransferFulfillmentOrder::on('unity-read')->where('seller_id',$seller_id)->whereIn('id', $ids)->get();
        Log::info($stock_transfer_orders);
        if($stock_transfer_orders->count() > 0){
            $api_key = Setting::where('seller_id',$seller_id)->where('key', config('enum.api_keys')['GTECH_SYSTEM_API_KEY'])->first();
            $url = Setting::where('seller_id',$seller_id)->where('key', config('enum.api_keys')['GTECH_SYSTEM_API_URL'])->first();
            if($api_key && $url){
                $gtech = new Gtech($api_key->value,$url->value);
                foreach($stock_transfer_orders as $stock_transfer_order){
                    Log::info($stock_transfer_order->reference_id);
                    $stock_order = StockOrder::where("id",$stock_transfer_order->stock_order_id)->first();
                    $origin_location_reference = SellerLocation::where('id',$stock_transfer_order->seller_location_id)->value('seller_reference_id');
                    $destination_location_reference = SellerLocation::where('id',$stock_order->location_id)->value('seller_reference_id');
                    $stock_order_items = StockOrderItem::where("stock_order_id",$stock_order->id)->get();
                    $item_arr = [];
                    foreach($stock_order_items as $item){
                        $temp_arr = [];
                        $product = Product::find($item->product_id);
                        $temp_arr['Date'] = Carbon::parse($stock_transfer_order->created_at)->toAtomString();
                        $temp_arr['URReferenceId'] = $stock_transfer_order->reference_id;
                        $temp_arr['LocationFrom'] = $origin_location_reference;
                        $temp_arr['LocationTo'] = $destination_location_reference;
                        $temp_arr['Barcode'] = $product->barcode;
                        $temp_arr['Qty'] = $item->qty;
                        $item_arr[] = $temp_arr;
                    
                    }
                    Log::info($item_arr);
    
                    $gtech->createTransferOrder($item_arr);
                    
                }
            }else{
                \Log::info("SendReturnShipmentDetailsToGTECHEvent : API KEY or URL is not configured for seller :: ".$seller_id);
            }
        }
        Log::info('getSpecificTransferOrders :: process completed');
        return "Stock Transfer Orders Fetching completed";
    }

    public function sendCancelledShipmentDetailsToGtech($seller_id){
        Log::info("Seller id ::".$seller_id);
        Log::info('GTech :: sendCancelledShipmentDetailsToGtech Started');
        
        $last_hour_date =  Carbon::now()->subMinutes(5)->toDateTimeString();
        $shipments = Shipment::on('unity-read')->whereSellerId($seller_id)
                    ->where('updated_at', '>=' , $last_hour_date)
                    ->where('status', "Cancelled")
                    ->get();

        if($shipments->count() > 0) {

            foreach($shipments as $shipment) {
                Log::info($shipment);
                event(new SendCancelledStatusToGtechEvent($shipment));
            }

        }

        Log::info('Send GTech Cancelled Shipment Details Process Completed');
        echo "Send GTech Cancelled Shipment Details Process Completed";
    }

    public function sendCancelledShipmentDetailsToGtechInBulk(){
        Log::info('GTech :: sendCancelledShipmentDetailsToGtechInBulk Started');
        
       $shipments = [''];
        
        foreach($shipments as $shipment){
            $shipment = Shipment::find($shipment);
            if($shipment) {
                Log::info($shipment);
                event(new SendCancelledStatusToGtechEvent($shipment));
            }
        }

        Log::info('Send GTech Cancelled Shipment Details Process In Bulk Completed');
        echo "Send GTech Cancelled Shipment Details Process  In Bulk  Completed";
    }

    public function getReceiveFinish($seller_id){
        Log::info("Seller id ::".$seller_id);
        Log::info('GTech :: getReceiveFinish Started');
        
        $api_key = Setting::where('seller_id',$seller_id)->where('key', config('enum.api_keys')['GTECH_SYSTEM_API_KEY'])->first();
        $url = Setting::where('seller_id',$seller_id)->where('key', config('enum.api_keys')['GTECH_SYSTEM_API_URL'])->first();

        if($api_key && $url){
            
            $gtech = new Gtech($api_key->value,$url->value);
            $sellerLocations = SellerLocation::where('seller_id', $seller_id)->get();
            if($sellerLocations->count() > 0){
                foreach($sellerLocations as $location){
                    $gtech->createStockOrder($location);
                }
            }

        }else{
            Log::info("getReceiveFinish : API KEY or URL is not configured for seller :: ".$seller_id);
        }


        Log::info('getReceiveFinish Process Completed');
        echo "getReceiveFinish Process Completed";
    }



}
