<?php

namespace App\Http\Controllers;

use App\Events\ReturnSalesZubaidasEvent;
use App\Models\Shipment;
use Exception;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;
use App\Helpers\ZubaidasERP;
use App\Models\GRNPostingReceipt;
use App\Models\Product;
use App\Models\SellerLocation;
use App\Models\StockOrder;
use App\Models\StockOrderItem;
use App\Models\StockTransferFulfillmentOrder;
use App\Service\Integration\ERP\ERPForZubaidas;

class ZubaidasController extends Controller
{

    public function index()
    {

    }

    public function getReturnMarkedOrders($seller_id)
    {
        Log::info("Zubaidas | GetReturnMarkedOrders | Seller id < " . $seller_id . " >");
        Log::info("Zubaidas | GetReturnMarkedOrders | Started");

        $last_hour_date = Carbon::now()->subMinutes(5)->toDateTimeString();
        $shipments = Shipment::on('unity-read')->whereSellerId($seller_id)
            ->where('updated_at', '>=', $last_hour_date)
            ->where('erp_sync', '=', 0)
            ->where('status', "Return Received")
            ->get();
        if ($shipments->count() > 0) {

            foreach ($shipments as $shipment) {
                Log::info($shipment);
                $params['shipment'] = $shipment;
                $params['cn'] = $shipment->tracking_number;
                event(new ReturnSalesZubaidasEvent($params));
            }

        }

        Log::info("Zubaidas | GetReturnMarkedOrders | Completed");
        return "Zubaidas | GetReturnMarkedOrders | Completed";
    }



    public function getStockTransferOrders($seller_id)
    {
        Log::info("Seller id ::" . $seller_id);
        $locations = SellerLocation::on('unity-read')->where('seller_id', $seller_id)->get();

        if ($locations->count() > 0) {

            $erp = new ERPForZubaidas();
            $zubaidas_erp = new ZubaidasERP($erp);

            foreach ($locations as $location) {
                $zubaidas_erp->getStockOrders($location->seller_reference_id, $seller_id);
            }
        }

        return "Stock orders request submitted";
    }


    public function getGRNPostedOrders($seller_id)
    {
        Log::info("getGRNPostedOrders Seller id ::" . $seller_id);

        $last_hour_date = Carbon::now()->subMinutes(15)->toDateTimeString();
        $stock_orders = StockOrder::on('unity-read')->where('seller_id', $seller_id)->where('status', "=", "Received")->where('updated_at', '>=', $last_hour_date)->get();

        // Log::info($stock_orders);

        if ($stock_orders->count() > 0) {

            $erp = new ERPForZubaidas();
            $zubaidas_erp = new ZubaidasERP($erp);

            foreach ($stock_orders as $stock_order) {

                $stock_order_items = GRNPostingReceipt::where("stock_order_id", $stock_order->id)->where("grn_receipt", 0)->get();
                $item_arr = [];
                $temp_arr = [];

                foreach ($stock_order_items as $item) {

                    $product = Product::find($item->seller_product_id);

                    if ($product) {

                        if (isset($temp_arr[$product->barcode])) {
                            $temp_arr[$product->barcode]['quantity'] = $temp_arr[$product->barcode]['quantity'] + $item->qty_received;

                        } else {
                            $temp_arr[$product->barcode]['sku'] = $product->SKU;
                            $temp_arr[$product->barcode]['quantity'] = $item->qty_received;
                        }
                    }
                }

                foreach ($temp_arr as $key => $value) {

                    $item_arr['items_arr'][] = ['item' => $key, 'quantity' => $value['quantity']];
                    $item_arr['items_arr_temp'][] = ['Barcode' => $key, 'sku' => $value['sku'], 'Quantity' => $value['quantity']];
                }

                // Log::info($item_arr);

                if (count($item_arr) > 0) {
                    $zubaidas_erp->getGRNPostedOrders($stock_order, $item_arr, $stock_order_items, $seller_id);
                }
            }
        }

        return "GRN Posted orders request submitted";
    }


    public function getGRNPartialPostedOrders($seller_id)
    {
        // Log::info("Seller id ::".$seller_id);
        // $last_hour_date =  Carbon::now()->subMinutes(15)->toDateTimeString();

        $stock_orders = StockOrder::on('unity-read')->where('seller_id', $seller_id)->where('status', "=", "Partial")->get();
        // Log::info($stock_orders);

        if ($stock_orders->count() > 0) {

            $erp = new ERPForZubaidas();
            $zubaidas_erp = new ZubaidasERP($erp);

            foreach ($stock_orders as $stock_order) {

                $stock_order_items = GRNPostingReceipt::where("stock_order_id", $stock_order->id)->where("grn_receipt", 0)->get();
                $item_arr = [];
                $temp_arr = [];

                foreach ($stock_order_items as $item) {

                    $product = Product::find($item->seller_product_id);

                    if ($product) {

                        if (isset($temp_arr[$product->barcode])) {
                            $temp_arr[$product->barcode]['quantity'] = $temp_arr[$product->barcode]['quantity'] + $item->qty_received;

                        } else {
                            $temp_arr[$product->barcode]['sku'] = $product->SKU;
                            $temp_arr[$product->barcode]['quantity'] = $item->qty_received;
                        }
                    }
                }

                foreach ($temp_arr as $key => $value) {

                    $item_arr['items_arr'][] = ['item' => $key, 'quantity' => $value['quantity']];
                    $item_arr['items_arr_temp'][] = ['Barcode' => $key, 'sku' => $value['sku'], 'Quantity' => $value['quantity']];
                }

                // Log::info($item_arr);

                if (count($item_arr) > 0) {
                    $zubaidas_erp->getGRNPostedOrders($stock_order, $item_arr, $stock_order_items, $seller_id);
                }
            }
        }
    }



    public function getTransferOrders($seller_id)
    {
        $time_to_match = Carbon::now()->subMinutes(5)->toDateTimeString();
        $stock_transfer_orders = StockTransferFulfillmentOrder::on('unity-read')->where('seller_id', $seller_id)->where('created_at', '>=', $time_to_match)->get();

        // Log::info($stock_transfer_orders);

        if ($stock_transfer_orders->count() > 0) {

            foreach ($stock_transfer_orders as $stock_transfer_order) {

                // Log::info($stock_transfer_order->reference_id);
                $stock_order = StockOrder::where("id", $stock_transfer_order->stock_order_id)->first();
                $origin_location_reference = SellerLocation::where('id', $stock_transfer_order->seller_location_id)->value('seller_reference_id');
                $destination_location_reference = SellerLocation::where('id', $stock_order->location_id)->value('seller_reference_id');
                $stock_order_items = StockOrderItem::where("stock_order_id", $stock_order->id)->get();

                $params = [
                    'docpaaa' => 'IWT',
                    'docno' => '',
                    'docdate' => Carbon::parse($stock_transfer_order->arrival_date)->toAtomString(),
                    'refdoc' => $stock_transfer_order->reference_id,
                    'refdate' => Carbon::parse($stock_transfer_order->created_at)->toAtomString(),
                    'locationfrom' => $origin_location_reference,
                    'locationto' => $destination_location_reference,
                    'nart' => 'Stock Transfer Order',
                    'rmrks' => 'Stock Transfer Order creation request from Unity',
                    'items' => []
                ];

                foreach ($stock_order_items as $item) {

                    $product = Product::find($item->product_id);
                    $params['items'][] = [
                        'item' => $product->barcode,
                        'quantity' => $item->qty
                    ];
                }
                // Log::info($params);

                $erp = new ERPForZubaidas();
                $erp->createTransferOrder($params);
            }
        }

        \Log::info('getTransferOrders :: process completed');
        return "Stock Transfer Orders Fetching completed";
    }

}
