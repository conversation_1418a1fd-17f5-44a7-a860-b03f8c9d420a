<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Models\Order;
use App\Models\RobocallLog;
use App\Models\ItsRobocallLog;
use App\Models\Seller;
use Carbon\Carbon;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use App\Helpers\OrderComment;
use App\Models\HthreeRobocallLog;

class RobocallController extends Controller
{
    public function getEvents(Request $request)
    {
        try{
            Log::info('Robocall Event Logs: '.$request);
            $topic = $request->header('X-Auth-Topic');
            $order = null;

            if(!$request->has('seller_id') || !$request->has('order_id') || !$request->has('event_date') || !$request->has('call_duration'))
            {
                return response()->json(['message' => 'Parameters Missing'],400);
            }
            elseif($request->seller_id == '' || $request->seller_id == null)
            {
                return response()->json(['message' => 'Seller ID parameter cannot be null'],400);
            }
            elseif($request->event_date == '' || $request->event_date == null)
            {
                return response()->json(['message' => 'Event Date parameter cannot be null'],400);
            }

            if($topic == config('enum.robocall_events_short_status')[1] || $topic == config('enum.robocall_events_short_status')[4]){
                if($request->order_id == '' || $request->order_id == null)
                {
                    return response()->json(['message' => 'Order ID parameter cannot be null'],400);
                }
            }

            if($topic == config('enum.robocall_events_short_status')[2] || $topic == config('enum.robocall_events_short_status')[3]){
                if($request->order_id == '' || $request->order_id == null)
                {
                    return response()->json(['message' => 'Order ID parameter cannot be null'],400);
                }
                elseif($request->call_duration == '' || $request->call_duration == null)
                {
                    return response()->json(['message' => 'Call Duration parameter cannot be null'],400);
                }
            }

            if(!Seller::whereId($request->seller_id)->exists()){
                return response()->json(['message' => 'Wrong Seller ID'],400);
            }

            if($request->order_id != '' || $request->order_id != null){
                $order = Order::whereSellerId($request->seller_id)->where('marketplace_reference_id',$request->order_id)->first();
                if(!$order){
                    return response()->json(['message' => 'Wrong Order ID'],400);
                }
            }

            $parsed_date = ($request->event_date === '0000-00-00 00:00:00' || empty($request->event_date))
                ? null
                : Carbon::parse($request->event_date)->toDateTimeString();

            $logs = new RobocallLog();
            $logs->seller_id = $request->seller_id;
            $logs->order_id = isset($order) ? $order->id : null;
            $logs->event_id = array_search ($topic, config('enum.robocall_events_short_status'));
            $logs->event_date = $parsed_date;
            $logs->call_duration = $request->call_duration;
            $logs->save();

            $order_id = $logs->order_id;
            $total_duration_of_call = "";
            $event_message = "Event is not mapped";
            
            if($logs->event_id != null && $logs->event_id != ""){
                $event_message = config('enum.robocall_events_long_status')[$logs->event_id];
            }

            if($logs->call_duration != null && $logs->call_duration != ""){
                $total_duration_of_call = " - Total Duration of call : ".$logs->call_duration;
            }

            $date_time  = " - ".$logs->event_date;
            $message = $event_message.$total_duration_of_call.$date_time;
            $key = "Robocall Event";
            $status = "Success";
            OrderComment::add(compact('order_id','message','key','status'));
            
            return response()->json(['message' => 'Logs created successfully'],201);
            
        } catch(Exception $e){
            Log::info('Robocall Logs | Error | '.$e->getMessage());
            return response()->json(['message' => 'Something went wrong'],500);
        }
        
    }

    public function getItsEvents(Request $request)
    {
        try{
            Log::info('Robocall ITS Event Logs : '.$request);
            $topic = $request->header('X-Auth-Topic');
            $order = null;
       
            if(!$request->has('order_id') || !$request->has('callinput') || !$request->has('camp_id'))
            {
                return response()->json(['message' => 'Parameters Missing'],400);
            }
            // elseif($request->seller_id == '' || $request->seller_id == null)
            // {
            //     return response()->json(['message' => 'Seller ID parameter cannot be null'],400);
            // }
            // elseif($request->callinput == '' || $request->callinput == null)
            // {
            //     return response()->json(['message' => 'Call input parameter cannot be null'],400);
            // }

            $seller_id = 3988;
            if($request->camp_id == 400){
                $seller_id = 412;
            }

            if(!Seller::whereId($seller_id)->exists()){
                return response()->json(['message' => 'Wrong Seller ID'],400);
            }

            if($request->order_id != '' || $request->order_id != null){
                $order = Order::whereSellerId($seller_id)->where('marketplace_reference_id',$request->order_id)->first();
                if(!$order){
                    return response()->json(['message' => 'Wrong Order ID'],400);
                }
            }

            $logs = new ItsRobocallLog();
            $logs->seller_id = $seller_id;
            $logs->order_id = isset($order) ? $order->id : null;
            $logs->cdrid = $request->cdrid;
            $logs->callresponse =  $request->callresponse;
            $logs->callinput = $request->callinput;
            $logs->camp_id = $request->camp_id;

            $logs->save();

            $order_id = $logs->order_id;
           
           
            
            $callinput = $request->callinput;
            if($callinput == 1 || $callinput == 2){
                ItsRobocallLog::addInput(compact('order_id','seller_id','callinput'));
                $event_message = "The call was : ".$logs->callresponse." , with input : ".$logs->callinput;
            }else{
                $event_message = "The call was : ".$logs->callresponse." , with no or incorrect input";
            }


            $date_time  = " - ".Carbon::now()->toDateTimeString();
            $message = $event_message.$date_time;
            $key = "Robocall Event";
            $status = "Success";

             OrderComment::add(compact('order_id','message','key','status'));

            return response()->json(['message' => 'Logs created successfully'],201);
            
        } catch(Exception $e){
            Log::info('Robocall ITS Event Logs | Error | '.$e->getMessage());
            return response()->json(['message' => 'Something went wrong'],500);
        }
        
    }

    public function getH3Events(Request $request)
    {
        try{
            Log::info('Robocall H3 Event Logs : '.$request);
            $topic = $request->header('X-Auth-Topic');
            $order = null;
       
            if(!$request->has('order_id') || !$request->has('dtmf') || !$request->has('seller_id'))
            {
                return response()->json(['message' => 'Parameters Missing'],400);
            }
           

            $seller_id = $request->seller_id;
            

            if(!Seller::whereId($seller_id)->exists()){
                return response()->json(['message' => 'Wrong Seller ID'],400);
            }

            if($request->order_id != '' || $request->order_id != null){
                $order = Order::whereSellerId($seller_id)->where('id',$request->order_id)->first();
                if(!$order){
                    return response()->json(['message' => 'Wrong Order ID'],400);
                }
            }

            $logs = new HthreeRobocallLog();
            $logs->seller_id = $seller_id;
            $logs->order_id = isset($order) ? $order->id : null;
            $logs->callresponse = ($request->has('vs_name') ? $request->vs_name : $request->dtmf ) ;
            $logs->callinput = $request->dtmf;
            $logs->call_duration = $request->voice_sec;
            $logs->event_date = Carbon::parse($request->time)->toDateTimeString();
            $logs->save();

            $total_duration_of_call = "";
            $event_message = $logs->callresponse;
        

           
            $order_id = $logs->order_id;
            
            $dtmf = $request->dtmf;
            $date_time  = " - ".$logs->event_date;

            if($dtmf == 1 || $dtmf == 2 || $dtmf == 3){
                HthreeRobocallLog::addInput(compact('order_id','seller_id','dtmf'));
                if($logs->call_duration != null && $logs->call_duration != ""){
                    $total_duration_of_call = " - Total Duration of call : ".$logs->call_duration;
                    $message = $event_message.$total_duration_of_call.$date_time;
                }    
            }else{
                $event_message = "The call was : ".$logs->callresponse." , with no or incorrect input";
                $message = $event_message.$date_time;
            }


            $key = "Robocall Event";
            $status = "Success";

             OrderComment::add(compact('order_id','message','key','status'));

            return response()->json(['message' => 'Logs created successfully'],201);
            
        } catch(Exception $e){
            Log::info('Robocall H3 Event Logs | Error | '.$e->getMessage());
            return response()->json(['message' => 'Something went wrong'],500);
        }    
    }

    public function getH3MerchantEvents(Request $request)
    {
        try{
            Log::info('Robocall H3 Merchant Event Logs : '.$request);
            $topic = $request->header('X-Auth-Topic');
            $order = null;
       
            if(!$request->has('key1') || !$request->has('dtmf') || !$request->has('key2'))
            {
                return response()->json(['message' => 'Parameters Missing'],400);
            }
           

            $seller_id = $request->key1;
            

            if(!Seller::whereId($seller_id)->exists()){
                return response()->json(['message' => 'Wrong Seller ID'],400);
            }

            if($request->key2 != '' || $request->key2 != null){
                $order = Order::whereSellerId($seller_id)->where('id',$request->key2)->first();
                if(!$order){
                    return response()->json(['message' => 'Wrong Order ID'],400);
                }
            }

            $logs = new HthreeRobocallLog();
            $logs->seller_id = $seller_id;
            $logs->order_id = isset($order) ? $order->id : null;
            $logs->callresponse = ($request->has('call_status') ? $request->call_status : "" );
            $logs->callinput = $request->dtmf;
            $logs->call_duration = ($request->has('seconds') ? $request->seconds : "" );
            $logs->event_date = Carbon::now()->toDateTimeString();
            $logs->save();

            $total_duration_of_call = "";
            $event_message = $logs->callresponse;
        

           
            $order_id = $logs->order_id;
            
            $dtmf = $request->dtmf;
            $date_time  = " - ".$logs->event_date;
            $message = "";
            if($dtmf == 1 || $dtmf == 2 || $dtmf == 3){
                if($logs->callresponse != null && $logs->callresponse != ""){
                    HthreeRobocallLog::addInputForMerchant(compact('order_id','seller_id','dtmf'));
                    $message = "Call Attempt Status : ".$logs->callresponse;
                }
                if($logs->call_duration != null && $logs->call_duration != ""){
                    $total_duration_of_call = "Duration of call : ".$logs->call_duration;
                    $message = $event_message.$total_duration_of_call.$date_time;
                }    
            }else{
                if($logs->callresponse != null && $logs->callresponse != ""){
                    $event_message = "Call Attempt Status : ".$logs->callresponse." , with no or incorrect input";
                    $message = $event_message.$date_time;
                }
            }  


            $key = "Robocall Event";
            $status = "Success";

             OrderComment::add(compact('order_id','message','key','status'));

            return response()->json(['message' => 'Logs created successfully'],201);
            
        } catch(Exception $e){
            Log::info('Robocall H3 Event Logs | Error | '.$e->getMessage());
            return response()->json(['message' => 'Something went wrong'],500);
        }    
    }

    
}
