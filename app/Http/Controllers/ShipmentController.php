<?php

namespace App\Http\Controllers;

use App\Events\OrderTrackingEvent;
use App\Models\ShipmentCourierHistory;
use App\Events\ShipmentStatusEvent;
use App\Events\GetOrderOriginEvent;
use App\Events\InternationalShipmentStatusEvent;
use App\Events\SendShipmentDetailsEvent;
use App\Events\ShipmentReturnStatusEvent;
use App\Events\ShipmentStatus2Event;
use App\Events\ShipmentStatus3Event;
use App\Events\ShipmentStatus4Event;
use App\Exports\ReportExport;
use App\Helpers\BundledEmailEmpty;
use App\Helpers\BundledEmailFill;
use App\Models\BundledEmail;
use App\Models\Order;
use App\Models\OrderComment;
use App\Models\Shipment;
use App\Models\ShipmentHistory;
use Carbon\Carbon;
use Exception;
use Illuminate\Support\Facades\Log;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Storage;
use Maatwebsite\Excel\Facades\Excel;
use App\Events\SendDeliveredShipmentDetailsToGtechEvent;
use App\Events\SendReturnShipmentDetailsToGTECHEvent;

class ShipmentController extends Controller
{
    public function index()
    {
        Log::info('Shipment Status Started Again');
        // $from = '2021-12-31';

        // $terminal_shipments = [];
        // $terminal_statuses =  [
        //     'Shipment - Delivered',
        //     'Delivered',
        //     'DELIVERED'
        // ];

        // $shipments =  ShipmentCourierHistory::on('unity-read')->where('shipment_courier_histories.shipment_id', '>', 1668437)
        //                 ->join('shipments','shipment_courier_histories.shipment_id','shipments.id')
        //                 ->join('couriers','shipments.courier_id','couriers.id')
        //                 ->join('orders','shipments.order_id','orders.id')
        //                 ->join('seller_locations','shipments.seller_location_id','seller_locations.id')
        //                 ->selectRaw('shipment_courier_histories.*,shipments.tracking_number,shipments.seller_location_id,shipments.order_id,orders.entity_id,orders.marketplace_reference_id,couriers.name,shipments.courier_id,shipments.type,seller_locations.location_name')
        //                 ->where('shipments.seller_id','119')
        //                 ->where('shipment_courier_histories.storefront_sync', 0)
        //                 ->orderBy('shipment_courier_histories.status_at','asc')
        //                 ->get();

        // Log::info('Shipment Status Query Completed');
        // $count = 0;

        // if($shipments){
        //     foreach($shipments->groupBy('shipment_id') as $shipment_by_id) {
                
        //         $count++;
        //         foreach ($shipment_by_id as $key => $shipment) {
                    
        //             if (in_array($shipment->shipment_id, $terminal_shipments)) {
        //                 continue;
        //             }

        //             if (in_array($shipment->status, $terminal_statuses)) {
        //                 $terminal_shipments[] = $shipment->shipment_id;
        //             }

        //             if ($count == 1) {
        //                 event(new ShipmentStatusEvent($shipment->toArray()));
        //             } else if ($count == 2) {
        //                 event(new ShipmentStatus2Event($shipment->toArray()));
        //             } else if ($count == 3) {
        //                 event(new ShipmentStatus3Event($shipment->toArray()));
        //             } else if ($count == 4) {
        //                 event(new ShipmentStatus4Event($shipment->toArray()));
        //             }

        //         }

        //         if ($count == 4) {
        //             $count = 0;
        //         }
        //     }
        //     ShipmentCourierHistory::whereIn('id', $shipments->pluck('id'))->update(['storefront_sync' => 1]);
        // }
        Log::info('Shipment Status Ended');
        echo "completed";
    }

    public function khaadiReturnStatus()
    {
        Log::info('Khaadi Return Status Started');
        // $to = Carbon::now()->toDateTimeString();
        // $from = Carbon::now()->subMinutes(30)->toDateTimeString();
        $return_statuses =  [
            'Replacement - Delivered to Shipper',
            'Return - Delivered to Shipper',
            'Returned',
            'RETURNED',
            'Return to Shipper',
            'Return'
        ];

        $date_limit = Carbon::parse('2021-12-01')->toDateTimeString();
        
        // $shipments =  ShipmentCourierHistory::where('shipment_courier_histories.created_at','>=',$date_limit)->whereIn('shipment_courier_histories.status',$return_statuses)->join('shipments','shipment_courier_histories.shipment_id','shipments.id')->join('couriers','shipments.courier_id','couriers.id')->join('orders','shipments.order_id','orders.id')->join('seller_locations','shipments.seller_location_id','seller_locations.id')->selectRaw('shipment_courier_histories.*,shipments.tracking_number,shipments.seller_location_id,shipments.order_id,orders.entity_id,orders.marketplace_reference_id,couriers.name,shipments.courier_id,shipments.type,seller_locations.location_name')->whereIn('shipments.seller_id',['1'])->get();

        $orders = Shipment::on('unity-read')->where('shipments.created_at','>=',$date_limit)
                    ->join('orders','shipments.order_id','orders.id')
                    ->where('shipments.status','Return')
                    ->whereIn('shipments.seller_id',[119])
                    ->where('shipments.return_receive_at_store',0)
                    ->select('orders.marketplace_reference_id')
                    ->take(1800)
                    ->get();

        if($orders){
            foreach($orders->chunk(300) as $order){
                event(new ShipmentReturnStatusEvent($order->toArray()));
            }
        }
        echo "completed";
    }

    public function internationalStatusSync()
    {
        Log::info('International Shipment Status Started');
        $to = Carbon::now()->toDateTimeString();
        $from = Carbon::now()->subMinutes(30)->toDateTimeString();
        
        $shipments =  ShipmentHistory::whereBetween('shipment_histories.created_at',[$from,$to])->join('shipments','shipment_histories.shipment_id','shipments.id')->join('couriers','shipments.courier_id','couriers.id')->join('orders','shipments.order_id','orders.id')->selectRaw('shipment_histories.*,shipments.tracking_number,shipments.seller_location_id,shipments.order_id,orders.entity_id,orders.marketplace_reference_id,orders.country,couriers.name,shipments.courier_id,shipments.type')->whereIn('shipments.seller_id',['120'])->orderBy('shipment_histories.status_at','asc')->get();

        if($shipments){
            foreach($shipments as $shipment){
                event(new InternationalShipmentStatusEvent($shipment->toArray()));
            }
            ShipmentHistory::whereIn('id', $shipments->pluck('id'))->update(['storefront_sync' => 1]);
        }
        echo "completed";
    }

    public function temporaryStatusReport()
    {
        // return Excel::download(new ReportExport, 'khaadi_magento_status_sync_report(Weaves).xlsx');
    }

    public function orderTracking()
    {
        Log::info('International Order Tracking Started');

        $orders = Order::where('seller_id',120)->where('status','!=','Cancelled')->doesnthave('shipments')->where('country','GB')->select('id','entity_id','marketplace_reference_id')->get();

        foreach($orders as $order){
            event(new OrderTrackingEvent($order->id, $order->entity_id, $order->marketplace_reference_id));
        }
        echo "International Order Tracking Ended";
    }

    public function latest_status()
    {
        Log::info('Shipment latest Status Started');

        $shipments = Shipment::where('shipments.seller_id',119)
                    ->join('orders','shipments.order_id','=','orders.id')
                    ->join('couriers','shipments.courier_id','=','couriers.id')
                    ->where('shipments.status','!=','Cancelled')
                    ->where('shipments.type',Null)
                    ->select('shipments.id','shipments.tracking_number','shipments.order_id','orders.entity_id','couriers.name')
                    ->get();

        foreach ($shipments as $shipment) {
            $latest_status = ShipmentCourierHistory::whereShipmentId($shipment->id)->orderBy('status_at','desc');
            
            if($latest_status->exists()) {
                $latest_status = $latest_status->first()->toArray();
                $temp = array(  'tracking_number' => $shipment->tracking_number,
                                'order_id' => $shipment->order_id,
                                'entity_id' => $shipment->entity_id,
                                'name' => $shipment->name);

                
                $data = array_merge($latest_status, $temp);
                //event(new ShipmentStatusEvent($data));
            }
        }

        echo "Shipment latest Status Completed";
    }

    public function nonOriginOrders()
    {
        Log::info('Get Order Origin Process Started');
        
        $orders_array = [];
        $orders_count = 0;
        $total = 0;
        
        $order_ids = Order::on('unity-read')->whereSellerId('119')->whereStatus('Pending')->where('seller_location_id','1')->orderBy('id','asc')->pluck('id');
        $order_ids_count = count($order_ids); 
        Log::info('Get Order Origin Process | total '.$order_ids_count.' orders');
        
        if($order_ids) {

            foreach($order_ids as $order_id) {
                array_push($orders_array, $order_id);
                $total++;
                $orders_count++;

                if($orders_count == env('D365_GET_BULK_ORDER_ORIGIN_SIZE', 100) || $total == $order_ids_count) {
                    event(new GetOrderOriginEvent($orders_array));
                    $orders_count = 0;
                    $orders_array = [];
                }
            }
        }
        Log::info('Get Order Origin Process Ended');
        return "Get Order Origin Process Completed";
    }

    public function sendShipmentDetails()
    {
        Log::info('Send Shipment Details Process Started');

        $from = '2021-12-31';

        $shipments = Shipment::on('unity-read')->whereSellerId('119')
                            ->where('id', '>', 1668437)
                            ->where('type',Null)
                            ->whereDate('created_at', '>', $from)
                            ->where('erp_sync', 0)
                            ->get();

        if($shipments) {
            
            foreach($shipments as $shipment) {
                event(new SendShipmentDetailsEvent($shipment));
            }
            Shipment::whereIn('id', $shipments->pluck('id'))->update(['erp_sync' => 1]);
        }
        Log::info('Send Shipment Details Process Completed');
        echo "Send Shipment Details Process Completed";
    }

    public function syncShipments()
    {
        Log::info('Shipments Sync Process Started');

        $from = '2021-12-31';

        $count = 0;
        $data = [];
        $total = 0;
        $ids = [];

        $shipments = Shipment::on('unity-read')->where('seller_id','119')
                            ->where('id', '>', 1668437)
                            ->where('type',Null)
                            ->whereDate('created_at', '>', $from)
                            ->where('storefront_sync', 0)
                            ->with([
                                'order'  => function ($query) {
                                    $query->select('entity_id','id','seller_location_id');
                                },
                                 'order.location' => function ($query) {
                                    $query->select('id','location_name');
                                }
                            ])
                            ->get(['tracking_number','courier_id','order_id','id']);

        if($shipments) {

            foreach($shipments as $shipment) {
            
                $ids[] = $shipment->id;
                $data[$count]['orderid'] = $shipment->order->entity_id;
                $data[$count]['cn'] = $shipment->tracking_number;
            
                if($shipment->courier_id == 4) {
                    $data[$count]['carrier'] = 'mnp';
                } elseif($shipment->courier_id == 8) {
                    $data[$count]['carrier'] = 'trax';
                } elseif($shipment->courier_id == 11) {
                    $data[$count]['carrier'] = 'rider';
                } elseif($shipment->courier_id == 1) {
                    $data[$count]['carrier'] = 'KLM';
                } elseif($shipment->courier_id == 7) {
                    $data[$count]['carrier'] = 'call_courier';
                } elseif($shipment->courier_id == 19) {
                    $data[$count]['carrier'] = 'movex';
                } elseif($shipment->courier_id == 5) {
                    $data[$count]['carrier'] = 'Leopard';
                } elseif($shipment->courier_id == 13) {
                    $data[$count]['carrier'] = 'TCS';
                } 


                $data[$count]['hub'] = $shipment->order->location->location_name;
                
                $count++;
                $total++;

                if($count == 300 || $total == count($shipments)) {
                    
                    Log::info('Request Started');
                    $curl = curl_init();
                    $order_array = [
                        'orders' => $data
                    ];

                    curl_setopt_array($curl,
                        array(
                            CURLOPT_URL => env('KHAADI_URL')."/rest/V1/orders/updatecarriercn",
                            CURLOPT_RETURNTRANSFER => true,
                            CURLOPT_ENCODING => "",
                            CURLOPT_MAXREDIRS => 10,
                            CURLOPT_TIMEOUT => 60,
                            CURLOPT_FOLLOWLOCATION => true,
                            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                            CURLOPT_CUSTOMREQUEST => "POST",
                            CURLOPT_POSTFIELDS =>json_encode($order_array),
                            CURLOPT_HTTPHEADER => [
                                "Authorization: Bearer ".env('KHAADI_TOKEN'),
                                "Content-Type: application/json"
                            ],
                        )
                    );

                    $response = curl_exec($curl);
                    $response = json_decode($response);
                    Log::info('Request Ended');
                    Log::info('Count = '.$count);
                    Log::info($order_array);
                    Log::info($response);
                    $count = 0;
                    $data = [[]];

                    if(isset($response['Failed rows']) && $response['Failed rows'] > 0) {
                        
                        $unity = explode(',',env('UNITY_POC'));
                        $others = explode(',',env('KHAADI_POC'));

                        if($others[0] != "") {
                            Mail::raw('Request Data: '.$order_array."\r\n".'Response Data: '.$response, function ($m)  use($others,$unity) {
                                $m->to($others)
                                    ->bcc($unity)
                                    ->subject('Shipment Tagging Storefront Process');
                            });
                        } else {
                            Mail::raw('Request Data: '.$order_array."\r\n".'Response Data: '.$response, function ($m)  use($unity) {
                                $m->to($unity)
                                    ->subject('Shipment Tagging Storefront Process');
                            });
                        }

                    } else {
                        Shipment::whereIn('id', $ids)->update(['storefront_sync' => 2, 'storefront_sync_at' => Carbon::now()->toDateTimeString() ]);
                    }

                    $ids = [];
                }
            }
        }
        

        
        Log::info("Shipments Sync Process Completed");
        echo "Shipments Sync Process Completed";
    }

    public function forceShipmentStatusSyncPage(){
        return view('order');
    }

    public function forceShipmentStatusSync(Request $request)
    {
        // return $request->all();
        Log::info('Force Shipment Status Started');
        $shipment_id = [];
        $order_arr = explode(',',$request->orders);
        // $order_arr = ["PK-001578790","PK-001599208","PK-001629958","PK-001779677"];
    
        $shipments =  ShipmentCourierHistory::where('shipments.seller_id','119')
                        ->whereIn('orders.marketplace_reference_id',$order_arr)
                        ->join('shipments','shipment_courier_histories.shipment_id','shipments.id')
                        ->join('couriers','shipments.courier_id','couriers.id')
                        ->join('orders','shipments.order_id','orders.id')
                        ->join('seller_locations','shipments.seller_location_id','seller_locations.id')
                        ->selectRaw('shipment_courier_histories.*,shipments.tracking_number,shipments.seller_location_id,shipments.order_id,orders.entity_id,orders.marketplace_reference_id,couriers.name,shipments.courier_id,shipments.type,seller_locations.location_name');
                        
        if($request->sort_order == 'status_at'){
            $shipments->orderBy('shipment_courier_histories.status_at','desc');
        }
        elseif($request->sort_order == 'created_at'){
            $shipments->orderBy('shipment_courier_histories.created_at','desc');
        }
        elseif($request->sort_order == 'id'){
            $shipments->orderBy('shipment_courier_histories.id','desc');
        }

        $shipments = $shipments->get();


        // $shipments = Shipment::where('shipments.seller_id','119')->whereIn('orders.marketplace_reference_id',$order_arr)
        // ->join('orders','shipments.order_id','=','orders.id')
        // ->join('shipment_courier_histories','shipments.id','shipment_courier_histories.shipment_id')
        // ->join('couriers','shipments.courier_id','couriers.id')
        // ->selectRaw('shipment_courier_histories.shipment_id,shipment_courier_histories.*,shipments.tracking_number,shipments.seller_location_id,shipments.order_id,orders.entity_id,orders.marketplace_reference_id,couriers.name,shipments.courier_id');
        
        
        // $shipments = $shipments->get();

        // $count = 0;
        // return $shipments;

        if($shipments){
            foreach($shipments as $shipment){
                if(in_array($shipment->shipment_id,$shipment_id) == false)
                {  
                    // $count++;
                    event(new ShipmentStatusEvent($shipment->toArray()));  
                    array_push($shipment_id, $shipment->shipment_id);
                }
            }
        }
        // return $count;
        echo "completed";
    }

    public function revertWrongCancellation()
    {
        Log::info('Revert Wrong Cancellation Started');
        $shipment_ids = [];
        $order_arr = ["PK-001597018","PK-001597465","PK-001597768","PK-001779677"];
        $statuses = ['Booked','Ready for Dispatch'];

        $shipments =  Shipment::where('shipments.seller_id','119')->whereIn('orders.marketplace_reference_id',$order_arr)
        ->join('orders','shipments.order_id','=','orders.id')
        ->selectRaw('shipments.id as shipment_id,orders.marketplace_reference_id as order_id')
        ->whereIn('shipments.status',$statuses)
        ->get();

        // return $shipments;
        if($shipments){
            foreach($shipments as $shipment){
                
                
                array_push($shipment_ids, $shipment->shipment_id);
                unset($order_arr[array_search($shipment->order_id,$order_arr)]);
            }
        }

        // echo "These orders are not processed";
        // return $order_arr;

        try{


            $curl = curl_init();

            curl_setopt_array($curl, array(
            CURLOPT_URL => env('ENDPOINT_URL')."/api/shipments/bulk-cancel",
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => "",
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 60,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => "POST",
            CURLOPT_POSTFIELDS =>array('ids' => $shipment_ids),
            CURLOPT_HTTPHEADER => array(
                "X-Auth-Token: yoitsasecretkey"
            ),
            ));

        

            $unity_response = json_decode(curl_exec($curl),true);

            $httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);

            curl_close($curl);

            if($httpCode == '200')
            {
                if($unity_response['error'] == 0)
                {
                    echo "These orders are not processed";
                    return $order_arr;
                }
                else{
                    return 'Unity Response | '.$unity_response['message'];
                }
            }
            else{
                return 'Unity Response | '.$httpCode;
            }
            
        }
        catch(\Exception $e)
        {
            return 'Error in process of sending Shipments ids to unity';
        }

        
        // echo "completed";
    }

    public function getOldCancelledOrders()
    {
        $order_ids =  Shipment::join('orders','shipments.order_id','=','orders.id')
        ->selectRaw('orders.marketplace_reference_id,shipments.tracking_number,shipments.order_id')
        ->where('orders.status','Completed')
        ->where('shipments.status','Cancelled')
        ->pluck('shipments.order_id')->toArray();

        $shipments = Shipment::selectRaw('Distinct(order_id),count(tracking_number)')
        ->whereIn('order_id',$order_ids)
        ->groupBy('order_id')
        ->havingRaw('count(shipments.tracking_number) = 1')
        ->get();

        return $shipments;
    }



    public function avgBookingTime()
    {
        $order_ids = Order::where('seller_id','119')->where('created_date','>','2020-12,07')->where('status','Processing')->where('seller_location_id','<>',1)->pluck('id')->toArray();
        $get_loc = OrderComment::whereIn('order_id',$order_ids)->whereIn('key',['Get Order Origin Process','D365 - Update Order Details Process'])->where('status','Success')
        ->orderBy('order_id','asc')
        ->orderBy('created_at','asc')
        ->get();

        // return $get_loc;

        // $send_update = OrderComment::where('key','D365 - Update Order Details Process')->where('status','Success')
        // ->orderBy('order_id','asc')
        // ->orderBy('created_at','asc')
        // ->get();

        $get_loc_array = [[]];
        $send_update_arr = [[]];

        foreach($get_loc as $loc)
        {
            if($loc->key == 'Get Order Origin Process')
            {
                if(array_key_exists($loc->order_id,$get_loc_array))
                {
    
                }
                else{
                    $get_loc_array[$loc->order_id] = $loc->created_at;
                }
            }
            elseif($loc->key == 'D365 - Update Order Details Process')
            {
                if(array_key_exists($loc->order_id,$send_update_arr))
                {
    
                }
                else{
                    $send_update_arr[$loc->order_id] = $loc->created_at;
                }
            }
        }

        // return $get_loc_array;
        // $sum = 0;
        $diff = 0;
        $count = 0;
        $new_arr = [];
        $one_array = [[]];
        $static_diff = 0;
        foreach($send_update_arr as $key => $value)
        {
            // return $key;
            if(array_key_exists($key,$get_loc_array))
            {
                if(in_array($key,$new_arr))
                {

                }
                else{
                    $date_one = Carbon::parse($get_loc_array[$key]);
                    $date_two = Carbon::parse($value);
                    $static_diff = $date_one->diffInMinutes($date_two);
                    if($static_diff < 60)
                    {
                        $diff += $static_diff;
                        $count++;
                        $one_array[$key] = $static_diff;
                    }
                    
                    array_push($new_arr,$key);
                    
                }
                
            }
            else{

            }


        }

        echo $diff/$count;
        // return $one_array;
    }

    public function assignCNs(){
        /*
        $i=0;
        $error = '';
        $orders = ["UK-000145263","UK-000145425","UK-000146124","UK-000146148","UK-000146106","UK-000146241","UK-000146397","UK-000147225","UK-000147327","UK-000144960","UK-000145203","UK-000147564","UK-000146529","UK-000147132","UK-000147111","UK-000147459","UK-000147783","UK-000147924","UK-000149883","UK-000145164","UK-000149991","UK-000151282","UK-000151222","UK-000151234","UK-000151219","UK-000151186","UK-000151252","UK-000151477","UK-000151486","UK-000151489","UK-000151495","UK-000151501","UK-000151504","UK-000151513","UK-000151519","UK-000151471","UK-000151468","UK-000151465","UK-000151462","UK-000151456","UK-000151447","UK-000151378","UK-000151393","UK-000151396","UK-000152329","UK-000151243","UK-000152728","UK-000152725","UK-000152722","UK-000152758","UK-000151255","UK-000153703","UK-000154010","UK-000154181","UK-000154220","UK-000154748","UK-000155932","UK-000156190","UK-000156472","UK-000156823","UK-000158060","UK-000158234","UK-000158408","UK-000159932","UK-000159539","UK-000159065","UK-000160883","UK-000159506","UK-000160724","UK-000159491","UK-000162338","UK-000163598","UK-000163403","UK-000163817","UK-000163625","UK-000163559","UK-000163607","UK-000163502","UK-000163628","UK-000164006","UK-000164204","UK-000164339","UK-000164222","UK-000159128","UK-000164153","UK-000158894","UK-000160061","UK-000158963","UK-000164282","UK-000160235","UK-000165791","UK-000166409","UK-000166517","UK-000166592","UK-000166799","UK-000166685","UK-000167054","UK-000167534","UK-000169202","UK-000168098","UK-000167723","UK-000170474","UK-000170744","UK-000168470","UK-000171536","UK-000171584","UK-000172064","UK-000172118","UK-000172301","UK-000172559","UK-000170201","UK-000171542","UK-000171527","UK-000172430","UK-000172793","UK-000172829","UK-000173135","UK-000172385","UK-000173192","UK-000173069","UK-000170543","UK-000171863","UK-000173540","UK-000173624","UK-000173675","UK-000173915","UK-000174098","UK-000173924","UK-000174707","UK-000174794","UK-000175625","UK-000176435","UK-000176453","UK-000176906","UK-000176900","UK-000176642","UK-000176909","UK-000174869","UK-000177512","UK-000177677","UK-000177794","UK-000177818","UK-000178088","UK-000178361","UK-000178607","UK-000178823","UK-000179663"];
        
        $mapping = ["UK-000145263" => "15500614007219","UK-000145425" => "15500614007224","UK-000146124" => "15500614007387","UK-000146148" => "15500614007426","UK-000146106" => "15500614007452","UK-000146241" => "15500614007474","UK-000146397" => "15500614007477","UK-000147225" => "15500614007538","UK-000147327" => "15500614007551","UK-000144960" => "15500614007574","UK-000145203" => "15500614007667","UK-000147564" => "15500614007683","UK-000146529" => "15500614007724","UK-000147132" => "15500614007815","UK-000147111" => "15500614007836","UK-000147459" => "15500614007854","UK-000147783" => "15500614007904","UK-000147924" => "15500614007938","UK-000149883" => "15500614008393","UK-000145164" => "15500614008447","UK-000149991" => "15500614008444","UK-000151282" => "15500614008639","UK-000151222" => "15500614008642","UK-000151234" => "15500614008646","UK-000151219" => "15500614008648","UK-000151186" => "15500614008651","UK-000151252" => "15500614008738","UK-000151477" => "15500614008733","UK-000151486" => "15500614008745","UK-000151489" => "15500614008722","UK-000151495" => "15500614008728","UK-000151501" => "15500614008729","UK-000151504" => "15500614008735","UK-000151513" => "15500614008720","UK-000151519" => "15500614008721","UK-000151471" => "15500614008724","UK-000151468" => "15500614008731","UK-000151465" => "15500614008730","UK-000151462" => "15500614008723","UK-000151456" => "15500614008732","UK-000151447" => "15500614008725","UK-000151378" => "15500614008734","UK-000151393" => "15500614008726","UK-000151396" => "15500614008727","UK-000152329" => "15500614008879","UK-000151243" => "15500614008922","UK-000152728" => "15500614009004","UK-000152725" => "15500614009002","UK-000152722" => "15500614009001","UK-000152758" => "15500614009005","UK-000151255" => "15500614009123","UK-000153703" => "15500614009289","UK-000154010" => "15500614009329","UK-000154181" => "15500614009375","UK-000154220" => "15500614009382","UK-000154748" => "15500614009546","UK-000155932" => "15500614009951","UK-000156190" => "15500614010008","UK-000156472" => "15500614010126","UK-000156823" => "15500614010190","UK-000158060" => "15500614010565","UK-000158234" => "15500614010597","UK-000158408" => "15500614010789","UK-000159932" => "15500614010791","UK-000159539" => "15500614010817","UK-000159065" => "15500614010820","UK-000160883" => "15500614010984","UK-000159506" => "15500614011270","UK-000160724" => "15500614011322","UK-000159491" => "15500614011510","UK-000162338" => "15500614011797","UK-000163598" => "15500614012309","UK-000163403" => "15500614012396","UK-000163817" => "15500614012394","UK-000163625" => "15500614012393","UK-000163559" => "15500614012392","UK-000163607" => "15500614012391","UK-000163502" => "15500614012389","UK-000163628" => "15500614012399","UK-000164006" => "15500614012469","UK-000164204" => "15500614012540","UK-000164339" => "15500614012596","UK-000164222" => "15500614012610","UK-000159128" => "15500614012671","UK-000164153" => "15500614012698","UK-000158894" => "15500614012779","UK-000160061" => "15500614012774","UK-000158963" => "15500614012840","UK-000164282" => "15500614012851","UK-000160235" => "15500614013130","UK-000165791" => "15500614013188","UK-000166409" => "15500614013386","UK-000166517" => "15500614013419","UK-000166592" => "15500614013446","UK-000166799" => "15500614013467","UK-000166685" => "15500614013484","UK-000167054" => "15500614013580","UK-000167534" => "15500614013759","UK-000169202" => "15500614014047","UK-000168098" => "15500614014105","UK-000167723" => "15500614014095","UK-000170474" => "15500614014200","UK-000170744" => "15500614014239","UK-000168470" => "15500614014253","UK-000171536" => "15500614014248","UK-000171584" => "15500614014285","UK-000172064" => "15500614014289","UK-000172118" => "15500614014309","UK-000172301" => "15500614014333","UK-000172559" => "15500614014490","UK-000170201" => "15500614014509","UK-000171542" => "15500614014671","UK-000171527" => "15500614014650","UK-000172430" => "15500614014760","UK-000172793" => "15500614015120","UK-000172829" => "15500614015240","UK-000173135" => "15500614015270","UK-000172385" => "15500614015320","UK-000173192" => "15500614015335","UK-000173069" => "15500614015355","UK-000170543" => "15500614015439","UK-000171863" => "15500614015449","UK-000173540" => "15500614015479","UK-000173624" => "15500614015558","UK-000173675" => "15500614015597","UK-000173915" => "15500614015655","UK-000174098" => "15500614015717","UK-000173924" => "15500614015747","UK-000174707" => "15500614015897","UK-000174794" => "15500614015962","UK-000175625" => "15500614016141","UK-000176435" => "15500614016299","UK-000176453" => "15500614016449","UK-000176906" => "15500614016531","UK-000176900" => "15500614016539","UK-000176642" => "15500614016579","UK-000176909" => "15500614016564","UK-000174869" => "15500614016583","UK-000177512" => "15500614016780","UK-000177677" => "15500614016785","UK-000177794" => "15500614016833","UK-000177818" => "15500614016831","UK-000178088" => "15500614016916","UK-000178361" => "15500614016996","UK-000178607" => "15500614017069","UK-000178823" => "15500614017122","UK-000179663" => "15500614017401"];

        $order_data = Order::whereIn('marketplace_reference_id',$orders)->whereSellerId(120)->get(['marketplace_reference_id','seller_id']);

        try{
            foreach($order_data as $data){
                if(isset($data)){
                    $datas = array('marketplace_reference_id' => $data->marketplace_reference_id,
                    'seller_id' => $data->seller_id,
                    'courier_id' => 16,
                    'tracking_number' => $mapping[$data->marketplace_reference_id]
                    );
                    
                    Log::info($datas);
                    $curl2 = curl_init();
    
                    curl_setopt_array($curl2, array(
                        CURLOPT_URL => env('ENDPOINT_URL').'/api/order/book/manual',
                        CURLOPT_RETURNTRANSFER => true,
                        CURLOPT_ENCODING => "",
                        CURLOPT_MAXREDIRS => 10,
                        CURLOPT_FOLLOWLOCATION => true,
                        CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                        CURLOPT_CUSTOMREQUEST => "POST",
                        CURLOPT_POSTFIELDS => json_encode($datas),
                        CURLOPT_HTTPHEADER => array(
                        "Content-Type:application/json"
                                            )
                    ));
    
                    $response2 = curl_exec($curl2);
                    $response2 = json_decode($response2,true);
                    $httpCode = curl_getinfo($curl2, CURLINFO_HTTP_CODE);
    
                    if ($httpCode == '200' && $response2['error'] == 0) {
                        Log::info('Khaadi Synergy Order has been synced with unity | '.$data->marketplace_reference_id);
                    } else {
                        Log::info('FAILED | Khaadi Synergy Order not Synced | '.$response2['message'].' | '. $data->marketplace_reference_id);
                    }
                    curl_close($curl2);
                } 
            }
        }catch(Exception $e){
            Log::info("Synergy getting orders | ".$e->getMessage());
        }
        */
    }

    public function forceDynamicSyncPage(){
        return view('dynamic');
    }

    public function forceDynamicSync(Request $request)
    {
        Log::info('Dynamic Force Sync Started');

        $order_arr = explode(',',$request->orders);

        $orders = Order::whereIn('marketplace_reference_id',$order_arr)->pluck('id')->toArray();

        // return $orders;
        $shipments = Shipment::whereSellerId('119')->whereIn('order_id',$orders)->get();
        if($shipments)
        {
            foreach($shipments as $shipment){
                event(new SendShipmentDetailsEvent($shipment));
            }
        }
        echo 'Dynamic Force Sync Completed';
    }

    public function deliveryAttemptEmail()
    {
        // BundledEmailFill::add(array('key' => config('enum.bundled_email')['DELIVERYATTEMPT'],'order_id' => 221535,'shipment_id' => 111400,'marketplace_reference_id' => '972','tracking_number' => '1001900000062','details' => null));

        $ids = [];

        $unity = explode(',',env('UNITY_POC'));
        $others = explode(',',env('KHAADI_POC'));

        $data = BundledEmail::whereSellerId(119)->where('key',config('enum.bundled_email')['DELIVERYATTEMPT'])->get();
        // return $data;
        

        if($data){

            $output = null;

            $filePath = storage_path('app/public').'/delivery_attempt/Delivery_Attempt_Khaadi_Report_'.Carbon::yesterday()->toDateString().'.csv';
            Storage::put('public/delivery_attempt/Delivery_Attempt_Khaadi_Report_'.Carbon::yesterday()->toDateString().'.csv', '');
            $output = fopen($filePath,'w') or die("Can't open php://output");
            fputcsv($output, array( 'Order_ID','Details'));

            foreach ($data as $value) {
                fputcsv($output, [ $value->marketplace_reference_id, '3rd Delivery Attempt' ]);
                array_push($ids,$value->id);
            }
            fclose($output) or die("Can't close php://output");

            Mail::raw('Daily Delivery Attempt Report', function($m) use($others,$unity, $filePath) {
                $m->to($others)
                ->bcc($unity)
                ->subject('Daily Delivery Attempt Report['.Carbon::yesterday()->toDateString().']')
                ->attach($filePath);
            });

            if(is_file($filePath)){
                unlink($filePath); 
            }

            BundledEmailEmpty::destroy($ids);
        }
    }

    public function sendCambridgeDeliveredShipmentDetailsToGtech()
    {
        Log::info('Send Cambridge Delivered Shipment Details Process Started');
        
        $shipments = Shipment::on('unity-read')->whereSellerId('119')
                            ->where('type',Null)
                            ->whereDate('created_at', '>=', Carbon::now()->subDay())
                            ->where('status','Delivered')
                            ->where('erp_sync', 0)
                            ->get();

        if($shipments) {
            
            foreach($shipments as $shipment) {
                event(new SendDeliveredShipmentDetailsToGtechEvent($shipment));
            }
            // Shipment::whereIn('id', $shipments->pluck('id'))->update(['erp_sync' => 1]);
        }
        Log::info('Send Cambridge Delivered Shipment Details Process Completed');
        echo "Send Cambridge Delivered Shipment Details Process Completed";
    }

    public function sendCambridgeReturnShipmentDetailsToGtech()
    {
        Log::info('Send Cambridge Return Shipment Details Process Started');
        
        $shipments = Shipment::on('unity-read')->whereSellerId('119')
                            ->where('type',Null)
                            ->whereDate('created_at', '>=', Carbon::now()->subDay())
                            ->where('status','Return')
                            ->where('erp_sync', 0)
                            ->get();

        if($shipments) {
            
            foreach($shipments as $shipment) {
                event(new SendReturnShipmentDetailsToGTECHEvent($shipment));
            }
            // Shipment::whereIn('id', $shipments->pluck('id'))->update(['erp_sync' => 1]);
        }
        Log::info('Send Cambridge Return Shipment Details Process Completed');
        echo "Send Cambridge Return Shipment Details Process Completed";
    }
}
