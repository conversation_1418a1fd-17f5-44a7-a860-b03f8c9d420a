<?php

namespace App\Http\Controllers;

use App\Models\Order;
use App\Models\Shipment;
use Exception;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use Mtownsend\XmlToArray\XmlToArray;

class SynergyController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }



    public function index()
    {
        $session_id = 0; $error = '';

        try {

            $curl = curl_init();
            curl_setopt_array($curl, array(
                CURLOPT_URL => env('SYNERGY_ENDPOINT'),
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_ENCODING => "",
                CURLOPT_MAXREDIRS => 10,
                CURLOPT_TIMEOUT => 60,
                CURLOPT_FOLLOWLOCATION => true,
                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                CURLOPT_CUSTOMREQUEST => "POST",
                CURLOPT_POSTFIELDS =>"<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n<soapenv:Envelope xmlns:soapenv=\"http://schemas.xmlsoap.org/soap/envelope/\" xmlns:wsdl=\"http://dcswebservice/wsdl\">\n   <soapenv:Header/>\n   <soapenv:Body>\n      <wsdl:extractOrderHeader>\n      <InterfaceSettings_1>               \n      <authenticationKey>4vbl2afvtpde73xpz9c6fbhnddb6b5zrdlad663jjj94fnf218b4</authenticationKey>\n            <clientId>KHAADI</clientId>\n            <expirationSecs>60</expirationSecs>\n            <identifier>KHAADI</identifier>\n            <language>EN_GB</language>\n            <locality>true</locality>\n            <nlsCalendar>Gregorian</nlsCalendar>\n            <oracleDateFormat>yyyymmddhh24miss</oracleDateFormat>\n            <password>fa065a214a9d4b8e95be6878ada9831</password>\n            <referenceId>KH</referenceId>\n            <serialiseMergeAccess>false</serialiseMergeAccess>\n            <siteId>CROWLANE</siteId>\n            <sqlTracing>false</sqlTracing>\n            <stationId>WEB</stationId>\n            <testMode>false</testMode>\n            <timeOutSecs>60</timeOutSecs>\n            <traceLevel>0</traceLevel>\n            <transportUrl>true</transportUrl>\n            <userId>SUPPORT</userId>\n         </InterfaceSettings_1> \n         <ExtractParameters_2>\n            <!--Zero or more repetitions:-->\n            <bindValues/>\n            <doNotExtractLines>false</doNotExtractLines>\n            <inventorySummaryParameters>\n               </inventorySummaryParameters>\n            <inventoryTransactionParameters>\n               <!--Zero or more repetitions:-->\n               </inventoryTransactionParameters>\n            <m_bHeaderExtract>true</m_bHeaderExtract>\n            <m_bUploadedFlagExtract>true</m_bUploadedFlagExtract>\n            <m_strClientId>KHAADI</m_strClientId>\n            <m_strSiteId>CROWLANE</m_strSiteId>\n            <maxRecords>10</maxRecords>\n            <nlsCalendar/>\n            <orderBy>ORDER_ID</orderBy>\n            <selectHint/>\n            <timeZoneName/>\n            <uploadedFlagCustomerControl>true</uploadedFlagCustomerControl>\n            <uploadedFlagReExtractRecords>true</uploadedFlagReExtractRecords>\n            <whereClause>STATUS = 'Shipped' AND USER_DEF_TYPE_1 IS NOT NULL AND (UPLOADED = 'N' OR UPLOADED = 'W') </whereClause>\n         </ExtractParameters_2>\n      </wsdl:extractOrderHeader>\n   </soapenv:Body>\n</soapenv:Envelope>",
                CURLOPT_HTTPHEADER => array(
                    "Content-Type: text/xml"
                ),
            ));
            $response = curl_exec($curl);
            curl_close($curl);
            $array = XmlToArray::convert($response);

            if (isset($array['env:Body']['wsdl:extractOrderHeaderResponse']['result'])) {
                
                $result = $array['env:Body']['wsdl:extractOrderHeaderResponse']['result'];
                
                if ($result['success']) {
                    
                    $session_id = $result['sessionId'];
                    
                    if (count($result['results']) > 0) {
                        
                        foreach ($result['results'] as $value) {
                            
                            $order = Order::where('seller_id',120)->where('marketplace_reference_id', $value['orderReference']);
                            
                            if ($order->exists()) {
                                
                                $order->first();

                                ///// Creating Manual shipment in unity //////
                                $data = array('marketplace_reference_id' => $value['orderReference'],
                                                'seller_id' => $order->seller_id,
                                                'courier_id' => 16,
                                                'tracking_number' => $value['userDefType1']);

                                $curl2 = curl_init();

                                curl_setopt_array($curl2, array(
                                    CURLOPT_URL => env('ENDPOINT_URL').'/api/order/book/manual',
                                    CURLOPT_RETURNTRANSFER => true,
                                    CURLOPT_ENCODING => "",
                                    CURLOPT_MAXREDIRS => 10,
                                    CURLOPT_FOLLOWLOCATION => true,
                                    CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                                    CURLOPT_CUSTOMREQUEST => "POST",
                                    CURLOPT_POSTFIELDS => $data,
                                    CURLOPT_HTTPHEADER => array(
                                    "Content-Type:application/json"
                                                        )
                                ));

                                $response2 = curl_exec($curl2);
                                $response2 = json_decode($response2);
                                $httpCode = curl_getinfo($curl2, CURLINFO_HTTP_CODE);

                                if ($httpCode == '200' && $response2['error'] == 0) {
                                    Log::info('Khaadi Synergy Order has been synced with unity | '.$value['orderReference']);
                                    echo 'Khaadi Synergy Order has been synced with unity | '.$value['orderReference'];
                                } else {
                                    Log::info('FAILED | Khaadi Synergy Order not Synced | '. $value['orderReference']);
                                    $error .= "\n FAILED | Khaadi Synergy Order not Synced | ". $value['orderReference'];
                                }
                                
                                curl_close($curl2);

                            } else {
                                $error .= "\n".$value['orderReference']." | ".$value['userDefType1']." | order not exists in unity";
                            }

                            //echo $value['userDefType1'].' | ' .$value['orderReference']. ' | ' .$value['status'].'<br>';
                        }


                    } else {
                        Log::info("Synergy getting orders | results array is empty");
                        $error .= "\n Synergy getting orders | results array is empty";
                    }
                    
                    

                } else {
                    Log::info("Synergy getting orders | result is not success");
                    $error .= "\n Synergy getting orders | result is not success";
                }
                
            } else {
                Log::info("Synergy getting orders | main result not found");
                $error .= "\n Synergy getting orders | main result not found";
            }
            
        } catch (Exception $e) {
            Log::info("Synergy getting orders | ".$e->getMessage());
            Log::info($e->getTraceAsString());
            $error .= "\n".$e->getMessage();
        }

        if ($error) {
            echo $error;
            Mail::raw($error, function ($m)  {
                $m->to('<EMAIL>')->subject('Khaadi Synergy Not Synced');
            });
        }
    }
}
