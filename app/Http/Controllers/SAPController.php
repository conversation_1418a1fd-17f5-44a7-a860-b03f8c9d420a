<?php

namespace App\Http\Controllers;

use App\Events\ReturnSalesTechnosysEvent;
use App\Models\GRNPostingReceipt;
use App\Models\Product;
use App\Models\SellerLocation;
use App\Models\Shipment;
use App\Models\StockOrder;
use App\Models\StockOrderItem;
use App\Models\StockTransferFulfillmentOrder;
use App\Service\Integration\ERP\ERPForTechnosys;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;
use Mail;

class SAPController extends Controller
{
    public function getReturnMarkedOrders($seller_id)
    {
        Log::info("Technosys | GetReturnMarkedOrders | Seller id < {$seller_id} >");
        Log::info("Technosys | GetReturnMarkedOrders | Started");

        $last_hour_date = Carbon::now()->subMinutes(5)->toDateTimeString();
        $shipments = Shipment::on('unity-read')->whereSellerId($seller_id)
            ->where('updated_at', '>=', $last_hour_date)
            ->where('erp_sync', '=', 0)
            ->whereIn('status', ["Return Received", "Cancelled"])
            ->get();

        if ($shipments->count() > 0) {
            foreach ($shipments as $shipment) {
                Log::info($shipment);
                $params['shipment'] = $shipment;
                $params['cn'] = $shipment->tracking_number;
                event(new ReturnSalesTechnosysEvent($params));
            }
        }

        Log::info("Technosys | GetReturnMarkedOrders | Completed");
        return "Technosys | GetReturnMarkedOrders | Completed";
    }
}
