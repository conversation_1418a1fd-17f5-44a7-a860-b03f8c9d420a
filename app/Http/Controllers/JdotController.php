<?php

namespace App\Http\Controllers;

use App\Models\Order;
use App\Models\Shipment;
use Exception;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use App\Helpers\JdotERP;
use App\Models\SellerLocation;
use App\Models\StockOrder;
use App\Models\StockOrderItem;
use App\Models\Product;
use App\Models\OrderTag;
use App\Models\Tag;
use App\Models\FulfillmentOrder;
use App\Models\FulfillmentOrderItem;
use App\Models\OrderItem;
use Carbon\Carbon;
use App\Events\CreatePaymentJournalEvent;
use App\Events\ReturnSalesJdotEvent;
use App\Models\StockTransferFulfillmentOrder;
use App\Models\SellerOrderConfirmation;
use App\Models\Setting;
use App\Events\UpdateOrderTagOnShopify;
use App\Events\UpdateOrderTagOnShopifyEvent;
use App\Models\GRNPostingReceipt;
use App\Events\UpdateOrderTagOnMagentoEvent;
use App\Helpers\Magento2;
use App\Models\ShipmentHistory;
use App\Events\UpdateStatusOnMagnetoForJdotEvent;
use App\Events\UpdateShipmentDetailsOnMagnetoForJdotEvent;
use App\Events\CancelOrderOnMagnetoForJdotEvent;
use App\Models\DumpDynamicGrnPosting;
use App\Models\DumpDynamicsCreateSales;
use App\Models\DumpDynamicsGrnPostingsInSql;
use App\Models\DumpDynamicsSalesInSql;
use App\Models\DumpDynamicsTransferOrdersPickedInSql;
use App\Models\DumpDynamicTransferPicking;

class JdotController extends Controller
{

    public function index()
    {
       
    }

    public function getStockOrders($seller_id){
        Log::info("Seller id ::".$seller_id);
        $locations = SellerLocation::on('unity-read')->where('seller_id',$seller_id)->get();
        if($locations->count() > 0){
            $jdotERP = new JdotERP($seller_id);
            foreach($locations as $location){
                Log::info($location->seller_reference_id);
                $jdotERP->getStockOrders($location->seller_reference_id,$seller_id);
            }
        }
        return "Stock orders request submitted";
    }

    public function getGRNPostedOrders($seller_id){
        Log::info("Seller id ::".$seller_id);
        $last_hour_date =  Carbon::now()->subMinutes(15)->toDateTimeString();
        $stock_orders = StockOrder::on('unity-read')->where('seller_id',$seller_id)->where('status',"=","Received")->where('updated_at', '>=' , $last_hour_date)->get();
        Log::info($stock_orders);
        if($stock_orders->count() > 0){
            $jdotERP = new JdotERP($seller_id);
            foreach($stock_orders as $stock_order){
                Log::info($stock_order->reference_id);
                $stock_order_items = GRNPostingReceipt::where("stock_order_id",$stock_order->id)->where("grn_receipt",0)->get();
                $item_arr = [];
                $temp_arr = [];
                foreach($stock_order_items as $item){
                    $product = Product::find($item->seller_product_id);
                    if($product){
                        if(isset($temp_arr[$product->barcode])){
                            $temp_arr[$product->barcode]['quantity'] = $temp_arr[$product->barcode]['quantity'] + $item->qty_received;
                        }else{
                            $temp_arr[$product->barcode]['sku'] = $product->SKU;
                            $temp_arr[$product->barcode]['quantity'] = $item->qty_received;
                        }
                    }
                }

                foreach($temp_arr as $key => $value){
                    $final_temp_arry = [];
                    $final_temp_arry['Barcode'] = $key;
                    $final_temp_arry['sku'] = $value['sku'];
                    $final_temp_arry['Quantity'] = $value['quantity'];
                    $item_arr[] = $final_temp_arry;

                }
                Log::info($item_arr);
                if(count($item_arr) > 0){
                    $jdotERP->getGRNPostedOrders($stock_order,$item_arr,$stock_order_items,$seller_id);
                }
            }
        }
        return "GRN Posted orders request submitted";
    }

    public function getGRNPartialPostedOrders($seller_id){
        Log::info("Seller id ::".$seller_id);
        $last_hour_date =  Carbon::now()->subMinutes(15)->toDateTimeString();
        $stock_orders = StockOrder::on('unity-read')->where('seller_id',$seller_id)->where('status',"=","Partial")->get();
        Log::info($stock_orders);
        if($stock_orders->count() > 0){
            $jdotERP = new JdotERP($seller_id);
            foreach($stock_orders as $stock_order){
                Log::info($stock_order->reference_id);
                $stock_order_items = GRNPostingReceipt::where("stock_order_id",$stock_order->id)->where("grn_receipt",0)->get();
                $item_arr = [];
                $temp_arr = [];
                foreach($stock_order_items as $item){
                    $product = Product::find($item->seller_product_id);
                    if($product){
                        if(isset($temp_arr[$product->barcode])){
                            $temp_arr[$product->barcode]['quantity'] = $temp_arr[$product->barcode]['quantity'] + $item->qty_received;
                        }else{
                            $temp_arr[$product->barcode]['sku'] = $product->SKU;
                            $temp_arr[$product->barcode]['quantity'] = $item->qty_received;
                        }
                    }
                }

                foreach($temp_arr as $key => $value){
                    $final_temp_arry = [];
                    $final_temp_arry['Barcode'] = $key;
                    $final_temp_arry['sku'] = $value['sku'];
                    $final_temp_arry['Quantity'] = $value['quantity'];
                    $item_arr[] = $final_temp_arry;

                }
                Log::info($item_arr);
                if(count($item_arr) > 0){
                    $jdotERP->getGRNPostedOrders($stock_order,$item_arr,$stock_order_items,$seller_id);
                }
            }
        }
        return "GRN Posted orders request submitted";
    }

    public function getTransferOrderesThatArePicked($seller_id){
        Log::info("Seller id ::".$seller_id);
        $last_hour_date =  Carbon::now()->subMinutes(15)->toDateTimeString();
        $stock_transfer_orders = StockTransferFulfillmentOrder::on('unity-read')->where('seller_id',$seller_id)->where('is_packed',"=",1)->where('packed_at', '>=' , $last_hour_date)->get();
        Log::info($stock_transfer_orders);
        if($stock_transfer_orders->count() > 0){
            $jdotERP = new JdotERP($seller_id);
            foreach($stock_transfer_orders as $stock_order){
                Log::info($stock_order->reference_id);
                $stock_order_items = StockOrderItem::where("stock_order_id",$stock_order->stock_order_id)->get();
                $item_arr = [];
                foreach($stock_order_items as $item){
                    $temp_arr = [];
                    // if($item->qty > $item->qty_received){
                        $product = Product::find($item->product_id);
                        $temp_arr['Barcode'] = $product->barcode;
                        $temp_arr['sku'] = $product->SKU;
                        $temp_arr['Quantity'] = $item->qty;
                        $item_arr[] = $temp_arr;
                    // }

                }
                Log::info($item_arr);
                $jdotERP->getGRNPostedOrders($stock_order,$item_arr,[],$seller_id);
            }
        }
        return "GRN Posted for Transfer Orders that are picked request submitted";
    }

    public function getUnassignedOrders($seller_id){
        Log::info("Seller id ::".$seller_id);
        Log::info('JDot :: Get Order Origin Process Started');
        
        $orders_array = [];
        $orders_count = 0;
        $total = 0;
        
        $order_ids = Order::on('unity-read')->whereSellerId($seller_id)->whereStatus('Pending')->orderBy('id','asc')->pluck('id');
        $order_ids_count = count($order_ids); 
        Log::info('JDot :: Get Order Origin Process | total '.$order_ids_count.' orders');


        if($order_ids) {
            $jdotERP = new JdotERP($seller_id);
            foreach($order_ids as $order_id) {
                $fulfilment_order = FulfillmentOrder::where('order_id',$order_id)->orderBy('id', 'DESC')->first();
                if($fulfilment_order){
                    if($fulfilment_order->status != config('enum.fulfillment_order_status')['OPEN']){
                        array_push($orders_array, $order_id);
                        $orders_count++;
                    }
                }else{
                    array_push($orders_array, $order_id);
                    $orders_count++;
                }
                $total++;

                if($orders_count == env('D365_GET_BULK_ORDER_ORIGIN_SIZE', 100) || $total == $order_ids_count) {
                    $jdotERP->getOrderAssignedLocations($orders_array,$seller_id);

                    $orders_count = 0;
                    $orders_array = [];
                }
            }
        }
        Log::info('JDot :: Get Order Origin Process Ended');
        return "JDot :: Get Order Origin Process Completed";
    }

    public function getFulfilmentRejectedOrders($seller_id){
        Log::info("Seller id ::".$seller_id);
        Log::info('JDot :: getFulfilmentRejectedOrders Started');
        
        $orders_array = [];
        $orders_count = 0;
        $total = 0;
        
        $fulfilment_orders = FulfillmentOrder::on('unity-read')->whereSellerId($seller_id)->where('status','!=',config('enum.fulfillment_order_status')['OPEN'])->where('erp_sync', '!=' ,config('enum.fulfillment_erp_sync_status')['REJECTED_SYNCED'])->get();
        if($fulfilment_orders) {
            $jdotERP = new JdotERP($seller_id);
            foreach($fulfilment_orders as $fufulfilment_order) {
                Log::info($fufulfilment_order);

                    $order = Order::where('id',$fufulfilment_order->order_id)->value('marketplace_reference_id');
                    $location_id = $fufulfilment_order->seller_location_id;
                    $location_reference = SellerLocation::where('id',$location_id)->value('seller_reference_id');
                    $fulfilment_order_items = FulfillmentOrderItem::where('fulfillment_order_id',$fufulfilment_order->id)->get();
                    $item_arr = [];
                    foreach($fulfilment_order_items as $fulfilment_order_item){
                        $temp_arr = [];
                        $order_item = OrderItem::find($fulfilment_order_item->order_item_id);
                        $product = Product::find($order_item->product_id);
                        $temp_arr['sku'] = $order_item->SKU;
                        $temp_arr['Barcode'] = $product->barcode;
                        $temp_arr['Quantity'] = $order_item->quantity;
                        $item_arr[] = $temp_arr;
                    }
                    Log::info($order);
                    Log::info($location_reference);
                    Log::info($item_arr);
                    $jdotERP->rejectFulfilment($fufulfilment_order,$order,$location_reference,$item_arr);

            }
        }
        Log::info('JDot :: getFulfilmentRejectedOrders Ended');
        return "JDot ::getFulfilmentRejectedOrders Completed";
    }

    public function getCODRecievedOrders($seller_id){
        Log::info("Seller id ::".$seller_id);
        Log::info('JDot :: getCODRecievedOrders Started');
        
        $cod_received_at =  Carbon::now()->toDateString();
        $last_hour_date =  Carbon::now()->subMinutes(15)->toDateTimeString();
        $shipments = Shipment::on('unity-read')->whereSellerId($seller_id)
                    ->where('cod', '>' , 0)
                    ->where('cod_received', 1)
                    ->where('updated_at', '>=' , $last_hour_date)
                    ->where('status', "Delivered")
                    ->get();



        if($shipments->count() > 0) {
            $jdotERP = new JdotERP($seller_id);
            $result_arr = [];
            $cns_arr = [];
            $date = "";
            $arr = [];
            $courier = 0;
            $data = [];

            if($seller_id == env('JDOT_MAGENTO_SELLER_ID')){
                $jdotMagentoAPItoken = Setting::where('seller_id', $seller_id)->where('key', 'jdotMagentoAPItoken')->first();
                $WC = Setting::where('seller_id', $seller_id)->where('key', 'WC')->first();
            }

            foreach($shipments as $shipment) {
                Log::info($shipment);
                if($courier != $shipment->courier_id){
                    $arr = [];
                    $courier = $shipment->courier_id;
                }
                array_push($arr,$shipment->tracking_number);
                $cod_received_at = Carbon::createFromFormat('Y-m-d H:i:s', $shipment->cod_received_at)
                                    ->format('Y/m/d');
                $result_arr['courier'][$shipment->courier_id][$cod_received_at] = $arr;    
                $data['cn_mapping'][$shipment->tracking_number] = $shipment->order_id;
               // event(new CreatePaymentJournalEvent($shipment,$jdotERP));


               // This is to push status update to Magento
               if($seller_id == env('JDOT_MAGENTO_SELLER_ID')){
                if($WC){
                    // $token = "yj6oli4sx84fic97nn35qujd31j482of";
                    $token = $jdotMagentoAPItoken->value;
    
                    Log::info("The token generated by Magento 2");
                    Log::info($token);
                    $data['url'] = $WC->value;
                    $data['magento_token'] = $token;
    
                    $order = Order::find($shipment->order_id);
                    $data['magento_order_id'] = $order->marketplace_id;
                    $data['shipment_id'] = $shipment->id;

                    $comment = "COD of the shipment ".$shipment->tracking_number." has been received and the status is synced to storefront";
                    $data['comment'] =  $comment;
                    $data['magento_order_id'] = $order->marketplace_id;
                    $data['unity_order_id'] = $order->id;
                    $data['magento_token'] = $token;
                    $data['order_status'] =  "payment_received";

                    event(new UpdateStatusOnMagnetoForJdotEvent($data));
                }
            }

            }
            foreach($result_arr['courier'] as $keyA => $valueA){
                foreach($valueA as $key => $value){

                    $data['seller_id'] = $seller_id;
                    $data['cn'] = $value;
                    $data['date'] = $key;
                    $data['courier_id'] =  $keyA;

                    event(new CreatePaymentJournalEvent($data,$jdotERP));
                    Log::info($data);
    
                }

            }
        }

        
        Log::info('JDot :: getCODRecievedOrders Ended');
        return "JDot ::getCODRecievedOrders Completed";
    }

    public function getReturnMarkedOrders_OLD($seller_id){
        Log::info("Seller id ::".$seller_id);
        Log::info('JDot :: getReturnMarkedOrders Started');
        
        $last_hour_date =  Carbon::now()->subMinutes(5)->toDateTimeString();
        $shipments = Shipment::on('unity-read')->whereSellerId($seller_id)
                    ->where('updated_at', '>=' , $last_hour_date)
                    ->where('status', "Return Received")
                    ->get();
        if($shipments->count() > 0) {
            $jdotERP = new JdotERP($seller_id);       
            $date = [];
            
            foreach($shipments as $shipment) {
                Log::info($shipment);
                if($shipment->seller_return_location_id !=0){
                    $return_location_id = $shipment->seller_return_location_id;
                }else{
                    $return_location_id = $shipment->seller_location_id;
                }
                $data['cn'] =  $shipment->tracking_number;
                $data['order_id'] =  $shipment->order_id;
                $data['location'] =  SellerLocation::where('id',$return_location_id)->value('seller_reference_id');
                $data['fbr_return_invoice_no'] =  $shipment->fbr_return_invoice_no;

               event(new ReturnSalesJdotEvent($data,$jdotERP));
            }
  
        }

        
        Log::info('JDot :: getReturnMarkedOrders Ended');
        return "JDot ::getReturnMarkedOrders Completed";
    }

    public function getReturnMarkedOrders($seller_id){
        Log::info("Seller id ::".$seller_id);
        Log::info('JDot :: getReturnMarkedOrders Started');
        
        $last_hour_date =  Carbon::now()->subMinutes(15)->toDateTimeString();
        $shipments = Shipment::on('unity-read')->whereSellerId($seller_id)
                    ->where('updated_at', '>=' , $last_hour_date)
                    ->where('erp_sync', '=' , 0)
                    ->where(function ($query) {
                        $query->where('type', '!=', 'reverse')
                              ->orWhereNull('type');
                      })
                    ->where('status', "Return Received")
                    ->get();
        if($shipments->count() > 0) {
            $jdotERP = new JdotERP($seller_id);       
            $date = [];
            
            foreach($shipments as $shipment) {
                Log::info($shipment);

                $params['shipment'] =  $shipment;
                $params['cn'] =  $shipment->tracking_number;

               event(new ReturnSalesJdotEvent($params,$jdotERP));
            }
  
        }

        
        Log::info('JDot :: getReturnMarkedOrders Ended');
        return "JDot ::getReturnMarkedOrders Completed";
    }

    public function getConfirmedMarkedOrdersForShopify($seller_id){
        Log::info("Seller id ::".$seller_id);
        Log::info('Almirah :: getConfirmedMarkedOrdersForShopify Started');
        
        $last_1_min_date =  Carbon::now()->subMinutes(5)->toDateTimeString();
        Log::info($last_1_min_date);

        $order_ids = Order::on('unity-read')->whereSellerId($seller_id)->whereStatus('Pending')->orderBy('id','asc')->pluck('id');
        $order_ids_count = count($order_ids); 
        Log::info('Almirah :: getConfirmedMarkedOrdersForShopify | total '.$order_ids_count.' orders');


        if($order_ids) {
            foreach($order_ids as $order_id) {
                $order_tag = OrderTag::where('order_id', $order_id)->where('updated_at', '>=' , $last_1_min_date)->first();
                if($order_tag){
                    Log::info('Almirah :: getConfirmedMarkedOrdersForShopify | Order :: '.$order_id.' | Order Tag is  '.$order_tag->tag_id);
                    $tag = Tag::find($order_tag->tag_id);
                    if($tag && $tag->value == "Confirmed"){
                        Log::info('Almirah :: getConfirmedMarkedOrdersForShopify | Order :: '.$order_id.' |  Tag value is  '.$tag->value);
                        $order = Order::find($order_id);
                        $accessToken = Setting::where('seller_id', $seller_id)->where('key', 'ShopifyToken')->first();
                        $WC = Setting::where('seller_id', $seller_id)->where('key', 'WC')->first();
                        if($accessToken){
                            $data['access_token'] = $accessToken->value;
                            $data['url'] = $WC->value;
                            $data['shopify_order_id'] = $order->marketplace_id;
                            $data['order_tag'] =  "verified";
            
                            event(new UpdateOrderTagOnShopifyEvent($data));
                        }
                    }
                }
            }
        }
        
        Log::info('Almirah :: getConfirmedMarkedOrdersForShopify Ended');
        return "Almirah ::getConfirmedMarkedOrdersForShopify Completed";
    }

    public function getConfirmedMarkedOrdersForMagento($seller_id){
        Log::info("Seller id ::".$seller_id);
        Log::info('JDot :: getConfirmedMarkedOrdersForMagento Started');
        
        $last_1_min_date =  Carbon::now()->subMinutes(5)->toDateTimeString();
        Log::info($last_1_min_date);

        $order_confirmations = SellerOrderConfirmation::on('unity-read')->whereSellerId($seller_id)
                    ->where('confirmed_at', '>=' , $last_1_min_date)
                    ->get();
        if($order_confirmations->count() > 0) {
            $date = [];
            
            foreach($order_confirmations as $order_confirmation) {
                Log::info($order_confirmation->order_id);
                $order = Order::find($order_confirmation->order_id);
                // $magentoAPIpassword = Setting::where('seller_id', $seller_id)->where('key', 'magentoAPIPassword')->first();
                // $magentoAPIusername = Setting::where('seller_id', $seller_id)->where('key', 'magentoAPIUsername')->first();
                $jdotMagentoAPItoken = Setting::where('seller_id', $seller_id)->where('key', 'jdotMagentoAPItoken')->first();

                $WC = Setting::where('seller_id', $seller_id)->where('key', 'WC')->first();
                if($WC){
                    // $token = Magento2::getToken($WC->value,$magentoAPIusername->value,$magentoAPIpassword->value);
                    $token = $jdotMagentoAPItoken->value;

                    Log::info("The token generated by Magento 2");
                    Log::info($token);
                    $data['url'] = $WC->value;
                    $data['magento_token'] = $token;
                    $data['magento_order_id'] = $order->marketplace_id;
                    $data['order_status'] =  "s_order_confirmed";
    
                   event(new UpdateOrderTagOnMagentoEvent($data));
                }
             
            }
  
        }

        
        Log::info('JDot :: getConfirmedMarkedOrdersForMagento Ended');
        return "JDot ::getConfirmedMarkedOrdersForMagento Completed";
    }

    public function getOrdersWithOrderTagUnconfirmed1($seller_id){
        Log::info("Seller id ::".$seller_id);
        Log::info('JDot :: getOrdersWithOrderTagUnconfirmed1 Started');
        
        $last_5_min_date =  Carbon::now()->subMinutes(5)->toDateTimeString();
        Log::info($last_5_min_date);

        $orders = Order::join('order_tags', 'orders.id', '=', 'order_tags.order_id')
        ->where('orders.seller_id', $seller_id)
        ->where('orders.status', 'Pending')
        ->where('order_tags.tag_id', 35843)
        ->where('order_tags.updated_at', '>=', $last_5_min_date)
        ->select('orders.*') // This selects all columns from the orders table.
        ->get();

        if($orders->count() > 0) {
            $date = [];
            
            foreach($orders as $order) {
                Log::info($order->id);
                $jdotMagentoAPItoken = Setting::where('seller_id', $seller_id)->where('key', 'jdotMagentoAPItoken')->first();

                $WC = Setting::where('seller_id', $seller_id)->where('key', 'WC')->first();
                if($WC){
                    $token = $jdotMagentoAPItoken->value;

                    Log::info("The token generated by Magento 2");
                    Log::info($token);
                    $data['url'] = $WC->value;
                    $data['magento_token'] = $token;
                    $data['magento_order_id'] = $order->marketplace_id;
                    $data['order_status'] =  "unconfirmed_1";
    
                   event(new UpdateOrderTagOnMagentoEvent($data));
                }
             
            }
  
        }

        
        Log::info('JDot :: getOrdersWithOrderTagUnconfirmed1 Ended');
        return "JDot ::getOrdersWithOrderTagUnconfirmed1 Completed";
    }

    public function getOrdersWithOrderTagUnconfirmed2($seller_id){
        Log::info("Seller id ::".$seller_id);
        Log::info('JDot :: getOrdersWithOrderTagUnconfirmed2 Started');
        
        $last_5_min_date =  Carbon::now()->subMinutes(5)->toDateTimeString();
        Log::info($last_5_min_date);

        $orders = Order::join('order_tags', 'orders.id', '=', 'order_tags.order_id')
        ->where('orders.seller_id', $seller_id)
        ->where('orders.status', 'Pending')
        ->where('order_tags.tag_id', 35844)
        ->where('order_tags.updated_at', '>=', $last_5_min_date)
        ->select('orders.*') // This selects all columns from the orders table.
        ->get();

        if($orders->count() > 0) {
            $date = [];
            
            foreach($orders as $order) {
                Log::info($order->id);
                $jdotMagentoAPItoken = Setting::where('seller_id', $seller_id)->where('key', 'jdotMagentoAPItoken')->first();

                $WC = Setting::where('seller_id', $seller_id)->where('key', 'WC')->first();
                if($WC){
                    $token = $jdotMagentoAPItoken->value;

                    Log::info("The token generated by Magento 2");
                    Log::info($token);
                    $data['url'] = $WC->value;
                    $data['magento_token'] = $token;
                    $data['magento_order_id'] = $order->marketplace_id;
                    $data['order_status'] =  "unconfirmed_2";
    
                   event(new UpdateOrderTagOnMagentoEvent($data));
                }
             
            }
  
        }

        
        Log::info('JDot :: getOrdersWithOrderTagUnconfirmed2 Ended');
        return "JDot ::getOrdersWithOrderTagUnconfirmed2 Completed";
    }

    public function getOrdersWithOrderTagUnconfirmed3($seller_id){
        Log::info("Seller id ::".$seller_id);
        Log::info('JDot :: getOrdersWithOrderTagUnconfirmed3 Started');
        
        $last_5_min_date =  Carbon::now()->subMinutes(5)->toDateTimeString();
        Log::info($last_5_min_date);

        $orders = Order::join('order_tags', 'orders.id', '=', 'order_tags.order_id')
        ->where('orders.seller_id', $seller_id)
        ->where('orders.status', 'Pending')
        ->where('order_tags.tag_id', 35845)
        ->where('order_tags.updated_at', '>=', $last_5_min_date)
        ->select('orders.*') // This selects all columns from the orders table.
        ->get();

        if($orders->count() > 0) {
            $date = [];
            
            foreach($orders as $order) {
                Log::info($order->id);
                $jdotMagentoAPItoken = Setting::where('seller_id', $seller_id)->where('key', 'jdotMagentoAPItoken')->first();

                $WC = Setting::where('seller_id', $seller_id)->where('key', 'WC')->first();
                if($WC){
                    $token = $jdotMagentoAPItoken->value;

                    Log::info("The token generated by Magento 2");
                    Log::info($token);
                    $data['url'] = $WC->value;
                    $data['magento_token'] = $token;
                    $data['magento_order_id'] = $order->marketplace_id;
                    $data['order_status'] =  "unconfirmed_3";
    
                   event(new UpdateOrderTagOnMagentoEvent($data));
                }
             
            }
  
        }

        
        Log::info('JDot :: getOrdersWithOrderTagUnconfirmed3 Ended');
        return "JDot ::getOrdersWithOrderTagUnconfirmed3 Completed";
    }

    public function getShipmentUpdates($seller_id){
        Log::info("Seller id ::".$seller_id);
        Log::info('JDot :: getShipmentUpdates Started');

        $statuses = [
            config('enum.shipment_status')['BOOKED'],
            config('enum.shipment_status')['DISPATCHED'],
            config('enum.shipment_status')['DELIVERED'],
            config('enum.shipment_status')['RETURNED'],
            config('enum.shipment_status')['READY_FOR_DISPATCH'],
            config('enum.shipment_status')['RETURNED_RECEIVED'],
            config('enum.shipment_status')['LOST'],
        ];
              
        $last_15_min_date =  Carbon::now()->subMinutes(5)->toDateTimeString();
        $shipments = Shipment::on('unity-read')->whereSellerId($seller_id)
                    ->where('updated_at', '>=' , $last_15_min_date)
                    ->whereIn('status',$statuses)
                    ->get();

        if($shipments->count() > 0) {
            // $magentoAPIpassword = Setting::where('seller_id', $seller_id)->where('key', 'magentoAPIPassword')->first();
            // $magentoAPIusername = Setting::where('seller_id', $seller_id)->where('key', 'magentoAPIUsername')->first();
            $jdotMagentoAPItoken = Setting::where('seller_id', $seller_id)->where('key', 'jdotMagentoAPItoken')->first();

            $WC = Setting::where('seller_id', $seller_id)->where('key', 'WC')->first();
            if($WC){
                // $token = "yj6oli4sx84fic97nn35qujd31j482of";
                $token = $jdotMagentoAPItoken->value;

                Log::info("The token generated by Magento 2");
                Log::info($token);
                $data['url'] = $WC->value;
                $data['magento_token'] = $token;

               foreach($shipments as $shipment) {
                    Log::info($shipment);
                    $order = Order::find($shipment->order_id);
                    $data['magento_order_id'] = $order->marketplace_id;

                    $shipment_histories = ShipmentHistory::where('shipment_id',$shipment->id)->where('storefront_sync',0)->whereIn('status',$statuses)->get();
                    if($shipment_histories->count() > 0){
                        foreach($shipment_histories as $shipment_history) {
                            $data['shipment_id'] = $shipment->id;
                            $data['shipment_history_id'] = $shipment_history->id;

                            $comment = "Tracking Update by <b>".$shipment->courier->name."</b> for <br> Tracking number : <b>".$shipment->tracking_number."</b> <br>
                            Shipment Status : <b>".$shipment_history->status."</b> ";
                            $data['comment'] =  $comment;
                            $data['magento_order_id'] = $order->marketplace_id;
                            $data['unity_order_id'] = $order->id;
                            $data['magento_token'] = $token;
                            $data['order_status'] =  config('enum.shipment_status_jdot_mapping')[$shipment_history->status];
                            
                            event(new UpdateStatusOnMagnetoForJdotEvent($data));
                            $shipment_history->storefront_sync = 1;
                            $shipment_history->save();

                        }
                    }
                }
            }


        }

        
        Log::info('JDot :: getShipmentUpdates Ended');
        return "JDot ::getShipmentUpdates Completed";
    }

    public function getShipmentsForCNUpdates($seller_id){
        Log::info("Seller id ::".$seller_id);
        Log::info('JDot :: getShipmentsForCNUpdates Started');

        $one_day_old= Carbon::now()->subDays(1);
        $shipments = Shipment::on('unity-read')->whereSellerId($seller_id)
                    ->where('updated_at', '>=' , $one_day_old)
                    ->where('storefront_sync',0)
                    ->get();

        if($shipments->count() > 0) {
            // $magentoAPIpassword = Setting::where('seller_id', $seller_id)->where('key', 'magentoAPIPassword')->first();
            // $magentoAPIusername = Setting::where('seller_id', $seller_id)->where('key', 'magentoAPIUsername')->first();
            $jdotMagentoAPItoken = Setting::where('seller_id', $seller_id)->where('key', 'jdotMagentoAPItoken')->first();

            $WC = Setting::where('seller_id', $seller_id)->where('key', 'WC')->first();
            if($WC){
                // $token = "yj6oli4sx84fic97nn35qujd31j482of";
                $token = $jdotMagentoAPItoken->value;

                Log::info("The token generated by Magento 2");
                Log::info($token);
                $data['url'] = $WC->value;
                $data['magento_token'] = $token;

               foreach($shipments as $shipment) {
                    Log::info($shipment);
                    $order = Order::find($shipment->order_id);
                    $data['shipment_id'] = $shipment->id;             
                    $data['magento_order_id'] = $order->marketplace_id;
                    $data['unity_order_id'] = $order->id;
                    $data['magento_token'] = $token;
                    $data['tracking_number'] = $shipment->tracking_number;
                    $data['courier_id'] = $shipment->courier_id;

                    event(new UpdateShipmentDetailsOnMagnetoForJdotEvent($data));

                }
            }


        }

        
        Log::info('JDot :: getShipmentsForCNUpdates Ended');
        return "JDot ::getShipmentsForCNUpdates Completed";
    }


    public function getConfirmedMarkedOrdersForShopifyBulk($seller_id){
        Log::info("Seller id ::".$seller_id);
        Log::info('Almirah :: getConfirmedMarkedOrdersForShopifyBulk Started');
        
        $order_ids = [194604,194612,194623,194624,194645,194667,194668,194671,194672,194673,194676,194678,194679,194685,194686,194689,194690,194691,194694,194695,194696,194698,194701,194702,194704,194705,194708,194714,194718,194719,194720,194721,194722,194724,194725,194726,194119,194545,194730,194732,194733,194735,194737,194739,194740,194741,194742,194743,194744,194747,194749,194750,194758,194762,194763,194764,194765,194768,194769,194772,194774,194775,194776,194778,194779,194780,194783,194784,194785,194786,194787,194788,194789,194790,194792,194791,194793,194794,194795,194796,194797,194798,194799,194800,194801,194802,194803,194804,194806,194807,194808,194809,194810,194811,194812,194814,194815,194816,194818,194819,194820,194821,194822,194823,194824,194825,194826,194827,194829,194830,194832,194833,194834,194835,194837,194838,194839,194840,194841,194842,194843,194844,194845,194846,194847,194848,194849,194850,194851,194852,194853,194856,194857,194858,194860,194861,194862,194863,194864,194865,194868,194869,194870,194871,194872,194875,194876,194877,194879,194880,194881,194882,194883,194885,194888,194889,194890,194891,194893,194897,194899,194900,194903,194904,194905,194907,194908,194909,194910,194912,194914,194915,194916,194917,194918,194919,194920,194921,194922,194923,194925,194926,194927,194930,194931,194933,194934,194935,194936,194937,194938,194939,194940,194942,194943,194944,194945,194946,194947,194948,194949,194950,194951,194952,194953,194955,194956,194958,194959,194960,194961,194962,194963,194966,194967,194969,194971,194972,194973,194975,194977,194979,194980,194981,194982,194983,194984,194985,194986,194987,194988,194990,194992,194993,194994,194995,194996,194997,194998,195000,195001,195002,195003,195004,195005,195006,195011,195014,195015,195016,195018,195019,195020,195022,195023,195024,195025,195027,195028,195029,195031,195032,195033,195036,195037,195038,195039,195040,195041,195042,195044,195045,195046,195047,195049,195050,195051,195052,195054,195055,195056,195057,195058,195059,195060,195061,195063,195064,195066,195067,195068,195069,195070,195071,195072,195073,195074,195075,195076,195077,195078,195079,195081,195082,195083,195084,195085,195086,195087,195088,195089,195090,195092,195093,195095,195096,195097,195098,195102,195103,195106,195107,195108,195109,195110,195111,195112,195114,195115,195116,195117,195118,195119,195121,195122,195124,195125,195126,195127,195128,195129,195130,195132,195134,195135,195136,195137,195139,195140,195142,195143,195144,195146,195147,195148,195149,195150,195151,195153,195154,195157,195159,195160,195161,195162,195163,195165,195166,195167,195168,195173,195174,195175,195176,195178,195179,195181,195182,195183,195184,195185,195186,195187,195189,195192,195194,195195,195197,195198,195200,195201,195203,195204,195205,195207,195208,195209,195210,195211,195212,195213,195214,195215,195217,195218,195220,195221,195222,195223,195224,195225,195226,195227,195229,195232,195234,195235,195236,195237,195241,195242,195243,195244,195245,195247,195248,195249,195250,195251,195254,195255,195256,195257,195258,195259,195262,195263,195264,195265,195266,195267,195268,195269,195270,195271,195272,195273,195274,195276,195277,195278,195279,195280,195281,195282,195284,195285,195287,195289,195290,195291,195292,195293,195294,195295,195296,195297,195303,195304,195305,195306,195307,195308,195309,195311,195312,195313,195314,195315,195316,195317,195318,195319,195320,195322,195323,195324,195325,195327,195328,195329,195330,195332,195333,195334,195336,195337,195338,195339,195340,195342,195343,195344,195345,195346,195347,195349,195351,195352,195353,195354,195356,195357,195358,195359,195360,195361,195362,195363,195366,195367,195369,195370,195371,195372,195373,195374,195375,195376,195378,195379,195380,195381,195382,195383,195384,195385,195386,195387,195388,195389,195390,195391,195397,195398,195399,195400,195401,195402,195403,195404,195406,195407,195408,195409,195410,195412,195413,195414,195415,195417,195419,195421,195423,195424,195425,195426,195427,195428,195429,195430,195431,195432,195433,195434,195435,195437,195438,195439,195440,195441,195442,195443,195445,195446,195448,195449,195451,195452,195453,195454,195459,195461,195462,195463,195464,195466,195467,195468,195469,195470,195471,195473,195474,195476,195477,195479,195481,195482,195484,195485,195486,195488,195489,195492,195494,195495,195496,195497,195498,195499,195500,195501,195503,195504,195505,195506,195507,195508,195509,195510,195511,195512,195514,195515,195516,195518,195520,195524,195525,195526,195527,195528,195529,195530,195531,195532,195533,195534,195535,195536,195537,195538,195539,195540,195543,195545,195546,195547,195548,195549,195550,195554,195555,195556,195558,195560,195561,195562,195563,195565,195566,195567,195568,195569,195570,195571,195576,195578,195579,195580,195581,195583,195584,195586,195587,195588,195589,195590,195591,195592,195593,195595,195596,195598,195599,195600,195601,195604,195606,195607,195608,195609,195610,195611,195613,195615,195617,195618,195621,195622,195624,195625,195628,195629,195630,195631,195632,195634,195635,195636,195637,195638,195639,195640,195641,195642,195643,195644,195645,195646,195647,195648,195649,195650,195651,195652,195653,195654,195656,195657,195658,195659,195660,195661,195662,195663,195664,195667,195669,195670,195679,195680,195683,195684,195688,195689,195691,195692,195693,195694,195698,195699,194592,195701,195702,195703,195704,195705,195707,195708,195710,194536,195716,195717,195718,195719,195721,195722,195723,195725,195726,195727,195729,195731,195732,195733,195734,195735,195736,195738,195739,195740,195741,195742,195743,195744,195745,195746,195747,195748,195749,195750,195751,195752,195753,195754,195756,195757,195758,195759,195760,195762];
        if($order_ids) {
            $counter = 1;
            foreach($order_ids as $order_id) {
                $order = Order::find($order_id);
                
                if($order){
                    Log::info("Almirah :: getConfirmedMarkedOrdersForShopifyBulk :: The counter is :: ".$counter." The order id is :: ".$order->id);
                    $counter++;
                    $accessToken = Setting::where('seller_id', $seller_id)->where('key', 'ShopifyToken')->first();
                    $WC = Setting::where('seller_id', $seller_id)->where('key', 'WC')->first();
                    if($accessToken){
                        $data['access_token'] = $accessToken->value;
                        $data['url'] = $WC->value;
                        $data['shopify_order_id'] = $order->marketplace_id;
                        $data['order_tag'] =  "verified";
        
                        // event(new UpdateOrderTagOnShopifyEvent($data));
                    }
                }else{
                    Log::info("Almirah :: getConfirmedMarkedOrdersForShopifyBulk Order ID incorrect :: ".$order_id);
                }

            }
        }
        Log::info('Almirah :: getConfirmedMarkedOrdersForShopifyBulk Order Total count '.$counter);

        Log::info('Almirah :: getConfirmedMarkedOrdersForShopifyBulk Ended');
        return "Almirah ::getConfirmedMarkedOrdersForShopifyBulk Completed";
    }

    public function getAllCODRecievedOrders($seller_id){
        Log::info("Seller id ::".$seller_id);
        Log::info('JDot :: getAllCODRecievedOrders Started');
        
        $cod_received_at =  Carbon::now()->toDateString();
        $last_hour_date =  Carbon::now()->subMinutes(5)->toDateTimeString();
        $shipments = Shipment::on('unity-read')->whereSellerId($seller_id)
                    ->where('cod', '>' , 0)
                    ->where('cod_received', 1)
                    ->where('status', "Delivered")
                    ->get();
        
        Log::info('JDot :: getAllCODRecievedOrders Shipments count ::'.$shipments->count());

        if($shipments->count() > 0) {
            $jdotERP = new JdotERP($seller_id);
            $result_arr = [];
            $cns_arr = [];
            $date = "";
            $arr = [];
            $courier = 0;
            $data = [];
            $counter = 1;

            if($seller_id == env('JDOT_MAGENTO_SELLER_ID')){
                $jdotMagentoAPItoken = Setting::where('seller_id', $seller_id)->where('key', 'jdotMagentoAPItoken')->first();
                $WC = Setting::where('seller_id', $seller_id)->where('key', 'WC')->first();
            }

            foreach($shipments as $shipment) {
                Log::info($counter);
                Log::info($shipment->id);
                if($courier != $shipment->courier_id){
                    $arr = [];
                    $courier = $shipment->courier_id;
                }
                array_push($arr,$shipment->tracking_number);
                $cod_received_at = Carbon::createFromFormat('Y-m-d H:i:s', $shipment->cod_received_at)
                                    ->format('Y/m/d');
                $result_arr['courier'][$shipment->courier_id][$cod_received_at] = $arr;    
                $data['cn_mapping'][$shipment->tracking_number] = $shipment->order_id;
               // event(new CreatePaymentJournalEvent($shipment,$jdotERP));
               $counter++;

               // This is to push status update to Magento
               if($seller_id == env('JDOT_MAGENTO_SELLER_ID')){
                    if($WC){
                        // $token = "yj6oli4sx84fic97nn35qujd31j482of";
                        $token = $jdotMagentoAPItoken->value;
        
                        Log::info("The token generated by Magento 2");
                        Log::info($token);
                        $data['url'] = $WC->value;
                        $data['magento_token'] = $token;
        
                        $order = Order::find($shipment->order_id);
                        $data['magento_order_id'] = $order->marketplace_id;
                        $data['shipment_id'] = $shipment->id;

                        $comment = "COD of the shipment ".$shipment->tracking_number." has been received and the status is synced to storefront";
                        $data['comment'] =  $comment;
                        $data['magento_order_id'] = $order->marketplace_id;
                        $data['unity_order_id'] = $order->id;
                        $data['magento_token'] = $token;
                        $data['order_status'] =  "Payment received";

                        event(new UpdateStatusOnMagnetoForJdotEvent($data));
                    }
                }
            }
            $counter = 1;

            foreach($result_arr['courier'] as $keyA => $valueA){
                foreach($valueA as $key => $value){
                    Log::info('JDot :: getAllCODRecievedOrders payment event counter ::'.$counter);


                    $data['seller_id'] = $seller_id;
                    $data['cn'] = $value;
                    $data['date'] = $key;
                    $data['courier_id'] =  $keyA;

                    event(new CreatePaymentJournalEvent($data,$jdotERP));
                    $counter++;
    
                }

            }
        }

        
        Log::info('JDot :: getAllCODRecievedOrders Ended');
        return "JDot ::getAllCODRecievedOrders Completed";
    }

    public function getCancelledOrders($seller_id){
        Log::info("Seller id ::".$seller_id);
        Log::info('JDot :: getCancelledOrders Started');

        $last_15_min_date =  Carbon::now()->subMinutes(15)->toDateTimeString();
        $orders = Order::on('unity-read')->whereSellerId($seller_id)
                    ->where('updated_at', '>=' , $last_15_min_date)
                    ->whereIn('status',"Cancelled")
                    ->get();

        if($orders->count() > 0) {
            // $magentoAPIpassword = Setting::where('seller_id', $seller_id)->where('key', 'magentoAPIPassword')->first();
            // $magentoAPIusername = Setting::where('seller_id', $seller_id)->where('key', 'magentoAPIUsername')->first();
            $jdotMagentoAPItoken = Setting::where('seller_id', $seller_id)->where('key', 'jdotMagentoAPItoken')->first();

            $WC = Setting::where('seller_id', $seller_id)->where('key', 'WC')->first();
            if($WC){
                // $token = "yj6oli4sx84fic97nn35qujd31j482of";
                $token = $jdotMagentoAPItoken->value;

                Log::info("The token generated by Magento 2");
                Log::info($token);
                $data['url'] = $WC->value;
                $data['magento_token'] = $token;

               foreach($orders as $order) {
                    Log::info($order);
                    $data['magento_order_id'] = $order->marketplace_id;


                    $comment = "Order has been marked as Cancelled through Unity";
                    $data['comment'] =  $comment;
                    $data['magento_order_id'] = $order->marketplace_id;
                    $data['unity_order_id'] = $order->id;
                    $data['magento_token'] = $token;
                    $data['order_status'] =  "Canceled";

                    event(new CancelOrderOnMagnetoForJdotEvent($data));

                }
            }


        }

        
        Log::info('JDot :: getCancelledOrders Ended');
        return "JDot ::getCancelledOrders Completed";
    }


    public function getPickedOrders($seller_id){
        Log::info("Seller id ::".$seller_id);
        Log::info('JDot :: getPickedOrders Started');

        $last_15_min_date =  Carbon::now()->subMinutes(5)->toDateTimeString();

        $fulfilment_orders = FulfillmentOrder::on('unity-read')->where('seller_id',$seller_id)->where('sub_user_assigned_date', '>=' , $last_15_min_date)->whereNotNull('sub_user_id')->where('status',0)->get();
        if($fulfilment_orders->count() > 0){
            $jdotMagentoAPItoken = Setting::where('seller_id', $seller_id)->where('key', 'jdotMagentoAPItoken')->first();

            $WC = Setting::where('seller_id', $seller_id)->where('key', 'WC')->first();
            if($WC){
                // $token = "yj6oli4sx84fic97nn35qujd31j482of";
                $token = $jdotMagentoAPItoken->value;

                Log::info("The token generated by Magento 2");
                Log::info($token);


                foreach($fulfilment_orders as $fulfilment_order){

                    $data['url'] = $WC->value;
                    $data['magento_token'] = $token;
                    $order = Order::find($fulfilment_order->order_id);
                    if($order){
                        Log::info($order);
                        $data['magento_order_id'] = $order->marketplace_id;
        
        
                        $comment = "Order has been assigned to Picker";
                        $data['comment'] =  $comment;
                        $data['magento_order_id'] = $order->marketplace_id;
                        $data['unity_order_id'] = $order->id;
                        $data['magento_token'] = $token;
                        $data['order_status'] =  "under_inprocess";
        
                        event(new UpdateStatusOnMagnetoForJdotEvent($data));
                    }
                }

            }

        }
        Log::info('JDot :: getPickedOrders Ended');
        return "JDot ::getPickedOrders Completed";
    }

    public function getReturnMarkedOrdersInBulk($seller_id){
        Log::info("Seller id ::".$seller_id);
        Log::info('JDot :: getReturnMarkedOrdersInBulk Started');
        
       
        $orders = [3332298,3538885,3596042,3607404,3607515,3610301,3612600,3614940,3615179,3623731,3626810,3630162,3630794,3641740,3641991,3642106,3644091,3645395,3646822,3656888,3671177,3674572,3677257,3678873,3681369,3715167,3751300,3753450,3761663,3762584,3762596,3762707,3763183,3763578,3763748,3763810,3763897,3763922,3764266,3764309,3764535,3764892,3765072,3765260,3765977,3766312,3776527,3778546,3796249,3797508,3814405,3815211,3816075,3820961,3827239,3827298,3827382,3827448,3827468,3828150,3828280,3828304,3828924,3829132,3829225,3829275,3829287,3829334,3829336,3831278,3831690,3831762,3831989,3838009,3838169,3842015,3842850,3843092,3843438,3844387,3845681,3845885,3846160,3847613,3847824,3847889,3848423,3848507,3848907,3849509,3849826,3849921,3851545,3851765,3851846,3852025,3852931,3853274,3853300,3853596,3853999,3854119,3854638,3854707,3854952,3855018,3855028,3855445,3855632,3855639,3855663,3856400,3856562,3856595,3856691,3856888,3857134,3857160,3857410,3857541,3857603,3857708,3857791,3857887,3857935,3857938,3857996,3859163,3859407,3859703,3859715,3859779,3860225,3860255,3860630,3860833,3860840,3860998,3861004,3861128,3861429,3861471,3861580,3861598,3861946,3862084,3862150,3862552,3862857,3863026,3863080,3863123,3863314,3863356,3863484,3863560,3863833,3863842,3863922,3864052,3864129,3864167,3864178,3864256,3864356,3864398,3864432,3864777,3864857,3865042,3865476,3865514,3865584,3865877,3865954,3866818,3866906,3866960,3866999,3867056,3867127,3867397,3867862,3868459,3868629,3868775,3868804,3868923,3869001,3869044,3869061,3869123,3869132,3869256,3869283,3869302,3869529,3869545,3870089,3870734,3870768,3870796,3871561,3871767,3872108,3872119,3872184,3872511,3872770,3872798,3874019,3875024,3875498,3875539,3875706,3875738,3875763,3875779,3875831,3875924,3875929,3875943,3876077,3876103,3876194,3876411,3877105,3877283,3877301,3877355,3877359,3877367,3877582,3877715,3877758,3877791,3877888,3878069,3878153,3878285,3878386,3878408,3879610,3879830,3879939,3880239,3880570,3880633,3880769,3881021,3881078,3881496,3881688,3881708,3885624,3891359,3894226,3895272,3903224,3903518];
        $order_counter = 1;
        $shipment_counter = 1;
        $jdotERP = new JdotERP($seller_id);   
        foreach($orders as $order_id){
            $shipment = Shipment::where('order_id', $order_id)->whereIn('status', ['Return Received','Return'])->first();
            if($shipment){
                \Log::info("getReturnMarkedOrdersInBulk :: shipment counter ".$shipment_counter." ::  shipment tacking number is ".$shipment->tracking_number);
                $shipment_counter++;
                $params['shipment'] =  $shipment;
                $params['cn'] =  $shipment->tracking_number;

                event(new ReturnSalesJdotEvent($params,$jdotERP));
            }
        }

        
        Log::info('JDot :: getReturnMarkedOrdersInBulk Ended');
        return "JDot ::getReturnMarkedOrdersInBulk Completed";
    }

    public function getAllSalesAndPushToDynamics($seller_id){
        Log::info("Seller id ::".$seller_id);
        Log::info('JDot :: getAllSalesAndPushToDynamics Started');
       
        $all_data = DumpDynamicsCreateSales::where('seller_id',$seller_id)->where('is_synced_sql_db',false)->get();
        if(count($all_data) > 0){
            foreach($all_data as $data){
                DumpDynamicsSalesInSql::createOrUpdate($data);
                $data->is_synced_sql_db = true;
                $data->save();
            }
        }
        
        Log::info('JDot :: getAllSalesAndPushToDynamics Ended');
        return "JDot ::getAllSalesAndPushToDynamics Completed";
    }

    public function getGrnPostingsAndPushToDynamics($seller_id){
        Log::info("Seller id ::".$seller_id);
        Log::info('JDot :: getGrnPostingsAndPushToDynamics Started');
       
        $all_data = DumpDynamicGrnPosting::where('seller_id',$seller_id)->where('is_synced_sql_db',false)->get();
        if(count($all_data) > 0){
            foreach($all_data as $data){
                DumpDynamicsGrnPostingsInSql::createOrUpdate($data);
                $data->is_synced_sql_db = true;
                $data->save();
            }
        }
        
        Log::info('JDot :: getGrnPostingsAndPushToDynamics Ended');
        return "JDot ::getGrnPostingsAndPushToDynamics Completed";
    }

    public function getTransferOrdersPickedAndPushToDynamics($seller_id){
        Log::info("Seller id ::".$seller_id);
        Log::info('JDot :: getTransferOrdersPickedAndPushToDynamics Started');
       
        $all_data = DumpDynamicTransferPicking::where('seller_id',$seller_id)->where('is_synced_sql_db',false)->get();
        if(count($all_data) > 0){
            foreach($all_data as $data){
                DumpDynamicsTransferOrdersPickedInSql::createOrUpdate($data);
                $data->is_synced_sql_db = true;
                $data->save();
            }
        }
        
        Log::info('JDot :: getTransferOrdersPickedAndPushToDynamics Ended');
        return "JDot ::getTransferOrdersPickedAndPushToDynamics Completed";
    }
    
    public function bulkShipmentStatusSyncMagento($seller_id){
        Log::info("Seller id ::".$seller_id);
        Log::info('JDot :: bulkShipmentStatusSyncMagento Started');
        $orders = [3332298,3538885];

        foreach($orders as $order_id){
            $shipment = Shipment::where('order_id', $order_id)->where('status','!=','Cancelled')->first();
            if($shipment) {
                // $magentoAPIpassword = Setting::where('seller_id', $seller_id)->where('key', 'magentoAPIPassword')->first();
                // $magentoAPIusername = Setting::where('seller_id', $seller_id)->where('key', 'magentoAPIUsername')->first();
                $jdotMagentoAPItoken = Setting::where('seller_id', $seller_id)->where('key', 'jdotMagentoAPItoken')->first();
    
                $WC = Setting::where('seller_id', $seller_id)->where('key', 'WC')->first();
                if($WC){
                    // $token = "yj6oli4sx84fic97nn35qujd31j482of";
                    $token = $jdotMagentoAPItoken->value;
    
                    Log::info("The token generated by Magento 2");
                    Log::info($token);
                    $data['url'] = $WC->value;
                    $data['magento_token'] = $token;
    
                    Log::info($shipment);
                    $order = Order::find($shipment->order_id);
                    $data['magento_order_id'] = $order->marketplace_id;

                    $data['shipment_id'] = $shipment->id;

                    $comment = "Tracking Update by <b>".$shipment->courier->name."</b> for <br> Tracking number : <b>".$shipment->tracking_number."</b><br>
                    Shipment Status : <b>".$shipment->status."</b> ";
                    $data['comment'] =  $comment;
                    $data['magento_order_id'] = $order->marketplace_id;
                    $data['unity_order_id'] = $order->id;
                    $data['magento_token'] = $token;
                    $data['order_status'] =  "complete";
                    Log::info($data);
                    event(new UpdateStatusOnMagnetoForJdotEvent($data));

                    
                }
    
    
            }
        }

        

        
        Log::info('JDot :: bulkShipmentStatusSyncMagento Ended');
        return "JDot ::bulkShipmentStatusSyncMagento Completed";
    }




}
