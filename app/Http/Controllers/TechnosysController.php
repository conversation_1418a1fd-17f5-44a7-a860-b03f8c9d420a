<?php

namespace App\Http\Controllers;

use App\Events\ReturnSalesTechnosysEvent;
use App\Models\GRNPostingReceipt;
use App\Models\Product;
use App\Models\SellerLocation;
use App\Models\Shipment;
use App\Models\StockOrder;
use App\Models\StockOrderItem;
use App\Models\StockTransferFulfillmentOrder;
use App\Service\Integration\ERP\ERPForTechnosys;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;
use Mail;

class TechnosysController extends Controller
{
    public function getReturnMarkedOrders($seller_id)
    {
        Log::info("Technosys | GetReturnMarkedOrders | Seller id < {$seller_id} >");
        Log::info("Technosys | GetReturnMarkedOrders | Started");

        $last_hour_date = Carbon::now()->subMinutes(5)->toDateTimeString();
        $shipments = Shipment::on('unity-read')->whereSellerId($seller_id)
            ->where('updated_at', '>=', $last_hour_date)
            ->where('erp_sync', '=', 0)
            ->whereIn('status', ["Return Received", "Cancelled"])
            ->get();

        if ($shipments->count() > 0) {
            foreach ($shipments as $shipment) {
                Log::info($shipment);
                $params['shipment'] = $shipment;
                $params['cn'] = $shipment->tracking_number;
                event(new ReturnSalesTechnosysEvent($params));
            }
        }

        Log::info("Technosys | GetReturnMarkedOrders | Completed");
        return "Technosys | GetReturnMarkedOrders | Completed";
    }

    public function getGRNPostedOrders($seller_id)
    {
        $message = "TechnoSys ERP getGRNPostedOrders | ";
        $recipients = explode(',', env('TECHNOSYS_NOTIFICATION_EMAIL', '<EMAIL>'));
        $unity_email = env('UNITY_NOTIFICATION_EMAIL', '');

        try {

            Log::info("getGRNPostedOrders Seller id ::" . $seller_id);

            $last_hour_date = Carbon::now()->subMinutes(15)->toDateTimeString();
            $stock_orders = StockOrder::on('unity-read')->where('seller_id', $seller_id)->where('status', "=", "Received")->where('updated_at', '>=', $last_hour_date)->get();

            if ($stock_orders->count() > 0) {

                foreach ($stock_orders as $stock_order) {

                    $destination_location_reference = SellerLocation::where('id', $stock_order->location_id)->value('seller_reference_id');
                    $stock_order_items = GRNPostingReceipt::where("stock_order_id", $stock_order->id)->where("grn_receipt", 0)->get();
                    $item_arr = [];
                    $temp_arr = [];

                    foreach ($stock_order_items as $item) {

                        $product = Product::find($item->seller_product_id);

                        if ($product) {

                            if (isset($temp_arr[$product->barcode])) {
                                $temp_arr[$product->barcode]['quantity'] = $temp_arr[$product->barcode]['quantity'] + $item->qty_received;

                            } else {
                                $temp_arr[$product->barcode]['sku'] = $product->SKU;
                                $temp_arr[$product->barcode]['sale'] = $product->sale;
                                $temp_arr[$product->barcode]['cost'] = $product->cost;
                                $temp_arr[$product->barcode]['quantity'] = $item->qty_received;
                            }
                        }
                    }

                    foreach ($temp_arr as $key => $value) {

                        $item_arr['items_arr'][] = ['Barcode' => $key, 'Quantity' => $value['quantity'], 'SaleRate' => $value['sale'], 'CostRate' => $value['cost']];
                        $item_arr['items_arr_temp'][] = ['Barcode' => $key, 'sku' => $value['sku'], 'Quantity' => $value['quantity']];
                    }

                    // Log::info($item_arr);

                    if (count($item_arr) > 0) {

                        $stock_reference_id = $stock_order->reference_id;

                        if($stock_order->type == config('enum.stock_order_types')['TRANSFER']) {

                            $origin_location_id = StockTransferFulfillmentOrder::where('stock_order_id', $stock_order->id)->value('seller_location_id');
                            $origin_location_reference = SellerLocation::where('id', $origin_location_id)->value('seller_reference_id');


                            $header = [
                                'ReceivingType' => 2,
                                'IssuenceNumber' => $stock_reference_id,
                                'ReceiveFromBranchId' => $origin_location_reference ?? $stock_order->supplier_name,
                                'CompanyBranchId' => $destination_location_reference,
                                'ReceiveNoteDate' => $stock_order->arrival_date,
                                'Remarks' => 'Transfer Type (Branch/Stock Issuence) GRN Receipt from Unity',
                            ];

                        } else if($stock_order->type == config('enum.stock_order_types')['NORMAL']) {

                            $header = [
                                'PurchaseOrderNumbers' => $stock_reference_id,
                                'ReceivingType' => 3,
                                'ProductVendorName' => $stock_order->supplier_name,
                                'ReceiveNoteDate' => $stock_order->arrival_date,
                                'CompanyBranchId' => $destination_location_reference,
                                'Remarks' => 'Normal Type (Vendor/Purchase Order) GRN Receipt from Unity',
                            ];
                        }

                        $params = [
                            'header' => $header,
                            'receivingDetail' => $item_arr['items_arr'],
                        ];

                        Log::info(' TechnoSys getGRNPostedOrders params  | '.json_encode($params));
                        $erp = new ERPForTechnosys($seller_id);
                        $response = $erp->postGRNPostedOrders($params);


                        if ($response['success']) {

                            /// Updating GRN Posting on Erp in Unity
                            if(count($stock_order_items) > 0) {

                                foreach($stock_order_items as $item) {

                                    $item->grn_receipt = 1;
                                    $item->grn_receipt_date = Carbon::now()->toDateTimeString();
                                    $item->save();
                                }
                            }

                            $stock_order = array("stock_order_id" => $stock_reference_id , "line_items" => $item_arr);

                        } else {

                            $message .= "failure | ".json_encode($response);

                            Mail::raw($message, function ($m) use ($recipients,$unity_email,$stock_reference_id) {
                                $m->to($recipients)
                                    ->bcc($unity_email)
                                    ->subject('Stock Reference :: '.$stock_reference_id.' - TechnoSys ERP getGRNPostedOrders : failure');
                            });
                        }

                    }
                }
            }

        } catch (\Throwable $th) {

            Log::info("TechnoSys getGRNPostedOrders : Catch ", $th->getTrace());  

            $message .= " Catch | ". $th->getMessage();

            Mail::raw($message, function ($m) use ($recipients,$unity_email,$stock_reference_id) {
                    $m->to($recipients)
                    ->bcc($unity_email)
                    ->subject('Stock Reference :: '.$stock_reference_id.' - TechnoSys ERP getGRNPostedOrders : failure');
            });   
        }


    }

    public function getTransferOrders($seller_id)
    {
        $time_to_match = Carbon::now()->subMinutes(5)->toDateTimeString();
        $stock_transfer_orders = StockTransferFulfillmentOrder::on('unity-read')->where('seller_id', $seller_id)->where('created_at', '>=', $time_to_match)->get();

        // Log::info($stock_transfer_orders);

        if ($stock_transfer_orders->count() > 0) {

            foreach ($stock_transfer_orders as $stock_transfer_order) {

                // Log::info($stock_transfer_order->reference_id);
                $stock_order = StockOrder::where("id", $stock_transfer_order->stock_order_id)->first();
                $origin_location_reference = SellerLocation::where('id', $stock_transfer_order->seller_location_id)->value('seller_reference_id');
                $destination_location_reference = SellerLocation::where('id', $stock_order->location_id)->value('seller_reference_id');
                $stock_order_items = StockOrderItem::where("stock_order_id", $stock_order->id)->get();

                $params = [
                    'header' => [
                        'IssuenceType' => '7',
                        'IssuenceDate' => Carbon::parse($stock_transfer_order->created_at)->toAtomString(),
                        'CompanyBranchId' => $origin_location_reference,
                        'StockIssueToBranch' => $destination_location_reference,
                        'DepartmentId' => null,
                        'Remarks' => '('.$stock_transfer_order->reference_id.') Stock Transfer Order creation request from Unity'
                    ]
                ];

                foreach ($stock_order_items as $item) {

                    $product = Product::find($item->product_id);
                    $params['issuenceDetail'][] = [
                        'Barcode' => $product->barcode,
                        'Quantity' => $item->qty
                    ];
                }

                Log::info(' TechnoSys getTransferOrders params  | '.json_encode($params));

                $erp = new ERPForTechnosys($seller_id);
                $response = $erp->createTransferOrder($params);

                Log::info(' TechnoSys getTransferOrders response  | '.json_encode($response));

            }
        }

        \Log::info('getTransferOrders :: process completed');
        return "Stock Transfer Orders Fetching completed";
    }
}
