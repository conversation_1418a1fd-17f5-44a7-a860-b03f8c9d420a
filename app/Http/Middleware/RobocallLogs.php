<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;

class RobocallLogs
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {
        $sig=$request->header('X-Auth-Secret');

        if(!($request->header('X-Auth-Topic')))
        {
            return response()->json(['message' => 'Topic not found'],400);
        }

        $topic=$request->header('X-Auth-Topic');

        $authSig = base64_encode(hash_hmac('sha256', $request->seller_id.$request->order_id, 'URxRobocall', true));

        if($authSig != $sig){
            return response()->json(['message' => 'Unauthorized'],401);
        }

        if(!in_array($topic,config('enum.robocall_events_short_status'))){
            return response()->json(['message' => 'Invalid Topic'],400);
        }
        return $next($request);
    }
}
