<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;

class HthreeRobocallLogs
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {
        $sig=$request->header('X-Auth-Secret');

        // $authSig = base64_encode(hash_hmac('sha256', $request->order_id, 'URxRobocall', true));

        $authSig = "YJ5trZKd9qaiJ4AT68QcaAZJntqtiCp9";

        // if($authSig != $sig){
        //     return response()->json(['message' => 'Unauthorized'],401);
        // }

        return $next($request);
    }
}
