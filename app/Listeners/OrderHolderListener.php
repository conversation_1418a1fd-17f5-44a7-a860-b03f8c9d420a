<?php

namespace App\Listeners;

use App\Events\OrderHolderEvent;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;

class OrderHolderListener implements ShouldQueue
{
    public $queue = "orderHolder";

    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     *
     * @param  OrderHolderEvent  $event
     * @return void
     */
    public function handle(OrderHolderEvent $event)
    {
        $generate_again = false;

        try {

            $curl = curl_init();
            $endpoint_url = env('ENDPOINT_URL');
            $khaadi_url = env('KHAADI_URL');
            
            if ($event->staging) {
                $endpoint_url = 'https://staging.unityretail.com';
                $khaadi_url = 'https://staging.khaadi.com';
            }

            curl_setopt_array($curl, array(
                CURLOPT_URL =>  $endpoint_url.'/api/khaadi/order',
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_ENCODING => "",
                CURLOPT_MAXREDIRS => 10,
                CURLOPT_TIMEOUT => 60,
                CURLOPT_FOLLOWLOCATION => true,
                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                CURLOPT_CUSTOMREQUEST => "POST",
                CURLOPT_POSTFIELDS => json_encode($event->request['data']),
                CURLOPT_HTTPHEADER => array(
                    "Content-Type: application/json",
                    "X-Origin-Url: ".$event->request['url'],
                    "X-Auth-Token: ".$event->request['token']
                ),
            ));

            $response = curl_exec($curl);
            $httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);

            if ($httpCode == '200') {
                Log::info('Khaadi Order Holder Api Unity Endpoint Request Success');
                curl_close($curl);
                Log::info('Khaadi Order Holder Api response '.$response);
            } else {
                $generate_again = true;
                if ($event->staging) {
                    Mail::raw('Staging Khaadi Order Holder Api Unity Endpoint Event Generated again | '.$httpCode.' | '.json_encode($response), function ($m)  {
                        $m->to('<EMAIL>')->subject('Khaadi Order Holder Api');
                    });
                }else{
                    Mail::raw('Khaadi Order Holder Api Unity Endpoint Event Generated again | '.$httpCode.' | '.json_encode($response), function ($m)  {
                        $m->to('<EMAIL>')->subject('Khaadi Order Holder Api');
                    });
                }
                
                Log::info('Khaadi Order Holder Api Unity Endpoint Event Generated again | '.$httpCode.' | '.$response);
            }


                // $curl = curl_init();
    
                // $data = array("statusHistory" => array("comment" => "Unity Retail | Response | ".$response , "status" => 'comment') );
                
                // curl_setopt_array($curl, array(
                // CURLOPT_URL => $khaadi_url."/rest/V1/orders/".$event->request['data']['OrderData']['entity_id']."/comments",
                // CURLOPT_RETURNTRANSFER => true,
                // CURLOPT_ENCODING => "",
                // CURLOPT_MAXREDIRS => 10,
                // CURLOPT_TIMEOUT => 60,
                // CURLOPT_FOLLOWLOCATION => true,
                // CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                // CURLOPT_CUSTOMREQUEST => "POST",
                // CURLOPT_POSTFIELDS => json_encode($data),
                // CURLOPT_HTTPHEADER => array(
                //     "Authorization: Bearer ".env('KHAADI_TOKEN'),
                //     "Content-Type: application/json"
                // ),
                // ));
    
                // $response2 = curl_exec($curl);

                // $httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);

                // if ($httpCode != '200') {
                //     $message = 'Order Synced in unity, status not updated at storefront';
                //     Mail::raw($message.' | '.json_encode($response2), function ($m)  {
                //         $m->to('<EMAIL>')->subject('Khaadi Order Holder Api');
                //     });
                // }
    
                // curl_close($curl);

        } catch(\Exception $e){
            $activity_id = activity()
            ->withProperties( json_encode($e->getMessage()) )
            ->log('Khaadi Order Holder Api');
            Log::info('Khaadi Order Holder Api error '.$e->getMessage());
            Log::info($e->getTraceAsString());
            $generate_again = true;
            
            Mail::raw('Khaadi Order Holder Api Unity Endpoint Event Generated again | '.$e->getMessage(), function ($m)  {
                $m->to('<EMAIL>')->subject('Khaadi Order Holder Api');
            });
        }

        if ($generate_again) {
            $request_data = array('data' => $event->request['data'], 'url' => $event->request['url'], 'token' => $event->request['token']);
            sleep(300);
            event(new OrderHolderEvent($request_data));
        }
        return "good";
        
    }
}
