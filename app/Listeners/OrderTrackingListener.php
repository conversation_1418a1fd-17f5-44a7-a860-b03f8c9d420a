<?php

namespace App\Listeners;

use App\Events\OrderTrackingEvent;
use App\Helpers\OrderComment;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;

class OrderTrackingListener implements ShouldQueue
{
    public $queue = "internationalOrder";

    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     *
     * @param  OrderTrackingEvent  $event
     * @return void
     */
    public function handle(OrderTrackingEvent $event)
    {
        try {
    
            $curl = curl_init();

            curl_setopt_array($curl, array(
            CURLOPT_URL => env('KHAADI_URL')."/rest/V1/ordershipmenttrack/".$event->entity_id,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => "",
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 60,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => "POST",
            CURLOPT_HTTPHEADER => array(
                "Authorization: Bearer ".env('KHAADI_TOKEN'),
                "Content-Type: application/json"
            ),
            ));

            $response = curl_exec($curl);
            $response = json_decode($response);

            curl_close($curl);

            if (isset($response[0]->failed)) {
                Log::info('Khaadi International Order Tracking Api | '.$response[0]->failed);
                // Mail::raw('Khaadi response | '.$event->marketplace_reference_id.' | '.json_encode($response), function ($m)  {
                //     $m->to('<EMAIL>')->subject('Khaadi International Order Tracking Api');
                // });
            } else {

                if (isset($response[0]->carrier_code) && $response[0]->carrier_code == 'DPD') {

                    Log::info('Khaadi International Order Tracking Api | Creating Shipment');

                    ///// Creating Manual shipment in unity //////
                    $data = array('marketplace_reference_id' => $event->marketplace_reference_id,
                                    'seller_id' => 120,
                                    'courier_id' => 16,
                                    'tracking_number' => $response[0]->track_number);
    
                    $curl2 = curl_init();
    
                    curl_setopt_array($curl2, array(
                        CURLOPT_URL => env('ENDPOINT_URL').'/api/order/book/manual',
                        CURLOPT_RETURNTRANSFER => true,
                        CURLOPT_ENCODING => "",
                        CURLOPT_MAXREDIRS => 10,
                        CURLOPT_FOLLOWLOCATION => true,
                        CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                        CURLOPT_CUSTOMREQUEST => "POST",
                        CURLOPT_POSTFIELDS => json_encode($data),
                        CURLOPT_HTTPHEADER => array(
                        "Content-Type:application/json"
                                            )
                    ));
    
                    $response2 = curl_exec($curl2);
                    $response2 = json_decode($response2, true);
                    $httpCode = curl_getinfo($curl2, CURLINFO_HTTP_CODE);
    
                    if ($httpCode == '200' && $response2['error'] == 0) {
                        Log::info('Khaadi International Order Tracking Api has been synced with unity | '.$event->marketplace_reference_id);
                        $order_id = $event->id;
                        $message = $response2['message'];
                        $key = 'Khaadi International Order Tracking Api';
                        $status = 'Success';
                        OrderComment::add(compact('order_id','message','key','status'));
                        
                    } else {
                        Log::info('FAILED | Khaadi International Order Tracking Api not Synced | '. $event->marketplace_reference_id);
                        Mail::raw('Unity response | '.$event->marketplace_reference_id.' | '.json_encode($response2), function ($m)  {
                            $m->to('<EMAIL>')->subject('Khaadi International Order Tracking Api');
                        });
                    }
                    
                    curl_close($curl2);
                } elseif(isset($response[0]->carrier_code) && in_array($response[0]->carrier_code,['NoneSet','AMAZON','custom'])) {
                    
                    Log::info('Khaadi International Order Tracking Api | Creating Shipment');

                    ///// Creating Manual shipment in unity //////
                    $data = array('marketplace_reference_id' => $event->marketplace_reference_id,
                                    'seller_id' => 120,
                                    'courier_id' => 25,
                                    'tracking_number' => $response[0]->track_number);
    
                    $curl2 = curl_init();
    
                    curl_setopt_array($curl2, array(
                        CURLOPT_URL => env('ENDPOINT_URL').'/api/order/book/manual',
                        CURLOPT_RETURNTRANSFER => true,
                        CURLOPT_ENCODING => "",
                        CURLOPT_MAXREDIRS => 10,
                        CURLOPT_FOLLOWLOCATION => true,
                        CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                        CURLOPT_CUSTOMREQUEST => "POST",
                        CURLOPT_POSTFIELDS => json_encode($data),
                        CURLOPT_HTTPHEADER => array(
                        "Content-Type:application/json"
                                            )
                    ));
    
                    $response2 = curl_exec($curl2);
                    $response2 = json_decode($response2, true);
                    $httpCode = curl_getinfo($curl2, CURLINFO_HTTP_CODE);
    
                    if ($httpCode == '200' && $response2['error'] == 0) {
                        Log::info('Khaadi International Order Tracking Api has been synced with unity | '.$event->marketplace_reference_id);
                        $order_id = $event->id;
                        $message = $response2['message'];
                        $key = 'Khaadi International Order Tracking Api';
                        $status = 'Success';
                        OrderComment::add(compact('order_id','message','key','status'));
                        
                    } else {
                        Log::info('FAILED | Khaadi International Order Tracking Api not Synced | '. $event->marketplace_reference_id);
                        Mail::raw('Unity response | '.$event->marketplace_reference_id.' | '.json_encode($response2), function ($m)  {
                            $m->to('<EMAIL>')->subject('Khaadi International Order Tracking Api');
                        });
                    }
                    
                    curl_close($curl2);
                } else {
                    Mail::raw('Khaadi International Order Tracking Api | in else | '.json_encode($response).' | Order ID: '.$event->marketplace_reference_id, function ($m)  {
                        $m->to('<EMAIL>')->subject('Khaadi International Order Tracking Api - (carrier_code)');
                    });
                    Log::info('Khaadi International Order Tracking Api | in else | '.json_encode($response));
                }
            }

        } catch(\Exception $e){

            $activity_id = activity()
            ->withProperties(['response' => $e->getMessage() , 'dump' => $e->getTraceAsString()])
            ->log('Khaadi International Order Tracking Api');
            Log::info('Khaadi International Order Tracking Api error '.$e->getMessage());
        }
    }
}
