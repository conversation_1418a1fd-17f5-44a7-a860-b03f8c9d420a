<?php

namespace App\Listeners;

use App\Events\GetOrderOriginEvent;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use App\Helpers\OrderComment;
use App\Helpers\DynamicAccessToken;
use App\Models\SellerLocation;
use App\Models\Order;
use Carbon\Carbon;
use App\Events\SendOrderOriginEvent;
use App\Traits\ExceptionTrait;
use Exception;

class GetOrderOriginListener implements ShouldQueue
{
    public $queue = "getOriginFromDynamics";
    use ExceptionTrait;
    use InteractsWithQueue;

    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     *
     * @param  GetOrderOriginEvent  $event
     * @return void
     */
    public function handle(GetOrderOriginEvent $event)
    {
        $message = '';
        $order_array = Order::whereIn('id', $event->order)->where('seller_location_id',1)->where('status', 'Pending')->get(['id','marketplace_reference_id']);
        $order_marketplace_reference_ids = $order_array->pluck('marketplace_reference_id');
        
        if(count($order_marketplace_reference_ids) > 0) {

            try {
        
                $data = [ '_contract' =>  ['Orders' => $order_marketplace_reference_ids]  ];
                $api_call = $this->getOriginApi($data, $message, $event->order);

                if ($api_call['code'] == '200') {
                    $this->sendLocation($api_call['response'], $order_array);
                }

            } catch(Exception $e){

                Log::info('Get Order Origin process error '.$e->getMessage());

                $message .= 'Get Order Origin Process error';

                $unity = explode(',',env('UNITY_POC'));
                $others = explode(',',env('KHAADI_POC'));

                if($others[0] != "") {
                    Mail::raw($message.' | '.$e->getMessage(), function ($m) use ($others,$unity) {
                        $m->to($others)
                        ->bcc($unity)
                        ->subject('Get Order Origin Process');
                    });
                } else {
                    Mail::raw($message.' | '.$e->getMessage(), function ($m)  use ($unity) {
                        $m->to($unity)
                            ->subject('Get Order Origin Process');
                    });
                }

                $activity_id = activity()
                ->withProperties(['response' => json_encode($e->getMessage()) , 'dump' => $e->getTraceAsString()])
                ->log('Get Order Origin Process');
            }

           
        }


    }

    public function comments($status, $order_id,$message)
    {
        $key = 'Get Order Origin Process';
        OrderComment::add(compact('order_id','message','key','status'));
    }

    public function getOriginApi($data,$order_array)
    {
        $token = DynamicAccessToken::getToken();
        
        if($token['error'] == 0) {

            $request_url = env('D365_URL').'/api/services/SLD_UnityRetailService/SLD_UnityRetailService/GetOrderDetails';
            
            $curl = curl_init();
            curl_setopt_array($curl, array(
                CURLOPT_URL => $request_url,
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_ENCODING => "",
                CURLOPT_MAXREDIRS => 10,
                CURLOPT_TIMEOUT => 60,
                CURLOPT_FOLLOWLOCATION => true,
                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                CURLOPT_CUSTOMREQUEST => "POST",
                CURLOPT_POSTFIELDS =>json_encode($data),
                CURLOPT_HTTPHEADER => array(
                    "Content-Type: application/json",
                    "Authorization: bearer ".$token['token'].""
                ),
            ));

            $response = json_decode(curl_exec($curl),true);
            $httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);

            if($httpCode == '200') {
                return array('response' => $response, 'code' => $httpCode); 
            } else {
                $message = 'Get Origin API with token ('.$token['token'].') Failed. Response Code | '.$httpCode;
                $this->addException(    119,
                                        config('enum.exception_types')['DYNAMICSGETORIGINAPI'],
                                        $message,
                                        'Order',
                                        null,
                                        null,
                                        json_encode(['request_url' => $request_url]),
                                        ( $data ? json_encode($data) : null),
                                        ( $response ? json_encode($response) : null)
                                    );


                $unity = explode(',',env('UNITY_POC'));
                $others = explode(',',env('KHAADI_POC'));

                if($others[0] != "") {
                    Mail::raw($message, function ($m) use ($others,$unity) {
                        $m->to($others)
                        ->bcc($unity)
                        ->subject('D365 - Get Order Origin Process');
                    });
                } else {
                    Mail::raw($message, function ($m)  use ($unity) {
                        $m->to($unity)
                            ->subject('D365 - Get Order Origin Process');
                    });
                }

                return array('response' => $response, 'code' => $httpCode); 
            }
        } else {
            Log::info('Get Order Origin Process | '.$token);

            // foreach($order_array as $order) {
            //     $order_obj = Order::find($order);
            //     $this->comments('Failed',$order_obj,$token['message']);
            // }
            return array('response' => $token['message'], 'code' => '0'); 
        }
        
    }

    public function sendLocation($response, $order_array)
    {
        $counter = 0;

        for( $i=0; $i<count($response); $i++ ) {
        
            $message = '';
            
            if (isset($response[$i]['Warehouse']) && isset($response[$i]['EcommOrder']) &&  $response[$i]['Warehouse'] && $response[$i]['EcommOrder']) {
                $counter++;
                $temp_order_id = $order_array->where('marketplace_reference_id', $response[$i]['EcommOrder'])->first()->id;
                $message .= "Received Pickup Location ID: <b>".$response[$i]['Warehouse']."</b>";
                $this->comments('Success', $temp_order_id, $message);
                Log::info('Get Order Origin Process | '.$message.' | '.$temp_order_id);
                event(  new SendOrderOriginEvent( $response[$i]['EcommOrder'], $response[$i]['Warehouse'] )  );
            } else {
                Log::info('Get Order Origin Process | Warehouse : '.$response[$i]['Warehouse'].' | Order : '.$response[$i]['EcommOrder']);
            }
            
        }
        Log::info('Get Order Origin Process | Got Warehouse of '.$counter.' orders.');
    }

    public function failed(GetOrderOriginEvent $event, $exception)
    {
        Mail::raw($exception, function ($m)  {
            $m->to('<EMAIL>')->subject('Get Order Origin Event Failed');
        });
    }
}
