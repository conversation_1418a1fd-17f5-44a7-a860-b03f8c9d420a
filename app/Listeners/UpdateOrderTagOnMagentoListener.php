<?php

namespace App\Listeners;

use App\Events\UpdateOrderTagOnMagentoEvent;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;

use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use App\Models\FulfillmentOrder;
use App\Models\Setting;
use App\Models\Shipment;
use App\Traits\ExceptionTrait;
use Carbon\Carbon;
use App\Models\FulfillmentOrderGtechUpdates;
use GuzzleHttp\Client;
use Exception;

class UpdateOrderTagOnMagentoListener implements ShouldQueue
{
    public $queue = "updateOrderTagOnMagentoListener";
    use ExceptionTrait;
    use InteractsWithQueue;

    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     *
     * @param  SendShipmentDetailsEvent  $event
     * @return void
     */
    public function handle(UpdateOrderTagOnMagentoEvent $event)
    {
        sleep(2);
        Log::info("UpdateOrderTagOnMagentoEvent Event Started Order with  : ".$event->data['magento_order_id']);

        $data = $event->data;
        Log::info($data);
        $params = array("comment" => 'Order was marked confirmed by Unity Retail' , "status" => $data['order_status']);
        $params = array("statusHistory" => $params);

        Log::info($params);
        $params = json_encode($params);

        Log::info($data['url'].'/rest/V1/orders/'.$data['magento_order_id'].'/comments');


        $curl = curl_init();

        curl_setopt_array($curl, array(
          CURLOPT_URL => $data['url'].'/rest/V1/orders/'.$data['magento_order_id'].'/comments',
          CURLOPT_RETURNTRANSFER => true,
          CURLOPT_ENCODING => '',
          CURLOPT_MAXREDIRS => 10,
          CURLOPT_TIMEOUT => 0,
          CURLOPT_FOLLOWLOCATION => true,
          CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
          CURLOPT_CUSTOMREQUEST => 'POST',
          CURLOPT_POSTFIELDS =>$params,
          CURLOPT_HTTPHEADER => array(
            'Content-Type: application/json',
            'Authorization: Bearer '.$data['magento_token']
          ),
        ));
        
        $response = curl_exec($curl);
        
        curl_close($curl);
        echo $response;

        Log::info("UpdateOrderTagOnMagentoEvent :: Response :: ");
        Log::info($response);
        Log::info("UpdateOrderTagOnMagentoEvent :: End :: ");

    }
}
