<?php

namespace App\Listeners;

use App\Events\ReturnSalesTechnosysEvent;
use App\Helpers\TechnosysERP;
use Illuminate\Support\Facades\Log;
use App\Models\RMARequest;
use App\Models\RMAItems;
use App\Models\Shipment;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use App\Traits\ExceptionTrait;
use App\Service\Integration\ERP\ERPForTechnosys;

class ReturnSalesTechnosysListener implements ShouldQueue
{
    public $queue = "returnSalesTechnosysListener";
    use ExceptionTrait;
    use InteractsWithQueue;

    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     *
     * @param  SendShipmentDetailsEvent  $event
     * @return void
     */
    public function handle(ReturnSalesTechnosysEvent $event)
    {

        Log::info("ReturnSalesTechnosysEvent | Event Started Order with courier < {$event->data['cn']} >");

        $shipment = $event->data['shipment'];
        $internalParams = [
            'order_id' => $shipment->order->id,
            'seller_id' => $shipment->seller_id
        ];
        $technosys_service = new ERPForTechnosys($shipment->seller_id);
        $technosys_helper = new TechnosysERP($technosys_service);

        $params = [
            'shipment' => $shipment,
            'internal_params' => $internalParams,
            'rma' => false
        ];

        if ($shipment->type === "reverse") {
            $this->processReverseShipment($shipment, $params, $technosys_helper);
        } else {
            $this->processRegularShipment($shipment, $params, $technosys_helper);
        }

        Log::info("ReturnSalesTechnosysEvent | Event Completed Order with courier < {$event->data['cn']} >");
    }

    private function processReverseShipment($shipment, &$params, $technosys_helper_obj)
    {
        $rma_shipment = RMARequest::where('return_shipment_id', $shipment->id)->first();

        if ($rma_shipment) {
            $rma_items = RMAItems::with(['items.order_item'])
                ->where('rma_requests_id', $rma_shipment->id)
                ->firstOrFail();
            Log::info("The RMA Items are");
            Log::info($rma_items);
            $original_shipment = Shipment::find($rma_shipment->shipment_id);

            $params['rma'] = true;
            $params['rma_items'] = $rma_items;
            $params['shipment'] = $original_shipment;
            $technosys_helper_obj->createReturnOrder($params);
        }
    }

    private function processRegularShipment($shipment, $params, $technosys_helper_obj)
    {
        $technosys_helper_obj->createReturnOrder($params);
    }
}
