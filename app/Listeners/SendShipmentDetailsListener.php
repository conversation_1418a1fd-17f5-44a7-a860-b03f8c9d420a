<?php

namespace App\Listeners;

use App\Events\SendShipmentDetailsEvent;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;

use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use App\Helpers\OrderComment;
use App\Models\SellerLocation;
use App\Models\Shipment;
use App\Traits\ExceptionTrait;
use Carbon\Carbon;

class SendShipmentDetailsListener implements ShouldQueue
{
    public $queue = "sendShipmentDetail";
    use ExceptionTrait;

    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     *
     * @param  SendShipmentDetailsEvent  $event
     * @return void
     */
    public function handle(SendShipmentDetailsEvent $event)
    {
        $shipment = (object)$event->shipment;

        $courier_id = '';
        $courier_name = '';
        $request_url = null;
        $request_data = null;
        $response_data = null;

        $unity = explode(',',env('UNITY_POC'));
        $others = explode(',',env('KHAADI_POC'));

        

        if($shipment->cod == null)
        {
            $courier_id = '5018329';
            if($shipment->courier_id == 4)
            {
                $courier_name = 'MnP';
            }
            elseif($shipment->courier_id == 8)
            {
                $courier_name = 'TRAX';
            }
            elseif($shipment->courier_id == 11)
            {
                $courier_name = 'RIDER';
            }
            elseif($shipment->courier_id == 10)
            {
                $courier_name = 'BEX';
            }
            elseif($shipment->courier_id == 1)
            {
                $courier_name = 'COD-KLM';
            }
            elseif($shipment->courier_id == 7)
            {
                $courier_name = 'CALL COURIER';
            }
            elseif($shipment->courier_id == 19)
            {
                $courier_name = 'COD-MOVEX';
            }
            elseif($shipment->courier_id == 5)
            {
                $courier_name = 'LEOPARD';
            }
            elseif($shipment->courier_id == 13)
            {
                $courier_name = 'TCS';
            }
        }
        else{
            if($shipment->courier_id == 4)
            {
                $courier_id = '2080428';
                $courier_name = 'MnP';
            }
            elseif($shipment->courier_id == 8)
            {
                $courier_id = '5018881';
                $courier_name = 'TRAX';
            }
            elseif($shipment->courier_id == 11)
            {
                $courier_id = '5018839';
                $courier_name = 'RIDER';
            }
            elseif($shipment->courier_id == 10)
            {
                $courier_id = '5000521';
                $courier_name = 'BEX';
            }
            elseif($shipment->courier_id == 1)
            {
                $courier_id = '5018404';
                $courier_name = 'KLM';
            }
            elseif($shipment->courier_id == 7)
            {
                $courier_id = '5018880';
                $courier_name = 'CALL COURIER';
            }
            elseif($shipment->courier_id == 19)
            {
                $courier_id = '5018605';
                $courier_name = 'COD-MOVEX';
            }
            elseif($shipment->courier_id == 5)
            {
                $courier_id = '5018905';
                $courier_name = 'LEOPARD';
            }
            elseif($shipment->courier_id == 13)
            {
                $courier_id = '5018975';
                $courier_name = 'TCS';
            }
        }

 

        $process_status = 'Failed';
        $message = '';

        try{

            $curl = curl_init();

                $request_url = "https://login.microsoftonline.com/khaadi.com/oauth2/token";
                $request_data = "client_id=".env('D365_CLIENT_ID')."&client_secret=".env('D365_CLIENT_SECRET')."&resource=".env('D365_URL')."&grant_type=client_credentials";
                curl_setopt_array($curl, array(
                CURLOPT_URL => $request_url,
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_ENCODING => "",
                CURLOPT_MAXREDIRS => 10,
                CURLOPT_TIMEOUT => 60,
                CURLOPT_FOLLOWLOCATION => true,
                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                CURLOPT_CUSTOMREQUEST => "GET",
                CURLOPT_POSTFIELDS => $request_data,
                CURLOPT_HTTPHEADER => array(
                    "Content-Type: application/x-www-form-urlencoded",
                    "Cookie: x-ms-gateway-slice=prod; stsservicecookie=ests; fpc=ApzlCiChoEZHrANWIUOEKbgTfnkuAQAAAMMlR9cOAAAA"
                ),
                ));

            $token = json_decode(curl_exec($curl),true);
            $response_data = $token;
            $httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);

            curl_close($curl);

            

            if($httpCode == '200')
            {
                if(isset($token['access_token'])){
                    Log::info('got token');

                   
                    $items = array(
                        "OrderReference" => $shipment->order->marketplace_reference_id ,
                        "InvoiceAccountId" => $courier_id,
                        "CNNumber" => $shipment->tracking_number,
                        "FBRInvoiceNumber" => $shipment->order->fbr_invoice_no,
                        "CourrierMode" => $courier_name
                    );
                    $orders = array(
                        'Orders' => array($items)
                    );

                    $data = [
                        '_contract' => $orders
                    ];

                    // Log::info($data);



                    $curl = curl_init();

                    $request_url = env('D365_URL').'/api/services/SLD_UnityRetailService/SLD_UnityRetailService/UpdateOrderDetails';
                    $request_data = json_encode($data);
                    curl_setopt_array($curl, array(
                    CURLOPT_URL => $request_url,
                    CURLOPT_RETURNTRANSFER => true,
                    CURLOPT_ENCODING => "",
                    CURLOPT_MAXREDIRS => 10,
                    CURLOPT_TIMEOUT => 60,
                    CURLOPT_FOLLOWLOCATION => true,
                    CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                    CURLOPT_CUSTOMREQUEST => "POST",
                    CURLOPT_POSTFIELDS =>json_encode($data),
                    CURLOPT_HTTPHEADER => array(
                        "Content-Type: application/json",
                        "Authorization: bearer ".$token['access_token'].""
                    ),
                    ));
                    // Log::info('hdahad');
                
        
                    $response = json_decode(curl_exec($curl),true);
                    $response_data = $response;
        
                    $httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
        
                    curl_close($curl);
        
                    // Log::info("Log | ".$response->Status);
                    if($httpCode == '200')
                    {
                        if($response != [])
                        {
                            if($response[0]['Status'] == 'Success')
                            {
                                Log::info('Got success from D365');
                                $process_status = 'Success';
                                $message .= "Data Sent: <br>OrderReference: <b>".$items['OrderReference']."</b><br>InvoiceAccountId: <b>".$items['InvoiceAccountId']."</b><br>CNNumber :<b>".$items['CNNumber']."</b><br>FBRInvoiceNumber :<b>".$items['FBRInvoiceNumber']."</b><br>CourrierMode :<b>".$items['CourrierMode']."</b><br>Update Order Detail API | Shipment Details are sent to D365 | API Response: <b>".$response[0]['Status']."</b>";
                            }
                            else{
                                $message .= 'Update Order Details API | '.$response['message'];
                            }
                        }
                        else{
                            $message .= 'Update Order Details API | Invalid Order ID';
                        }
                    }
                    else{
                        $message .= 'Update Order Details API Status Code | '.$httpCode;
                    }
                }
                else{
                    $message .= 'Did not get API Token';
                }
            }
            else{
                $message .= 'Token API Status Code | '.$httpCode.' | Didnt get D365 API Token';
                if($others[0] != ""){
                    Mail::raw($message, function ($m) use ($others,$unity) {
                        $m->to($others)
                        ->bcc($unity)
                        ->subject('D365 - Update Order Details Process');
                    });
                }
                else{
                    Mail::raw($message, function ($m)  use ($unity) {
                        $m->to($unity)
                            ->subject('D365 - Update Order Details Process');
                    });
                }
                // Mail::raw($message, function ($m)  {
                //     $m->to(['<EMAIL>','<EMAIL>'])
                //     ->bcc('<EMAIL>')
                //     ->subject('D365 - Update Order Details Process');
                // });

                 $this->addException(    119,
                                        config('enum.exception_types')['DYNAMICSTOKENAPI'],
                                        $message,
                                        'Order',
                                        $shipment->order_id,
                                        $shipment->order->marketplace_reference_id,
                                        json_encode(['request_url' => $request_url, 'query_data' => $event->shipment]),
                                        ( $request_data ? $request_data : null),
                                        ( $response_data ? json_encode($response_data) : null)
                                    );

                $process_status = '';
            }



            
        }
        catch(\Exception $e){
            $message .= 'Update Order Details API Error | '.$e->getMessage();
            $activity_id = activity()
            ->withProperties(['response' => json_encode($e->getMessage()) , 'dump' => $e->getTraceAsString()])
            ->log('D365 - Update Order Details Process');

            if($others[0] != ""){
                Mail::raw($message, function ($m) use ($others,$unity) {
                    $m->to($others)
                    ->bcc($unity)
                    ->subject('D365 - Update Order Details Process');
                });
            }
            else{
                Mail::raw($message, function ($m)  use ($unity) {
                    $m->to($unity)
                        ->subject('D365 - Update Order Details Process');
                });
            }

            // Mail::raw($message, function ($m)  {
            //     $m->to(['<EMAIL>','<EMAIL>'])
            //         ->bcc('<EMAIL>')
            //         ->subject('D365 - Update Order Details Process');
            // });
            
            Log::info('D365 - Update Order Details Process error '.$e->getMessage());
        }

        if($process_status == 'Failed')
        {
            if($others[0] != ""){
                Mail::raw('D365 - Update Order Details Process | '.$message.' | Order ID: '.$shipment->order->marketplace_reference_id, function ($m) use ($others,$unity) {
                    $m->to($others)
                    ->bcc($unity)
                    ->subject('D365 - Update Order Details Process');
                });
            }
            else{
                Mail::raw('D365 - Update Order Details Process | '.$message.' | Order ID: '.$shipment->order->marketplace_reference_id, function ($m)  use ($unity) {
                    $m->to($unity)
                        ->subject('D365 - Update Order Details Process');
                });
            }
            // Mail::raw('D365 - Update Order Details Process | '.$message.' | Order ID: '.$shipment->order->marketplace_reference_id, function ($m)  {
            //     $m->to(['<EMAIL>','<EMAIL>'])
            //         ->bcc('<EMAIL>')
            //         ->subject('D365 - Update Order Details Process');
            // });


                $this->addException(    119,
                                        config('enum.exception_types')['DYNAMICSSENDSHIPMENTAPI'],
                                        $message,
                                        'Order',
                                        $shipment->order_id,
                                        $shipment->order->marketplace_reference_id,
                                        json_encode(['request_url' => $request_url, 'query_data' => $event->shipment]),
                                        ( $request_data ? $request_data : null),
                                        ( $response_data ? json_encode($response_data) : null)
                                    );
        }

        $status = $process_status;
        $order_id = $shipment->order->id;
        $key = 'D365 - Update Order Details Process';
        Log::info($message);

        if ($status == 'Success') {
            Shipment::where('id', $shipment->id)->update(['erp_sync' => 2, 'erp_sync_at' => Carbon::now()->toDateTimeString() ]);
        } else {
            Shipment::where('id', $shipment->id)->update(['erp_sync' => 0]);
        }

        OrderComment::add(compact('order_id','message','key','status'));
    }
}
