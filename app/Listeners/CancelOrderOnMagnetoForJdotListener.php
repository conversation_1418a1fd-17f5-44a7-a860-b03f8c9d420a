<?php

namespace App\Listeners;

use App\Events\CancelOrderOnMagnetoForJdotEvent;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;

use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use App\Models\FulfillmentOrder;
use App\Models\Setting;
use App\Models\Shipment;
use App\Traits\ExceptionTrait;
use Carbon\Carbon;
use App\Models\FulfillmentOrderGtechUpdates;
use GuzzleHttp\Client;
use Exception;
use App\Helpers\OrderComment;
use App\Models\ShipmentHistory;


class CancelOrderOnMagnetoForJdotListener implements ShouldQueue
{
    public $queue = "cancelOrderOnMagnetoForJdotListener";
    use ExceptionTrait;
    use InteractsWithQueue;

    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     *
     * @param  SendShipmentDetailsEvent  $event
     * @return void
     */
    public function handle(CancelOrderOnMagnetoForJdotEvent $event)
    {

        Log::info("CancelOrderOnMagnetoForJdotEvent Event Started Order with  : ".$event->data['magento_order_id']);

        $data = $event->data;
        Log::info($data);


        Log::info($data['url'].'/rest/V1/orders/'.$data['magento_order_id'].'/comments');


        $curl = curl_init();

        curl_setopt_array($curl, array(
          CURLOPT_URL => $data['url'].'/rest/V1/orders/'.$data['magento_order_id'].'/cancel',
          CURLOPT_RETURNTRANSFER => true,
          CURLOPT_ENCODING => '',
          CURLOPT_MAXREDIRS => 10,
          CURLOPT_TIMEOUT => 0,
          CURLOPT_FOLLOWLOCATION => true,
          CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
          CURLOPT_CUSTOMREQUEST => 'POST',
          CURLOPT_HTTPHEADER => array(
            'Content-Type: application/json',
            'Authorization: Bearer '.$data['magento_token']
          ),
        ));
        
        $response = curl_exec($curl);
        $httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);

        curl_close($curl);

        if ($httpCode == '200' && $response == 'true') {
          Log::info('CancelOrderOnMagnetoForJdotEvent :: Jdot Magento Order Status Api Request Success');
            $message = $data['comment'];
            $key = 'Status Synced Process';
            $status = $data['order_status'];
            $order_id = $data['unity_order_id'];


            OrderComment::add(compact('order_id','message','key','status'));            

        } else{
          Log::info("CancelOrderOnMagnetoForJdotEvent :: Response :: ");
          Log::info($response);
        }


        Log::info("CancelOrderOnMagnetoForJdotEvent :: End :: ");

    }
}
