<?php

namespace App\Listeners;

use App\Events\CreateStockOrderThroughUnityForGtechEvent;
use App\Helpers\Gtech;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;

use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use App\Models\Setting;
use App\Traits\ExceptionTrait;
use GuzzleHttp\Client;
use Exception;

class CreateStockOrderThroughUnityForGtechListener implements ShouldQueue
{
    public $queue = "createStockOrderThroughUnityForGtechListener";
    use ExceptionTrait;
    use InteractsWithQueue;

    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     *
     * @param  SendShipmentDetailsEvent  $event
     * @return void
     */
    public function handle(CreateStockOrderThroughUnityForGtechEvent $event)
    {
        Log::info("CreateStockOrderThroughUnityForGtechEvent Event Started Order with order : ".$event->params['stock_order_id']);
        Log::info("CreateStockOrderThroughUnityForGtechEvent Params");

        $process_status = 'Failed';
        $message = 'CreateStockOrderThroughUnityEvent | '.$event->params['stock_order_id'].' | ';

        $api_key = Setting::where('seller_id',$event->params['seller_id'])->where('key', config('enum.api_keys')['ERP_API_KEY'])->first();

        Log::info("CreateStockOrderThroughUnityForGtechEvent ERP API KEY: ".$api_key->value);
        try {
            
            $client = new Client();

            $response = $client->post(env('ENDPOINT_URL')."/api/erp/create-stock-transfer-order", [
                'timeout' => 160, // Response timeout
                'connect_timeout' => 30, // Connection timeout
                'verify' => false,
                'headers' => [
                    'Content-Type' => 'application/json',
                    'Authorization' => $api_key->value,
                ],
                'body' => json_encode($event->params)
            ]);

            $unity_response = json_decode($response->getBody()->getContents(), TRUE);
            $httpCode = $response->getStatusCode();
          
            Log::info("CreateStockOrderThroughUnityForGtechEvent Response Code: ".$httpCode);
            Log::info("CreateStockOrderThroughUnityForGtechEvent Response : ". json_encode($unity_response));

            if($httpCode == '200') {

                if($unity_response['success'] == true) {
                    $process_status = 'Success';
                    $api_key = Setting::where('seller_id',$event->params['seller_id'])->where('key', config('enum.api_keys')['GTECH_SYSTEM_API_KEY'])->first();
                    $url = Setting::where('seller_id',$event->params['seller_id'])->where('key', config('enum.api_keys')['GTECH_SYSTEM_API_URL'])->first();
            
                    if($api_key && $url){
                        $gtech = new Gtech($api_key->value,$url->value);
                        $gtech->receiveFinishUpdate($event->params['stock_order_id'],$event->params['location_to']);
                    }else{
                        Log::info("receiveFinishUpdate : API KEY or URL is not configured for seller :: ".$event->params['seller_id']);
                    }
                } else {
                    $message .= 'Unity Response | '.$unity_response['message'];
                }
                
            } else {
                $message .= 'Unity Response Code | '.$httpCode;
            }
        }
        catch(Exception $e){
            $seller_id = $event->params['seller_id'];

            $message .= 'CreateStockOrderThroughUnityForGtechEvent  API Error | ';
            $activity_id = activity()
            ->withProperties(['response' => json_encode($e->getMessage()) , 'dump' => $e->getTraceAsString()])
            ->log('Get Order Origin Process');

            $message .= $e->getMessage();
            Log::info($e->getTraceAsString());
            // Mail::raw($e->getMessage(), function ($m) use ($seller_id) {
            //     $m->to('<EMAIL>')->subject('Seller :: '.$seller_id.' - DYNAMICS CreateStockOrderThroughUnity : failiure');
            // });
                // Mail::raw($message.' | '.$e->getMessage(), function ($m)  use ($unity) {
                //     $m->to($unity)
                //         ->subject('Send Order Origin Process');
                // });
            
            Log::info('CreateStockOrderThroughUnityForGtechEvent error '.$e->getMessage());
        }

        if($process_status == 'Failed') {
            Mail::raw('CreateStockOrderThroughUnityForGtechEvent Process | '.$message, function ($m)  use ($seller_id) {
                $m->to('<EMAIL>')->subject('Seller :: '.$seller_id.' - GTECH CreateStockOrderThroughUnityForGtechEvent : Process Failed');
            });
        }
       
    }
}
