<?php

namespace App\Listeners;

use App\Events\ReturnSalesJdotEvent;
use App\Models\ReturnReceive;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;

use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use App\Models\FulfillmentOrder;
use App\Models\Setting;
use App\Models\Shipment;
use App\Traits\ExceptionTrait;
use Carbon\Carbon;
use App\Models\FulfillmentOrderGtechUpdates;
use GuzzleHttp\Client;
use Exception;
use App\Models\RMARequest;
use App\Models\RMAItems;
use App\Models\Order;
use App\Models\OrderItem;
use App\Events\CreateCreditMemoOnJdotMagentoEvent;

class ReturnSalesJdotListener implements ShouldQueue
{
    public $queue = "returnSalesJdotListener";
    use ExceptionTrait;
    use InteractsWithQueue;

    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     *
     * @param  SendShipmentDetailsEvent  $event
     * @return void
     */
    public function handle(ReturnSalesJdotEvent $event)
    {
        Log::info("ReturnSalesJdotEvent Event Started Order with courier : ".$event->data['cn']);
        // $event->jdot_erp_obj->returnSalesApi($event->data);   
        $shipment = $event->data['shipment'];
        $return_receive_id = ReturnReceive::whereOrderId($shipment->order_id)->value('id');

        if($shipment->type == "reverse"){
            $rma_shipment = RMARequest::where('return_shipment_id',$shipment->id)->first();  
            if($rma_shipment){
                $rma_items = RMAItems::where('rma_requests_id', $rma_shipment->id)->get();
                $fulfilment_order = FulfillmentOrder::where('shipment_id',$rma_shipment->shipment_id)->select('id','reference_id','seller_location_id')->first();
                $original_shipment = Shipment::find($rma_shipment->shipment_id);
    
                if($fulfilment_order){
                    $event->jdot_erp_obj->createSalesOrderServiceForRMA($event->data,$return_receive_id,$original_shipment);        
                }else{
                    \Log::info("ReturnSalesJdotEvent : Unable to find fulfiment order id for reverse  shipment :: ".$shipment->id." - SellerID - ".$shipment->seller_id);
                }    
            }else{
                \Log::info("ReturnSalesJdotEvent : The reverse shipment was of Exchange type :: ".$shipment->id." - SellerID - ".$shipment->seller_id);
            }

        }else{
            $fulfilment_order = FulfillmentOrder::where('shipment_id',$shipment->id)->select('id','reference_id','seller_location_id')->first();
            if($fulfilment_order){
                $event->jdot_erp_obj->createSalesOrderServiceForRetrurn($event->data, $return_receive_id);    
                
                // This is to create credit memo in Magento
                if($shipment->seller_id == env('JDOT_MAGENTO_SELLER_ID')){
                    $jdotMagentoAPItoken = Setting::where('seller_id', $shipment->seller_id)->where('key', 'jdotMagentoAPItoken')->first();
                    $WC = Setting::where('seller_id', $shipment->seller_id)->where('key', 'WC')->first();
                    if($WC){
                        // $token = "yj6oli4sx84fic97nn35qujd31j482of";
                        $token = $jdotMagentoAPItoken->value;
        
                        Log::info("The token generated by Magento 2");
                        Log::info($token);
                        $data['url'] = $WC->value;
                        $data['magento_token'] = $token;
        
                        $order = Order::find($shipment->order_id);
                        
                        $data['magento_order_id'] = $order->marketplace_id;
                        $data['shipment_id'] = $shipment->id;

                        $comment = "Credit Memo for the order has been created through Unity Retail";
                        $data['comment'] =  $comment;
                        $data['magento_order_id'] = $order->marketplace_id;
                        $data['unity_order_id'] = $order->id;
                        $data['magento_token'] = $token;
                        $data['order_items'] =  $order->items;

                        event(new CreateCreditMemoOnJdotMagentoEvent($data));
                    }
                }

            }else{
                \Log::info("ReturnSalesJdotEvent : Unable to find fulfiment order id for shipment :: ".$shipment->id." - SellerID - ".$shipment->seller_id);
            }
        }
       
     
    }
}
