<?php

namespace App\Listeners;

use App\Events\UpdateStatusOnMagnetoForJdotEvent;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;

use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use App\Models\FulfillmentOrder;
use App\Models\Setting;
use App\Models\Shipment;
use App\Traits\ExceptionTrait;
use Carbon\Carbon;
use App\Models\FulfillmentOrderGtechUpdates;
use GuzzleHttp\Client;
use Exception;
use App\Helpers\OrderComment;
use App\Models\ShipmentHistory;


class UpdateStatusOnMagnetoForJdotListener implements ShouldQueue
{
    public $queue = "updateStatusOnMagnetoForJdotListener";
    use ExceptionTrait;
    use InteractsWithQueue;

    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     *
     * @param  SendShipmentDetailsEvent  $event
     * @return void
     */
    public function handle(UpdateStatusOnMagnetoForJdotEvent $event)
    {

        Log::info("UpdateStatusOnMagnetoForJdotEvent Event Started Order with  : ".$event->data['magento_order_id']);

        $data = $event->data;
        Log::info($data);
        // forwarded_order,delivered,complete,under_inprocess
        // "notify" => true
        $notify_statuses = ['delivered','under_inprocess'];

        if(in_array($data['order_status'],$notify_statuses)){
            $params = array("comment" =>  $data['comment'] , "is_customer_notified" => 1  ,"status" => $data['order_status']);
        }else{
            $params = array("comment" =>  $data['comment'] , "status" => $data['order_status']);
        }

        $params = array("statusHistory" => $params);

        Log::info($params);
        $params = json_encode($params);

        Log::info($data['url'].'/rest/V1/orders/'.$data['magento_order_id'].'/comments');


        $curl = curl_init();

        curl_setopt_array($curl, array(
          CURLOPT_URL => $data['url'].'/rest/V1/orders/'.$data['magento_order_id'].'/comments',
          CURLOPT_RETURNTRANSFER => true,
          CURLOPT_ENCODING => '',
          CURLOPT_MAXREDIRS => 10,
          CURLOPT_TIMEOUT => 0,
          CURLOPT_FOLLOWLOCATION => true,
          CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
          CURLOPT_CUSTOMREQUEST => 'POST',
          CURLOPT_POSTFIELDS =>$params,
          CURLOPT_HTTPHEADER => array(
            'Content-Type: application/json',
            'Authorization: Bearer '.$data['magento_token']
          ),
        ));
        
        $response = curl_exec($curl);
        $httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);

        curl_close($curl);

        if ($httpCode == '200' && $response == 'true') {
          Log::info('UpdateStatusOnMagnetoForJdotEvent :: Jdot Magento Order Status Api Request Success');
            $message = $data['comment'];
            $key = 'Status Synced Process';
            $status = $data['order_status'];
            $order_id = $data['unity_order_id'];
            
            if(isset($data['shipment_history_id'])){
              $shipment_history = ShipmentHistory::find($data['shipment_history_id']);

              $shipment_history->storefront_sync = 2;
              $shipment_history->storefront_sync_at = Carbon::now()->toDateTimeString();
              $shipment_history->save();
            }

            OrderComment::add(compact('order_id','message','key','status'));            

        } else{
          Log::info("UpdateStatusOnMagnetoForJdotEvent :: Response :: ");
          Log::info($response);
        }


        Log::info("UpdateStatusOnMagnetoForJdotEvent :: End :: ");

    }
}
