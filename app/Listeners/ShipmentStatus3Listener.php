<?php

namespace App\Listeners;

use App\Events\ShipmentStatus3Event;
use App\Helpers\OrderComment;
use App\Models\Shipment;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use App\Models\ShipmentCourierHistory;
use App\Service\ShipmentStatusSync;
use App\Traits\ExceptionTrait;
use Exception;

class ShipmentStatus3Listener implements ShouldQueue
{
    public $queue = "shipmentStatusSync3";
    use ExceptionTrait;

    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     *
     * @param  ShipmentStatus3Event  $event
     * @return void
     */
    public function handle(ShipmentStatus3Event $event)
    {
        (new ShipmentStatusSync())->khaadiSync($event);
    }
}
