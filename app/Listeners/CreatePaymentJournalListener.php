<?php

namespace App\Listeners;

use App\Events\CreatePaymentJournalEvent;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;

use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use App\Models\FulfillmentOrder;
use App\Models\Setting;
use App\Models\Shipment;
use App\Traits\ExceptionTrait;
use Carbon\Carbon;
use App\Models\FulfillmentOrderGtechUpdates;
use GuzzleHttp\Client;
use Exception;

class CreatePaymentJournalListener implements ShouldQueue
{
    public $queue = "createPaymentJournalListener";
    use ExceptionTrait;
    use InteractsWithQueue;

    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     *
     * @param  SendShipmentDetailsEvent  $event
     * @return void
     */
    public function handle(CreatePaymentJournalEvent $event)
    {
        Log::info("CreatePaymentJournalEvent Event Started Order with courier : ".$event->data['courier_id']);
        $event->jdot_erp_obj->createPaymentJournal($event->data);        
    }
}
