<?php

namespace App\Listeners;

use App\Events\ReturnSalesZubaidasEvent;
use App\Helpers\ZubaidasERP;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;

use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use App\Models\FulfillmentOrder;
use App\Models\Setting;
use App\Models\Shipment;
use App\Traits\ExceptionTrait;
use Carbon\Carbon;
use GuzzleHttp\Client;
use Exception;
use App\Models\RMARequest;
use App\Models\RMAItems;
use App\Models\Order;
use App\Models\OrderItem;
use App\Service\Integration\ERP\ERPForZubaidas;

class ReturnSalesZubaidasListener implements ShouldQueue
{
    public $queue = "returnSalesZubaidasListener";
    use ExceptionTrait;
    use InteractsWithQueue;

    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     *
     * @param  SendShipmentDetailsEvent  $event
     * @return void
     */
    public function handle(ReturnSalesZubaidasEvent $event)
    {
        Log::info("ReturnSalesZubaidasEvent | Event Started Order with courier < {$event->data['cn']} >");

        $zubaidas_service = new ERPForZubaidas();
        $zubaidas_helper = new ZubaidasERP($zubaidas_service);

        $shipment = $event->data['shipment'];
        $internalParams = [
            'order_id' => $shipment->order->id,
            'seller_id' => $shipment->seller_id
        ];

        $params = [
            'shipment' => $shipment,
            'internal_params' => $internalParams,
            'rma' => false
        ];

        if ($shipment->type === "reverse") {
            $this->processReverseShipment($shipment, $params, $zubaidas_helper);
        } else {
            $this->processRegularShipment($shipment, $params, $zubaidas_helper);
        }

        Log::info("ReturnSalesZubaidasEvent | Event Completed Order with courier < {$event->data['cn']} >");
    }

    private function processReverseShipment($shipment, &$params, $zubaidas_helper_obj)
    {
        $rma_shipment = RMARequest::where('return_shipment_id', $shipment->id)->first();

        if ($rma_shipment) {
            $rma_items = RMAItems::with(['items.order_item'])
                                ->where('rma_requests_id', $rma_shipment->id)
                                ->firstOrFail();
            Log::info("The RMA Items are");
            Log::info($rma_items);
            $original_shipment = Shipment::find($rma_shipment->shipment_id);

            $params['rma'] = true;
            $params['rma_items'] = $rma_items;
            $params['shipment'] = $original_shipment;
            $zubaidas_helper_obj->createReturnOrder($params);
        }
    }

    private function processRegularShipment($shipment, $params, $zubaidas_helper_obj)
    {
        $zubaidas_helper_obj->createReturnOrder($params);
    }

}
