<?php

namespace App\Listeners;

use App\Events\ShipmentStatus2Event;
use App\Helpers\OrderComment;
use App\Models\Shipment;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use App\Models\ShipmentCourierHistory;
use App\Service\ShipmentStatusSync;
use App\Traits\ExceptionTrait;
use Exception;

class ShipmentStatus2Listener implements ShouldQueue
{
    public $queue = "shipmentStatusSync2";
    use ExceptionTrait;

    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     *
     * @param  ShipmentStatus2Event  $event
     * @return void
     */
    public function handle(ShipmentStatus2Event $event)
    {
        (new ShipmentStatusSync())->khaadiSync($event);
    }
}
