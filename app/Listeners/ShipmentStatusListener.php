<?php

namespace App\Listeners;

use App\Events\ShipmentStatusEvent;
use App\Helpers\OrderComment;
use App\Models\Shipment;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use App\Models\ShipmentCourierHistory;
use App\Service\ShipmentStatusSync;
use App\Traits\ExceptionTrait;
use Exception;

class ShipmentStatusListener implements ShouldQueue
{
    public $queue = "shipmentStatusSync";
    use ExceptionTrait;

    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     *
     * @param  ShipmentStatusEvent  $event
     * @return void
     */
    public function handle(ShipmentStatusEvent $event)
    {
        (new ShipmentStatusSync())->khaadiSync($event);
    }
}
