<?php

namespace App\Listeners;

use App\Events\SendCancelledStatusToGtechEvent;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;

use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use App\Models\FulfillmentOrder;
use App\Models\Setting;
use App\Models\Shipment;
use App\Traits\ExceptionTrait;
use Carbon\Carbon;
use App\Models\FulfillmentOrderGtechUpdates;
use App\Helpers\Gtech;

class SendCancelledStatusToGtechListener implements ShouldQueue
{
    public $queue = "sendCancelledStatusToGtech";
    use ExceptionTrait;

    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     *
     * @param  SendShipmentDetailsEvent  $event
     * @return void
     */
    public function handle(SendCancelledStatusToGtechEvent $event)
    {
        $shipment = (object)$event->shipment;
        $fulfilment_order = FulfillmentOrder::where('shipment_id',$shipment->id)->select('id','reference_id','seller_location_id')->first();
        if($fulfilment_order){
            $api_key = Setting::where('seller_id',$shipment->seller_id)->where('key', config('enum.api_keys')['GTECH_SYSTEM_API_KEY'])->first();
            $url = Setting::where('seller_id',$shipment->seller_id)->where('key', config('enum.api_keys')['GTECH_SYSTEM_API_URL'])->first();
            if($api_key && $url){
                $gtech = new Gtech($api_key->value,$url->value);
                $gtech->SetOrderStatusCancelled($fulfilment_order);
            }else{
                \Log::info("SendDeliveredShipmentDetailsToGtechEvent : API KEY or URL is not configured for seller :: ".$shipment->seller_id);
            }
           
        }
       
    }
}
