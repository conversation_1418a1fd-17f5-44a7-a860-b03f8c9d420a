<?php

namespace App\Listeners;

use App\Events\SendReturnShipmentDetailsToGTECHEvent;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;

use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use App\Models\FulfillmentOrder;
use App\Models\Setting;
use App\Models\Shipment;
use App\Traits\ExceptionTrait;
use Carbon\Carbon;
use App\Helpers\Gtech;
use App\Models\RMARequest;
use App\Models\RMAItems;

class SendReturnShipmentDetailsToGTECHListener implements ShouldQueue
{
    public $queue = "sendReturnShipmentDetailToGtech";
    use ExceptionTrait;

    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     *
     * @param  SendShipmentDetailsEvent  $event
     * @return void
     */
    public function handle(SendReturnShipmentDetailsToGTECHEvent $event)
    {
        $shipment = (object)$event->shipment;
     
        $api_key = Setting::where('seller_id',$shipment->seller_id)->where('key', config('enum.api_keys')['GTECH_SYSTEM_API_KEY'])->first();
        $url = Setting::where('seller_id',$shipment->seller_id)->where('key', config('enum.api_keys')['GTECH_SYSTEM_API_URL'])->first();
        if($api_key && $url){
            
            $gtech = new Gtech($api_key->value,$url->value);

            if($shipment->type == "reverse"){
                $rma_shipment = RMARequest::where('return_shipment_id',$shipment->id)->first();  
                $rma_items = RMAItems::where('rma_requests_id', $rma_shipment->id)->get();
                $fulfilment_order = FulfillmentOrder::where('shipment_id',$rma_shipment->shipment_id)->select('id','reference_id','seller_location_id')->first();
                $original_shipment = Shipment::find($rma_shipment->shipment_id);

                if($fulfilment_order){
                    $gtech->posOnlineForRMA($fulfilment_order,$shipment,$rma_items,$original_shipment);
                }else{
                    \Log::info("SendReturnShipmentDetailsToGTECHEvent : Unable to find fulfiment order id for reverse  shipment :: ".$shipment->id." - SellerID - ".$shipment->seller_id);
                }

            }else{
                $fulfilment_order = FulfillmentOrder::where('shipment_id',$shipment->id)->select('id','reference_id','seller_location_id')->first();
                if($fulfilment_order){
                    $gtech->posOnline($fulfilment_order,$shipment);
                }else{
                    \Log::info("SendReturnShipmentDetailsToGTECHEvent : Unable to find fulfiment order id for shipment :: ".$shipment->id." - SellerID - ".$shipment->seller_id);
                }
            }
        }else{
            \Log::info("SendReturnShipmentDetailsToGTECHEvent : API KEY or URL is not configured for seller :: ".$shipment->seller_id);
        }


       
    }
}
