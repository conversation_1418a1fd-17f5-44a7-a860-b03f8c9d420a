<?php

namespace App\Listeners;

use App\Events\SendCancelledStatusToCandelaEvent;
use App\Helpers\Candela;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;

use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use App\Models\FulfillmentOrder;
use App\Models\Setting;
use App\Models\Shipment;
use App\Traits\ExceptionTrait;
use Carbon\Carbon;


class SendCancelledStatusToCandelaListener implements ShouldQueue
{
    public $queue = "sendCancelledStatusToCandela";
    use ExceptionTrait;

    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     *
     * @param  SendShipmentDetailsEvent  $event
     * @return void
     */
    public function handle(SendCancelledStatusToCandelaEvent $event)
    {
        $shipment = (object)$event->shipment;
        $fulfilment_order = FulfillmentOrder::where('shipment_id',$shipment->id)->select('id','reference_id','seller_location_id')->first();
        if($fulfilment_order){
          
            $api_key = Setting::where('seller_id',$shipment->seller_id)->where('key', config('enum.api_keys')['CANDELA_SYSTEM_API_KEY'])->first();
            $api_id = Setting::where('seller_id',$shipment->seller_id)->where('key', config('enum.api_keys')['CANDELA_SYSTEM_API_ID'])->first();
            $url = Setting::where('seller_id',$shipment->seller_id)->where('key', config('enum.api_keys')['CANDELA_SYSTEM_API_URL'])->first();
           
            if($api_key && $api_id && $url){
                $candela = new Candela($api_key->value, $api_id->value, $url->value);
                $candela->SetOrderStatusCancelled($fulfilment_order);
            }else{
                \Log::info("SendDeliveredShipmentDetailsToCandelaEvent : API KEY or URL is not configured for seller :: ".$shipment->seller_id);
            }
           
        }
       
    }
}
