<?php

namespace App\Listeners;

use App\Events\ReturnPostedMagentoSyncEvent;
use App\Events\ShipmentReturnStatusEvent;
use App\Helpers\DynamicAccessToken;
use App\Helpers\OrderComment;
use App\Models\Shipment;
use App\Models\ShipmentCourierHistory;
use App\Traits\ExceptionTrait;
use Exception;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;

class ShipmentReturnStatusListener implements ShouldQueue
{
    public $queue = "khaadiReturnStatus";
    use ExceptionTrait;
    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     *
     * @param  ShipmentReturnStatusEvent  $event
     * @return void
     */
    public function handle(ShipmentReturnStatusEvent $event)
    {
        Log::info('Shipment Return status Listener started');
        $orders = $event->orders;
        $order_array = [];
        Log::info($orders);
        $request_url = null;
        $request_data = null;
        $response_data = null;

        foreach($orders as $order){
            array_push($order_array,$order['marketplace_reference_id']);
        }

        $data = [
            '_contract' => [
                'PaginationModel' => [
                    "PageSize" => 300,
                    "PageNumber" => 1,
                    "GeTotalRecords" => true
                ],
                "OrderReferences" => $order_array,
                "ReturnOrderStatus" => "Full"  
            ]
        ];

        Log::info('Shipment Return status Listener | getting token');
        $token = DynamicAccessToken::getToken();
        Log::info('Shipment Return status Listener | got token');
        $order_string = '';
        Log::info($token);
        if($token['error'] == 0)
        {
            try {
                Log::info('Shipment Return status Listener | d365 api starting');

                $curl = curl_init();
                $request_url = env('D365_URL').'/api/services/SLD_UnityRetailService/SLD_UnityRetailService/GetReturnOrderStatus';
                Log::info(json_encode($data));
                curl_setopt_array($curl, array(
                CURLOPT_URL => $request_url,
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_ENCODING => "",
                CURLOPT_MAXREDIRS => 10,
                CURLOPT_TIMEOUT => 60,
                CURLOPT_FOLLOWLOCATION => true,
                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                CURLOPT_CUSTOMREQUEST => "POST",
                CURLOPT_POSTFIELDS =>json_encode($data),
                CURLOPT_HTTPHEADER => array(
                    "Content-Type: application/json",
                    "Authorization: bearer ".$token['token'].""
                ),
                ));
    
                $response = curl_exec($curl);
                $response_data = json_decode($response,true);
    
                $httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
    
                Log::info('Shipment Return status Listener | d365 got data');

                Log::info($response_data);

                if(isset($response_data) && count($response_data['Orders']) > 0){
                    foreach($response_data['Orders'] as $order){
                        event(new ReturnPostedMagentoSyncEvent($order));
                    }
                } else{
                    Log::info("no order is returned:ReturnStatusListener");
                }
                
            } catch(Exception $e){
                Log::info('catch: ReturnStatusListener'.$e->getMessage());
            }
        }
        Log::info('Shipment Return status Listener ended');
    }
}
