<?php

namespace App\Listeners;

use App\Events\SendOrderOriginEvent;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;

use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use App\Helpers\OrderComment;
use App\Models\SellerLocation;
use Exception;
use GuzzleHttp\Client;

class SendOrderOriginListener implements ShouldQueue
{
    public $queue = "sendOriginToUnity";

    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     *
     * @param  SendOrderOriginEvent  $event
     * @return void
     */
    public function handle(SendOrderOriginEvent $event)
    {
        Log::info("SendOrderOriginEvent Event Started Order with order : ".$event->order);

        $process_status = 'Failed';
        $message = '';
        $unity = explode(',',env('UNITY_POC'));
        $others = explode(',',env('KHAADI_POC'));
        $params = ['order_id' => $event->order, 'location_id' =>  $event->seller_location, 'seller_id' => 119];

        try {
            
            $client = new Client();

            $response = $client->post(env('ENDPOINT_URL')."/api/order/set-origin", [
                'timeout' => 160, // Response timeout
                'connect_timeout' => 30, // Connection timeout
                'verify' => false,
                'headers' => [
                    'Content-Type' => 'application/json',
                    'Authorization' => base64_encode(hash_hmac('sha256', json_encode($params), 'enterprise_order_location_sync', true)),
                ],
                'body' => json_encode($params)
            ]);

            $unity_response = json_decode($response->getBody()->getContents(), TRUE);
            $httpCode = $response->getStatusCode();
            
            Log::info("SendOrderOriginEvent Response Code: ".$httpCode.' | '.$event->order);
            Log::info("SendOrderOriginEvent Response : ". json_encode($unity_response).' | '.$event->order);

            if($httpCode == '200') {

                if($unity_response['error'] == 0) {
                    $process_status = 'Success';
                } else {
                    $message .= 'Unity Response | '.$unity_response['message'];
                }
                
            } else {
                $message .= 'Unity Response Code | '.$httpCode;
            }
        }
        catch(Exception $e){
            $message .= 'Send Order Origin to Unity API Error';
            $activity_id = activity()
            ->withProperties(['response' => json_encode($e->getMessage()) , 'dump' => $e->getTraceAsString()])
            ->log('Get Order Origin Process');

                Mail::raw($message.' | '.$e->getMessage(), function ($m)  use ($unity) {
                    $m->to($unity)
                        ->subject('Send Order Origin Process');
                });
            
            Log::info('Send Order Origin process error '.$e->getMessage());
        }

        if($process_status == 'Failed') {
            Mail::raw('Send Order Origin to Unity Process | '.$message.' | Order ID: '.$event->order, function ($m)  use ($unity) {
                $m->to($unity)
                    ->subject('Send Order Origin to Unity Process');
            });
        }
        
    }
}
