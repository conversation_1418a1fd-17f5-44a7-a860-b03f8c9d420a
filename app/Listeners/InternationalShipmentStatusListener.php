<?php

namespace App\Listeners;

use App\Events\InternationalShipmentStatusEvent;
use App\Models\Shipment;
use App\Helpers\OrderComment;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use Carbon\Carbon;
use App\Models\ShipmentHistory;
use App\Traits\ExceptionTrait;
use Exception;
use DB;

class InternationalShipmentStatusListener implements ShouldQueue
{
    public $queue = "InternationalShipmentStatusSync";
    use ExceptionTrait;

    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     *
     * @param  InternationalShipmentStatusEvent  $event
     * @return void
     */
    public function handle(InternationalShipmentStatusEvent $event)
    {
        $request_url = null;
        $request_data = null;
        $response_data = null;
        $terminal = false;
        $terminal_status = ['Delivered', 'Cancelled', 'Return', 'Lost'];
        $force_sync = $event->force_sync;
        $ignore_status_sync = false;


        // $booked_statuses = [
        //     'Booked'
        // ];

        $ready_for_dispatch = [
            'Ready for Dispatch'
        ]; // Unity Status

        $complete = [
            'Dispatched'
        ];

        $in_transit = [
            'Pending Delivery'
        ];

        $delivered = [
            'Delivered'
        ];

        $return_intransit = [
            'Pending Return'
        ];

        $returned = [
            'Return'
        ];

        $parcel_lost = [
            'Lost'
        ];

        $ignore_statuses = [
            'Booked',
            'Cancelled'
        ];
        
        $shipment_history = (object) $event->shipment;
        $status = $shipment_history->status;
        $api_status = '';
        Log::info($event->shipment);
        $rs_stat = 0;

        if($shipment_history->type == null){

            $process_status = 'Failed';
            $message = "Tracking status not found in KHAADI mapped status list for <br> Tracking number : <b>".$shipment_history->tracking_number."</b> <br>".$shipment_history->name." Status : <b>".$status."</b>";

            if (in_array($status, $ready_for_dispatch)) {  
                $api_status = 'Readyfordispatch';  
            } else if (in_array($status, $complete)) {          
                $api_status = 'complete';
            } else if (in_array($status, $in_transit)) {          
                $api_status = 'Intransit';
            } else if (in_array($status, $delivered)) {
                $terminal = true;
                $api_status = 'delivered';
            } else if (in_array($status, $return_intransit)) {
                $api_status = 'return_instransit';
            } else if (in_array($status, $returned)) {
                $terminal = true;
                $api_status = 'returned';
            } else if (in_array($status, $parcel_lost)) {
                $terminal = true;
                $api_status = 'Parcel_lost';
            }

            if ($api_status) {
                
                try {

                    if (    !$force_sync && 
                            !$terminal && 
                            in_array(DB::connection('mysql2')->table('shipments')->where('id', $shipment_history->shipment_id)->value('status'), $terminal_status) &&
                            ShipmentHistory::where('shipment_id', $shipment_history->shipment_id)->where('storefront_sync',2)->whereIn('status', array_merge($parcel_lost, $delivered, $returned))->exists() 
                        ) {
                            $ignore_status_sync = true;
                            $message = "Status not synced because Terminal tracking status is already synced for this <br> Tracking number : <b>".$shipment_history->tracking_number."</b> <br>".$shipment_history->name." Status : <b>".$status."</b>";
                    } else {
        
                        $curl = curl_init();
                        $comment = "Status Update by <b>Unity</b> for <br> Tracking number : <b>".$shipment_history->tracking_number."</b> <br>
                        Khaadi Status : <b>".$api_status."</b> <br> Unity Status : <b>".$status."</b> <br> Status Time : <b>".$shipment_history->status_at."</b>";
            
                        $data = array("statusHistory" => array("comment" => $comment , "status" => $api_status) );
                        $request_data = $data;

                        $request_url = env('KHAADI_URL')."/rest/V1/orders/".$shipment_history->entity_id."/comments";
                        curl_setopt_array($curl, array(
                        CURLOPT_URL => $request_url,
                        CURLOPT_RETURNTRANSFER => true,
                        CURLOPT_ENCODING => "",
                        CURLOPT_MAXREDIRS => 10,
                        CURLOPT_TIMEOUT => 60,
                        CURLOPT_FOLLOWLOCATION => true,
                        CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                        CURLOPT_CUSTOMREQUEST => "POST",
                        CURLOPT_POSTFIELDS => json_encode($data),
                        CURLOPT_HTTPHEADER => array(
                            "Authorization: Bearer ".env('KHAADI_TOKEN'),
                            "Content-Type: application/json"
                        ),
                        ));
            
                        $response = curl_exec($curl);
                        $response_data = $response;
            
                        $httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);

                        if ($httpCode == '200' && $response == 'true') {
                            Log::info('Khaadi International Order Status Api Request Success');
                            $process_status = 'Success';
                            $message = $comment." <br>Comment API Response : <b>".$response."</b>  <br>Khaadi Status : <b>'.$api_status.'</b> ";
                        } elseif ($httpCode == '200' && json_encode($response) == '"\ntrue"') {
                            Log::info('Khaadi International Order Status Api Request Success');
                            $process_status = 'Success';
                            $message = $comment." <br>Comment API Response : <b>".json_encode($response)."</b>  <br>Khaadi Status : <b>'.$api_status.'</b> ";
                        } else {
                            $message = 'Tracking status not updated at storefront | '.json_encode($response).' | '.$api_status.' | '.$shipment_history->marketplace_reference_id.' | '.$shipment_history->tracking_number;
                            // Mail::raw($message, function ($m)  {
                            //     $m->to('<EMAIL>')
                            //         ->bcc('<EMAIL>')
                            //         ->subject('Shipment Tracking Storefront Synced Process');
                            // });


                            $this->addException(    120,
                                                    config('enum.exception_types')['SHIPMENTSTATUSSYNC'],
                                                    ( strlen($message) > 250 ? substr($message,0,240) . "..." : $message ),
                                                    'Order',
                                                    $shipment_history->order_id,
                                                    $shipment_history->marketplace_reference_id,
                                                    json_encode(['message' => $message, 'request_url' => $request_url, 'query_data' => $event->shipment]),
                                                    ( $request_data ? json_encode($request_data) : null),
                                                    ( $response_data ? json_encode($response_data) : null)
                                                );
                        }

                        
                        curl_close($curl);
                        Log::info('International Shipment Status Api response '.json_encode($response));
                    }
                    
                } catch(\Exception $e){

                    $message = 'Tracking status not updated at storefront | '.$shipment_history->marketplace_reference_id;
                    $activity_id = activity()
                    ->withProperties(['response' => json_encode($e->getMessage()) , 'dump' => $e->getTraceAsString()])
                    ->log('Khaadi International Order Status Api');

                    // Mail::raw($message.' | '.$e->getMessage(), function ($m)  {
                    //     $m->to('<EMAIL>')
                    //         ->bcc('<EMAIL>')
                    //         ->subject('Shipment Tracking Storefront Synced Process');
                    // });
                    
                    Log::info('International Shipment Status Api error '.$e->getMessage());
                }
            } else if (in_array($status, $ignore_statuses)) {
                $ignore_status_sync = true;
            } else {
                $ignore_status_sync = true;
                // Mail::raw($message, function ($m)  {
                //     $m->to('<EMAIL>')->subject('International Shipment Tracking Storefront Synced Process');
                // });
            }

            $status = $process_status;
            $order_id = $shipment_history->order_id;
            $key = 'Shipment Tracking Storefront Synced Process';
            Log::info($message);
        }
        else{
            
            // $ship_stat = Shipment::whereTrackingNumber($shipment_history->tracking_number)->whereOrderId($shipment_history->order_id)->where('type','reverse')->first();
            // if(isset($ship_stat) && $ship_stat->status = 'Delivered'){
            //     try{
            //         $process_status = 'Failed';
            //         $request_url = config('enum.env_vars')['KHAADIURL'].'/rest/V1/custom-rmaunity/receivereturnshipment?order_id='.$shipment_history->marketplace_reference_id.'&details=Delivered&cn='.$shipment_history->tracking_number;

            //         $curl = curl_init();
            //         curl_setopt_array($curl, array(
            //         CURLOPT_URL => $request_url,
            //         CURLOPT_RETURNTRANSFER => true,
            //         CURLOPT_ENCODING => '',
            //         CURLOPT_MAXREDIRS => 10,
            //         CURLOPT_TIMEOUT => 0,
            //         CURLOPT_FOLLOWLOCATION => true,
            //         CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            //         CURLOPT_CUSTOMREQUEST => 'GET',
            //         CURLOPT_HTTPHEADER => array(
            //             'Authorization: Bearer '.config('enum.env_vars')['REVERSESYNCTOKEN'],
            //             ),
            //         ));

            //         $response = curl_exec($curl);

            //         $httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);

            //         if ($httpCode == '200') {
            //             Log::info('Reverse Shipment Delivered Status Sync Api Request Success');
            //             $process_status = 'Success';
            //             $message = "Reverse Shipment Delivered Status Sync Success";
            //         } else {
            //             $message = 'Reverse Shipment Delivered Status not updated at storefront | '.json_encode($response).' | '.$shipment_history->marketplace_reference_id.' | '.$shipment_history->tracking_number;
            //             // Mail::raw($message, function ($m)  {
            //             //     $m->to(['<EMAIL>','<EMAIL>','<EMAIL>','<EMAIL>',env('KHAADI_POC')])->subject('Reverse Shipment Delivered Status Synced Process');
            //             // });


            //             $this->addException(    119,
            //                                     config('enum.exception_types')['MAGENTOREVERSESHIPMENTSYNC'],
            //                                     ( strlen($message) > 250 ? substr($message,0,240) . "..." : $message ),
            //                                     'Order',
            //                                     $shipment_history->order_id,
            //                                     $shipment_history->marketplace_reference_id,
            //                                     json_encode(['message' => $message, 'request_url' => $request_url, 'query_data' => $event->shipment]),
            //                                     ( $request_url ? $request_url : null),
            //                                     ( $response ? json_encode($response) : null)
            //                                 );
            //         }

            //         $status = $process_status;
            //         $order_id = $shipment_history->order_id;
            //         $key = 'Reverse Shipment Delivered Status Synced Process';
            //         Log::info($message);

            //         curl_close($curl);

            //     } catch(Exception $e){
            //         $message = 'Reverse Shipment Delivered Status not updated at storefront | '.$shipment_history->marketplace_reference_id;
            //         $activity_id = activity()
            //         ->withProperties(['response' => json_encode($e->getMessage()) , 'dump' => $e->getTraceAsString()])
            //         ->log('Reverse Shipment Delivered Status Synced Process');

            //         // Mail::raw($message.' | '.$e->getMessage(), function ($m)  {
            //         //     $m->to(['<EMAIL>','<EMAIL>','<EMAIL>',env('KHAADI_POC')])->subject('Reverse Shipment Delivered Status Synced Process');
            //         // });
                    
            //         Log::info('Reverse Shipment Delivered Status Synced Process Api error '.$e->getMessage());
            //     }
                
            // }
            // else{
            //     $rs_stat = 1;
            //     Log::info('Alfred | Reverse Shipment other than Delivered');
            // }

        }

        if($rs_stat == 1){

        } else{
            OrderComment::add(compact('order_id','message','key','status'));
        }

        if (!$ignore_status_sync) {
            if ($status == 'Success') {
                ShipmentHistory::where('id', $shipment_history->id)->update(['storefront_sync' => 2, 'storefront_sync_at' => Carbon::now()->toDateTimeString() ]);
            } else {
                ShipmentHistory::where('id', $shipment_history->id)->update(['storefront_sync' => 0 ]);
            }
        } else {
            ShipmentHistory::where('id', $shipment_history->id)->update(['storefront_sync' => 2, 'storefront_sync_at' => NULL]);
        }
    }
}
