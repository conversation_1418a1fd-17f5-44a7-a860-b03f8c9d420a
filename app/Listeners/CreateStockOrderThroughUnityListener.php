<?php

namespace App\Listeners;

use App\Events\CreateStockOrderThroughUnityEvent;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;

use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use App\Models\FulfillmentOrder;
use App\Models\Setting;
use App\Models\Shipment;
use App\Traits\ExceptionTrait;
use Carbon\Carbon;
use App\Models\FulfillmentOrderGtechUpdates;
use GuzzleHttp\Client;
use Exception;

class CreateStockOrderThroughUnityListener implements ShouldQueue
{
    public $queue = "createStockOrderThroughUnityListener";
    use ExceptionTrait;
    use InteractsWithQueue;

    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     *
     * @param  SendShipmentDetailsEvent  $event
     * @return void
     */
    public function handle(CreateStockOrderThroughUnityEvent $event)
    {
        Log::info("CreateStockOrderThroughUnityEvent Event Started Order with order : ".$event->stock_order['transferOrderId']);

        $process_status = 'Failed';
        $message = 'CreateStockOrderThroughUnityEvent | '.$event->stock_order['transferOrderId'].' | ';
        $unity = explode(',',env('UNITY_POC'));
        $others = explode(',',env('KHAADI_POC'));
        $line_items = [];
        foreach($event->stock_order['CfzInventorySyncContract'] as $item){
            $temp_arr = [];
            $temp_arr['barcode'] = $item['barcode'];
            $temp_arr['sku'] = $item['sku'];
            $temp_arr['qty'] = $item['qty'];
            $line_items[] = $temp_arr;
        }

        $date = strtotime(trim($event->stock_order['orderCreationDateTime']));
        $stock_order_created_date = date('Y-m-d', $date);

        $date = strtotime(trim($event->stock_order['expectedArrivalDate']));
        $expected_arival_date = date('Y-m-d', $date);

        $params = ['stock_order_id' => $event->stock_order['transferOrderId'], 'location_to' => $event->stock_order['inventLocationTo'],
        'location_from' => $event->stock_order['inventLocationFrom'],'arrival_date' => $expected_arival_date,
        'stock_order_created_date' => $stock_order_created_date  , 'line_items' => $line_items ];

        $api_key = Setting::where('seller_id',$event->seller_id)->where('key', config('enum.api_keys')['ERP_API_KEY'])->first();

        Log::info("CreateStockOrderThroughUnityEvent ERP API KEY: ".$api_key->value);
        try {
            
            $client = new Client();

            $response = $client->post(env('ENDPOINT_URL')."/api/erp/create-stock-transfer-order", [
                'timeout' => 160, // Response timeout
                'connect_timeout' => 30, // Connection timeout
                'verify' => false,
                'headers' => [
                    'Content-Type' => 'application/json',
                    'Authorization' => $api_key->value,
                ],
                'body' => json_encode($params)
            ]);

            $unity_response = json_decode($response->getBody()->getContents(), TRUE);
            $httpCode = $response->getStatusCode();
          
            Log::info("CreateStockOrderThroughUnityEvent Response Code: ".$httpCode);
            Log::info("CreateStockOrderThroughUnityEvent Response : ". json_encode($unity_response));

            if($httpCode == '200') {

                if($unity_response['success'] == true) {
                    $process_status = 'Success';
                } else {
                    $message .= 'Unity Response | '.$unity_response['message'];
                }
                
            } else {
                $message .= 'Unity Response Code | '.$httpCode;
            }
        }
        catch(Exception $e){
            $seller_id = $event->seller_id;

            $message .= 'CreateStockOrderThroughUnity  API Error | ';
            $activity_id = activity()
            ->withProperties(['response' => json_encode($e->getMessage()) , 'dump' => $e->getTraceAsString()])
            ->log('Get Order Origin Process');

            $message .= $e->getMessage();
            Log::info($e->getTraceAsString());
            // Mail::raw($e->getMessage(), function ($m) use ($seller_id) {
            //     $m->to('<EMAIL>')->subject('Seller :: '.$seller_id.' - DYNAMICS CreateStockOrderThroughUnity : failiure');
            // });
                // Mail::raw($message.' | '.$e->getMessage(), function ($m)  use ($unity) {
                //     $m->to($unity)
                //         ->subject('Send Order Origin Process');
                // });
            
            Log::info('CreateStockOrderThroughUnity error '.$e->getMessage());
        }

        if($process_status == 'Failed') {
            Mail::raw('CreateStockOrderThroughUnity Process | '.$message, function ($m)  use ($seller_id) {
                $m->to('<EMAIL>')->subject('Seller :: '.$seller_id.' - DYNAMICS CreateStockOrderThroughUnity : Process Failed');
            });
        }
       
    }
}
