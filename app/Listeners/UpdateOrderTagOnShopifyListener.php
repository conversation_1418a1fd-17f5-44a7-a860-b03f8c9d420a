<?php

namespace App\Listeners;

use App\Events\UpdateOrderTagOnShopifyEvent;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;

use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use App\Models\FulfillmentOrder;
use App\Models\Setting;
use App\Models\Shipment;
use App\Traits\ExceptionTrait;
use Carbon\Carbon;
use App\Models\FulfillmentOrderGtechUpdates;
use GuzzleHttp\Client;
use Exception;

class UpdateOrderTagOnShopifyListener implements ShouldQueue
{
    public $queue = "updateOrderTagOnShopifyListener";
    use ExceptionTrait;
    use InteractsWithQueue;

    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     *
     * @param  SendShipmentDetailsEvent  $event
     * @return void
     */
    public function handle(UpdateOrderTagOnShopifyEvent $event)
    {
        sleep(2);
        Log::info("UpdateOrderTagOnShopifyEvent Event Started Order with  : ".$event->data['shopify_order_id']);

        $data = $event->data;
        Log::info($data);
        $params = array("id" => $data['shopify_order_id'] , "tags" => $data['order_tag']);
        $params = array("order" => $params);
                Log::info($data);
        $params = json_encode($params);

        Log::info('https://'.$data['url'].'/admin/api/2022-10/orders/'.$data['shopify_order_id'].'.json');
        $curl = curl_init();

        curl_setopt_array($curl, array(
        CURLOPT_URL => 'https://'.$data['url'].'/admin/api/2022-10/orders/'.$data['shopify_order_id'].'.json',
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_ENCODING => '',
        CURLOPT_MAXREDIRS => 10,
        CURLOPT_TIMEOUT => 0,
        CURLOPT_FOLLOWLOCATION => true,
        CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
        CURLOPT_CUSTOMREQUEST => 'PUT',
        CURLOPT_POSTFIELDS =>$params,
        CURLOPT_HTTPHEADER => array(
          'X-Shopify-Access-Token:  '.$data['access_token'],
          'Content-Type: application/json'
        ),
   
        ));

        $response = curl_exec($curl);

        curl_close($curl);
        Log::info("UpdateOrderTagOnShopifyEvent :: Response :: ");
        Log::info($response);
        Log::info("UpdateOrderTagOnShopifyEvent :: End :: ");

    }
}
