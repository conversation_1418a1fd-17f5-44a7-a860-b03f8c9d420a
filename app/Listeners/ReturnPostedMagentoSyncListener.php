<?php

namespace App\Listeners;

use App\Events\ReturnPostedMagentoSyncEvent;
use App\Helpers\OrderComment;
use App\Models\Shipment;
use App\Models\ShipmentCourierHistory;
use App\Traits\ExceptionTrait;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;

class ReturnPostedMagentoSyncListener implements ShouldQueue
{
    public $queue = "returnPostedSync";
    use ExceptionTrait;
    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     *
     * @param  ReturnPostedMagentoSyncEvent  $event
     * @return void
     */
    public function handle(ReturnPostedMagentoSyncEvent $event)
    {
        $order = $event->order;
        $unity = explode(',',env('UNITY_POC'));
        $others = explode(',',env('KHAADI_POC'));

        $return_to_store_statuses = [
            'Replacement - Delivered to Shipper',
            'Return - Delivered to Shipper',
            'Returned',
            'RETURNED',
            'Return to Shipper',
            'Direct Return to Vendor',
            'Return',
            'RETURN SUBMITTED',
            'RETURNED TO SHIPPER AFTER ARRIVED AT ORIGIN',
            'Returned to shipper'
        ];

        Log::info('Shipment Return status Listener | shipment_courier_history query starting');

        Log::info("ReturnStatusListener:".$order['OrderReference']);

        $shipment_history = ShipmentCourierHistory::where('orders.marketplace_reference_id',$order['OrderReference'])->join('shipments','shipment_courier_histories.shipment_id','shipments.id')->join('couriers','shipments.courier_id','couriers.id')->join('orders','shipments.order_id','orders.id')->join('seller_locations','shipments.seller_location_id','seller_locations.id')->selectRaw('shipment_courier_histories.*,shipments.tracking_number,shipments.seller_location_id,shipments.order_id,orders.entity_id,orders.marketplace_reference_id,couriers.name,shipments.courier_id,shipments.type,seller_locations.location_name')->whereIn('shipments.seller_id',['119'])->whereIn('shipment_courier_histories.status',$return_to_store_statuses)->orderBy('shipment_courier_histories.id','desc')->first();

        Log::info('Shipment Return status Listener | shipment_courier_history query ended');
        if(isset($shipment_history->tracking_number)){
            $ship = Shipment::where('tracking_number',$shipment_history->tracking_number)
                        ->where('seller_id',119)
                        ->where('order_id',$shipment_history->order_id)
                        ->first();

            if($ship->return_receive_at_store == 1){
                $process_status = 'existed';
            } else{
            
                // Log::info($shipment_history);
                $status = $shipment_history->status;
                $api_status = '';
                // Log::info($event->shipment);
                $rs_stat = 0;

                if($shipment_history->storefront_sync_at != null){

                    if($shipment_history->type == null){

                        $process_status = 'Failed';
                        $message = "Tracking status not found in KHAADI mapped status list for <br> Tracking number : <b>".$shipment_history->tracking_number."</b> <br>".$shipment_history->name." Status : <b>".$status."</b>";

                    
                        if (in_array($status, $return_to_store_statuses)) {
                        
                            $api_status = 'return_posted';

                        }
                

                        if ($api_status) {
                            
                            try{
                                Log::info('Shipment Return status Listener | magento comment api starting');
                                $curl = curl_init();
                                    $comment = "Tracking Update by <b>".$shipment_history->name."</b> for <br> Tracking number : <b>".$shipment_history->tracking_number."</b> <br> Hub : <b>".$shipment_history->location_name."</b> <br>
                                    Khaadi Status : <b>".$api_status."</b> <br> ".$shipment_history->name." Status : <b>".$status."</b> <br>"
                                    .$shipment_history->name." Status Time : <b>".$shipment_history->status_at."</b>";
                        
                                    $data = array("statusHistory" => array("comment" => $comment , "status" => $api_status) );
                                    $request_data = $data;

                                    $request_url = env('KHAADI_URL')."/rest/V1/orders/".$shipment_history->entity_id."/comments";
                                    curl_setopt_array($curl, array(
                                    CURLOPT_URL => $request_url,
                                    CURLOPT_RETURNTRANSFER => true,
                                    CURLOPT_ENCODING => "",
                                    CURLOPT_MAXREDIRS => 10,
                                    CURLOPT_TIMEOUT => 60,
                                    CURLOPT_FOLLOWLOCATION => true,
                                    CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                                    CURLOPT_CUSTOMREQUEST => "POST",
                                    CURLOPT_POSTFIELDS => json_encode($data),
                                    CURLOPT_HTTPHEADER => array(
                                        "Authorization: Bearer ".env('KHAADI_TOKEN'),
                                        "Content-Type: application/json"
                                    ),
                                    ));
                        
                                    $response = curl_exec($curl);
                                    $response_data = $response;
                        
                                    $httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
                                    Log::info('Shipment Return status Listener | magento comment api ended');
                                    if ($httpCode == '200' && $response == 'true') {
                                        Log::info('ReturnStatusListener: if : Khaadi Order Status Api Request Success');
                                        $process_status = 'Success';
                                        $message = $comment." <br>Comment API Response : <b>".$response."</b>  <br>Khaadi Status : <b>'.$api_status.'</b> ";
                                        Shipment::where('tracking_number',$shipment_history->tracking_number)
                                                ->where('seller_id',119)
                                                ->where('order_id',$shipment_history->order_id)
                                                ->update(['return_receive_at_store' => 1]);
                                    } elseif ($httpCode == '200' && json_encode($response) == '"\ntrue"') {
                                        Log::info('ReturnStatusListener: elseif : Khaadi Order Status Api Request Success');
                                        $process_status = 'Success';
                                        $message = $comment." <br>Comment API Response : <b>".json_encode($response)."</b>  <br>Khaadi Status : <b>'.$api_status.'</b> ";
                                        Shipment::where('tracking_number',$shipment_history->tracking_number)
                                                ->where('seller_id',119)
                                                ->where('order_id',$shipment_history->order_id)
                                                ->update(['return_receive_at_store' => 1]);
                                    } else {
                                        $message = 'Tracking status not updated at storefront | '.json_encode($response).' | '.$api_status.' | '.$shipment_history->marketplace_reference_id.' | '.$shipment_history->tracking_number;
                                        // Mail::raw($message, function ($m)  {
                                        //     $m->to(['<EMAIL>','<EMAIL>'])
                                        //         ->bcc('<EMAIL>')
                                        //         ->subject('Shipment Tracking Storefront Synced Process');
                                        // });

                                        if($others[0] != ""){
                                            // Mail::raw($message, function ($m)  use($others,$unity) {
                                            //     $m->to($others)
                                            //         ->bcc($unity)
                                            //         ->subject('Return Receive Storefront Synced Process');
                                            // });
                                        }
                                        else{
                                            Mail::raw($message, function ($m)  use($unity) {
                                                $m->to($unity)
                                                    ->subject('Return Receive Storefront Synced Process');
                                            });
                                        }


                                        $this->addException(    119,
                                                                config('enum.exception_types')['RETURNRECEIVESTOREFRONTSYNC'],
                                                                ( strlen($message) > 250 ? substr($message,0,240) . "..." : $message ),
                                                                'Order',
                                                                $shipment_history->order_id,
                                                                $shipment_history->marketplace_reference_id,
                                                                json_encode(['message' => $message, 'request_url' => $request_url, 'query_data' => $shipment_history]),
                                                                ( $request_data ? json_encode($request_data) : null),
                                                                ( $response_data ? json_encode($response_data) : null)
                                                            );
                                    }

                                    
                                    curl_close($curl);
                                    Log::info('Return Receive Api response '.json_encode($response));
                                    
                                } catch(\Exception $e){

                                    $message = 'Tracking status not updated at storefront | '.$shipment_history->marketplace_reference_id;
                                    $activity_id = activity()
                                    ->withProperties(['response' => json_encode($e->getMessage()) , 'dump' => $e->getTraceAsString()])
                                    ->log('Khaadi Order Status Api');

                                    if($others[0] != ""){
                                        Mail::raw($message.' | '.$e->getMessage(), function ($m)  use($others,$unity) {
                                            $m->to($others)
                                                ->bcc($unity)
                                                ->subject('Return Receive Storefront Synced Process');
                                        });
                                    }
                                    else{
                                        Mail::raw($message.' | '.$e->getMessage(), function ($m)  use($unity) {
                                            $m->to($unity)
                                                ->subject('Return Receive Storefront Synced Process');
                                        });
                                    }

                                    // Mail::raw($message.' | '.$e->getMessage(), function ($m)  {
                                    //     $m->to(['<EMAIL>','<EMAIL>'])
                                    //         ->bcc('<EMAIL>')
                                    //         ->subject('Return Receive Storefront Synced Process');
                                    // });
                                    
                                    Log::info('ReturnStatusListener : catch : Shipment Status Api error '.$e->getMessage());
                                }
                            
                        } else {
                            Mail::raw($message, function ($m)  {
                                $m->to('<EMAIL>')->subject('Return Receive Storefront Synced Process');
                            });
                        }

                        $status = $process_status;
                        $order_id = $shipment_history->order_id;
                        $key = 'Return Receive Storefront Synced Process';
                        Log::info($message);

                        OrderComment::add(compact('order_id','message','key','status'));
                    }
                    Log::info('return sync magento end | '.$order['OrderReference']);
                }
            }
        }
        Log::info('Return Posted Sync Listener Ended');
    }
}
