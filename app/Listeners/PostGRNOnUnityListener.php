<?php

namespace App\Listeners;

use App\Events\PostGRNOnUnityEvent;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;

use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use App\Models\FulfillmentOrder;
use App\Models\Setting;
use App\Models\Shipment;
use App\Traits\ExceptionTrait;
use Carbon\Carbon;
use App\Models\FulfillmentOrderGtechUpdates;
use GuzzleHttp\Client;
use Exception;

class PostGRNOnUnityListener implements ShouldQueue
{
    public $queue = "postGRNOnUnityListener";
    use ExceptionTrait;
    use InteractsWithQueue;

    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     *
     * @param  SendShipmentDetailsEvent  $event
     * @return void
     */
    public function handle(PostGRNOnUnityEvent $event)
    {
        Log::info("PostGRNOnUnityEvent Event Started Order with order : ".$event->stock_order['stock_order_id']);

        $process_status = 'Failed';
        $message = '';
        $unity = explode(',',env('UNITY_POC'));
        $others = explode(',',env('KHAADI_POC'));
        $line_items = [];
        foreach($event->stock_order['line_items'] as $item){
            $temp_arr = [];
            $temp_arr['barcode'] = $item['Barcode'];
            $temp_arr['sku'] = $item['sku'];
            $temp_arr['qty'] = $item['Quantity'];
            $line_items[] = $temp_arr;
        }

        $params = ['stock_order_id' => $event->stock_order['stock_order_id'], 'line_items' => $line_items ];

        try {
            
            $client = new Client();

            $response = $client->post(env('ENDPOINT_URL')."/api/erp/post-grn", [
                'timeout' => 160, // Response timeout
                'connect_timeout' => 30, // Connection timeout
                'verify' => false,
                'headers' => [
                    'Content-Type' => 'application/json',
                    'Authorization' => '9DkFiR4LxOhWuxPCIe6s85o8ebGGKL3O',
                ],
                'body' => json_encode($params)
            ]);

            $unity_response = json_decode($response->getBody()->getContents(), TRUE);
            $httpCode = $response->getStatusCode();
          
            Log::info("PostGRNOnUnityEvent Response Code: ".$httpCode);
            Log::info("PostGRNOnUnityEvent Response : ". json_encode($unity_response));

            if($httpCode == '200') {

                if($unity_response['success'] == true) {
                    $process_status = 'Success';
                } else {
                    Log::info("PostGRNOnUnityEvent Failed Response : ");
                    Log::info($unity_response);
                    $message .= 'Unity Response | '.$unity_response['message'];
                }
                
            } else {
                $message .= 'Unity Response Code | '.$httpCode;
            }
        }
        catch(Exception $e){
            $message .= 'PostGRNOnUnityEvent  API Error';
            $activity_id = activity()
            ->withProperties(['response' => json_encode($e->getMessage()) , 'dump' => $e->getTraceAsString()])
            ->log('Get Order Origin Process');

                // Mail::raw($message.' | '.$e->getMessage(), function ($m)  use ($unity) {
                //     $m->to($unity)
                //         ->subject('Send Order Origin Process');
                // });
            
            Log::info('PostGRNOnUnityEvent error '.$e->getMessage());
        }

        if($process_status == 'Failed') {
            // Mail::raw('CreateStockOrderThroughUnity Process | '.$message.' | Order ID: '.$event->order, function ($m)  use ($unity) {
            //     $m->to($unity)
            //         ->subject('CreateStockOrderThroughUnity Process');
            // });
        }
       
    }
}
