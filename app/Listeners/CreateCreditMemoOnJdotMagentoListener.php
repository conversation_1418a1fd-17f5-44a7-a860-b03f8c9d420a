<?php

namespace App\Listeners;

use App\Events\CreateCreditMemoOnJdotMagentoEvent;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;

use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use App\Models\FulfillmentOrder;
use App\Models\Setting;
use App\Models\Shipment;
use App\Traits\ExceptionTrait;
use Carbon\Carbon;
use App\Models\FulfillmentOrderGtechUpdates;
use GuzzleHttp\Client;
use Exception;
use App\Helpers\OrderComment;
use App\Models\ShipmentHistory;


class CreateCreditMemoOnJdotMagentoListener implements ShouldQueue
{
    public $queue = "createCreditMemoOnJdotMagentoListener";
    use ExceptionTrait;
    use InteractsWithQueue;

    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     *
     * @param  SendShipmentDetailsEvent  $event
     * @return void
     */
    public function handle(CreateCreditMemoOnJdotMagentoEvent $event)
    {

        Log::info("CreateCreditMemoOnJdotMagentoEvent Event Started Order with  : ".$event->data['magento_order_id']);

        $data = $event->data;
        Log::info($data);

        $item_arr = [];
        foreach($data['order_items'] as $item){
          $temp_arr = [];
          $temp_arr['order_item_id'] = $item->marketplace_id;
          $temp_arr['qty'] = $item->quantity;
          $item_arr[] = $temp_arr;
        }
        
        $params = array("items" => $item_arr);

        Log::info($params);
        $params = json_encode($params);

        Log::info($data['url'].'/rest/V1/order/'.$data['magento_order_id'].'/refund');


        $curl = curl_init();

        curl_setopt_array($curl, array(
          CURLOPT_URL => $data['url'].'/rest/V1/order/'.$data['magento_order_id'].'/refund',
          CURLOPT_RETURNTRANSFER => true,
          CURLOPT_ENCODING => '',
          CURLOPT_MAXREDIRS => 10,
          CURLOPT_TIMEOUT => 0,
          CURLOPT_FOLLOWLOCATION => true,
          CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
          CURLOPT_CUSTOMREQUEST => 'POST',
          CURLOPT_POSTFIELDS =>$params,
          CURLOPT_HTTPHEADER => array(
            'Content-Type: application/json',
            'Authorization: Bearer '.$data['magento_token']
          ),
        ));
        
        $response = curl_exec($curl);
        $httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
        curl_close($curl);

        if ($httpCode == '200' && $response == 'true') {
          Log::info('CreateCreditMemoOnJdotMagentoEvent ::  Success');
            $message = $data['comment'];
            $key = 'Credit Memo Process';
            $status = "Success";
            $order_id = $data['unity_order_id'];

            OrderComment::add(compact('order_id','message','key','status'));            

        } else{
          Log::info("CreateCreditMemoOnJdotMagentoEvent :: Response :: ");
          Log::info($response);
        }


        Log::info("CreateCreditMemoOnJdotMagentoEvent :: End :: ");

    }
}
