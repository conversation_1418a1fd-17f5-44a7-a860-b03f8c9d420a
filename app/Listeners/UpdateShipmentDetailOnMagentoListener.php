<?php

namespace App\Listeners;

use App\Events\UpdateShipmentDetailsOnMagnetoForJdotEvent;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;

use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use App\Models\FulfillmentOrder;
use App\Models\Setting;
use App\Models\Shipment;
use App\Traits\ExceptionTrait;
use Carbon\Carbon;
use App\Models\FulfillmentOrderGtechUpdates;
use GuzzleHttp\Client;
use Exception;
use App\Helpers\OrderComment;
use App\Models\ShipmentHistory;


class UpdateShipmentDetailOnMagentoListener implements ShouldQueue
{
    public $queue = "updateShipmentDetailOnMagentoListener";
    use ExceptionTrait;
    use InteractsWithQueue;

    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     *
     * @param  SendShipmentDetailsEvent  $event
     * @return void
     */
    public function handle(UpdateShipmentDetailsOnMagnetoForJdotEvent $event)
    {
        $courier_mapping = array(
                            4 => "MnP",
                            5 => "LCS",
                            7 => "Call_Courier",
                            8 => "Trax",
                            10 => "BlueEx",
                            11 => "TPL_Rider",
                            13 => "TCS",
                            15 => "Swyft");


        Log::info("UpdateShipmentDetailsOnMagnetoForJdotEvent Event Started Order with  : ".$event->data['magento_order_id']);

        $data = $event->data;
        Log::info($data);

        $params = array("track_number" =>   $data['tracking_number'] , "title" => $courier_mapping[$data['courier_id']] , "carrier_code" => $courier_mapping[$data['courier_id']]  );
        $params = array("tracks" => $params);

        Log::info($params);
        $params = json_encode($params);

        Log::info($data['url'].'/rest/V1/orders/'.$data['magento_order_id'].'/ship');

        $curl = curl_init();
        curl_setopt_array($curl, array(
          CURLOPT_URL => $data['url'].'rest/default/V1/orders/'.$data['magento_order_id'].'/ship',
          CURLOPT_RETURNTRANSFER => true,
          CURLOPT_ENCODING => '',
          CURLOPT_MAXREDIRS => 10,
          CURLOPT_TIMEOUT => 0,
          CURLOPT_FOLLOWLOCATION => true,
          CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
          CURLOPT_CUSTOMREQUEST => 'POST',
          CURLOPT_POSTFIELDS =>$params,
          CURLOPT_HTTPHEADER => array(
            'Content-Type: application/json',
            'Authorization: Bearer '.$data['magento_token']
          ),
        ));
        
        $response = curl_exec($curl);
        
        curl_close($curl);

        

        $httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);

        if ($httpCode == '200' && $response == 'true') {
          Log::info('UpdateShipmentDetailsOnMagnetoForJdotEvent :: Jdot Magento Order Status Api Request Success');
            $message = $data['comment'];
            $key = 'Shipment Details Sync Process';
            $status = 'Success';
            $message = "Shipment details are sent to Jdot Magento with tracking number : <b>".$data['tracking_number']."</b>";
            $shipment = Shipment::find($data['shipment_id']);
            $shipment->storefront_sync = 1;
            $shipment->save();


            OrderComment::add(compact('order_id','message','key','status'));

            $curl = curl_init();
            
            Log::info("UpdateInvoiceOnMagnetoForJdotEvent << started >> ");

            Log::info($data['url'].'rest/V1/order/'.$data['magento_order_id'].'/invoice');

            curl_setopt_array($curl, array(
              CURLOPT_URL => $data['url'].'rest/V1/order/'.$data['magento_order_id'].'/invoice',
              CURLOPT_RETURNTRANSFER => true,
              CURLOPT_ENCODING => '',
              CURLOPT_MAXREDIRS => 10,
              CURLOPT_TIMEOUT => 0,
              CURLOPT_FOLLOWLOCATION => true,
              CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
              CURLOPT_CUSTOMREQUEST => 'POST',
              CURLOPT_HTTPHEADER => array(
                'Content-Type: application/json',
                'Authorization: Bearer '.$data['magento_token']
              ),
            ));

            $response = curl_exec($curl);

            curl_close($curl);
            Log::info("UpdateInvoiceOnMagnetoForJdotEvent << response >> ");
            Log::info($response);

            

        } else{
          Log::info("UpdateShipmentDetailsOnMagnetoForJdotEvent :: Response :: ");
          Log::info($response);
        }


        Log::info("UpdateShipmentDetailsOnMagnetoForJdotEvent :: End :: ");

    }
}
