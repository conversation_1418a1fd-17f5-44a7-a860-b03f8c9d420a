<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class DumpDynamicsSalesInSql extends Model
{
    protected $table = 'ur_dynamics_sales';

    // Set the connection to the SQL Server
    protected $connection = 'odbc';

    protected $guarded = [];

    public static function createOrUpdate($data){
        
        $attributes = [
            'order_type' => $data['order_type'],
            'seller_id' => $data['seller_id'],
            'fulfillment_id' => $data['fulfillment_id'], 
            'shipment_id' => $data['shipment_id'], 
            'dynamics_order_id' => $data['dynamics_order_id'], 
            'order_id' => $data['order_id'], 
            'barcode' => $data['barcode'], 
            'sku' => $data['sku'], 

        ];

        $values = [
            'payment_method' => $data['payment_method'],
            'location_id' => $data['location_id'],
            'shipping_charges' => $data['shipping_charges'],
            'customer_name' => $data['customer_name'],
            'customer_address' => $data['customer_address'],
            'city' => $data['city'],
            'country' => $data['country'],
            'customer_number' => $data['customer_number'],
            'customer_email' => $data['customer_email'],
            'invoice_account' => $data['invoice_account'],
            'logistics_partner' => $data['logistics_partner'],
            'consignment_number' => $data['consignment_number'],
            'comments' => $data['comments'],
            'barcode' => $data['barcode'],
            'sku' => $data['sku'],
            'quantity' => $data['quantity'],
            'unit_price' => $data['unit_price'],
            'net_amount' => $data['net_amount'],
            'charges_code' => $data['charges_code'],
            'charges_amount' => $data['charges_amount'],
            'response_from_api' => $data['response_from_api']
        ];

        // Update if the record exists'], otherwise create a new one
        self::updateOrCreate($attributes, $values);
    

    
 
    }


}
