<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class DumpErpSale extends Model
{
    // Table name (optional, if it matches the class name)
    protected $table = 'dump_erp_sales';
    protected $connection = 'mysql2';

    // Mass-assignable attributes
    protected $fillable = [
        'seller_id',
        'order_id',
        'fulfillment_id',
        'shipment_id',
        'type',
        'order_reference_id',
        'endpoint',
        'payload',
        'last_response_received',
        'response_code',
        'sync_completed',
        'sync_attempted',
        'last_sync_attempted_at',
        'email_alert_sent',
    ];

    // Cast attributes for easy manipulation
    protected $casts = [
        'payload' => 'array',
        'last_response_received' => 'array',
        'sync_completed' => 'boolean',
        'sync_attempted' => 'boolean',
        'email_alert_sent' => 'boolean',
        'last_sync_attempted_at' => 'datetime',
    ];

    /**
     * Scope to get unsynced sales
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeUnsynced($query)
    {
        return $query->where('sync_completed', false);
    }

    /**
     * Scope to get sales that need an email alert
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeAlertPending($query)
    {
        return $query->where('sync_completed', false)
                     ->where('email_alert_sent', false);
    }

    /**
     * Mark a record as sync attempted
     *
     * @return bool
     */
    public function markSyncAttempted(): bool
    {
        $this->sync_attempted = true;
        $this->last_sync_attempted_at = now();
        return $this->save();
    }

    /**
     * Mark a record as synced
     *
     * @param string|null $response
     * @param int|null $responseCode
     * @return bool
     */
    public function markAsSynced(?string $response, ?int $responseCode): bool
    {
        $this->sync_completed = true;
        $this->last_response_received = $response;
        $this->response_code = $responseCode;
        return $this->save();
    }

    /**
     * Mark a record as email alert sent
     *
     * @return bool
     */
    public function markAlertSent(): bool
    {
        $this->email_alert_sent = true;
        return $this->save();
    }

    public function markSyncFailed(?string $response, ?int $responseCode): bool
    {
        $this->sync_attempted = true;
        $this->last_sync_attempted_at = now();
        $this->response_code = $responseCode;
        $this->last_response_received = $response;
        return $this->save();
    }

}
