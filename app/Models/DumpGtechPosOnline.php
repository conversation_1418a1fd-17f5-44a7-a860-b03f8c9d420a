<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class DumpGtechPosOnline extends Model
{
    protected $connection = 'mysql2';
    protected $table = 'dump_gtech_pos_online';

    public static function create($data){
        
        // $fbr_sales = self::where('order_id',$data['order_id'])->where('shipment_id',$data['shipment_id'])->where('fulfillment_id',$data['fulfillment_id'])->where('sales_type',$data['sales_type'])->get();
       
        // if(count($fbr_sales) == 0){
            $dump_gtech_pos_online = new self;
            $dump_gtech_pos_online->sales_type = $data['sales_type'];
            $dump_gtech_pos_online->seller_id = $data['seller_id'];
            $dump_gtech_pos_online->fulfillment_id = $data['fulfillment_id'];
            $dump_gtech_pos_online->order_id = $data['order_id'];
            $dump_gtech_pos_online->shipment_id = $data['shipment_id'];
            $dump_gtech_pos_online->bill_no = $data['bill_no'];
            $dump_gtech_pos_online->bill_date = $data['bill_date'];
            $dump_gtech_pos_online->sales_man = $data['sales_man'];
            $dump_gtech_pos_online->terminal = $data['terminal'];
            $dump_gtech_pos_online->card_no = $data['card_no'];
            
            $dump_gtech_pos_online->on_account = $data['on_account'];
            $dump_gtech_pos_online->description = $data['description'];
            $dump_gtech_pos_online->cash_amount = $data['cash_amount'];
            $dump_gtech_pos_online->net_receavable_amount = $data['net_receavable_amount'];
            $dump_gtech_pos_online->drcr_amount = $data['drcr_amount'];
            $dump_gtech_pos_online->on_account_amount = $data['on_account_amount'];
            $dump_gtech_pos_online->return_amount = $data['return_amount'];
            $dump_gtech_pos_online->balance_amount = $data['balance_amount'];
            $dump_gtech_pos_online->customer_name = $data['customer_name'];
            $dump_gtech_pos_online->contact_no = $data['contact_no'];
            $dump_gtech_pos_online->email = ( isset($data['email']) ? $data['email'] : "" );
            $dump_gtech_pos_online->address = str_replace(',', '', $data['address']);
            $dump_gtech_pos_online->location = $data['location'];
            $dump_gtech_pos_online->discount_type = $data['discount_type'];
            $dump_gtech_pos_online->slip_time = $data['slip_time'];
            $dump_gtech_pos_online->web_order_no = $data['web_order_no'];
            $dump_gtech_pos_online->freight_amount = $data['freight_amount'];
            $dump_gtech_pos_online->fbr_invoice_no = $data['fbr_invoice_no'];
            $dump_gtech_pos_online->user_id = $data['user_id'];
            $dump_gtech_pos_online->buyer_cnic = $data['buyer_cnic'];
            $dump_gtech_pos_online->return_no = $data['return_no'];
            $dump_gtech_pos_online->add_other_charges = $data['add_other_charges'];
            $dump_gtech_pos_online->sales_type_gtech = $data['sales_type_gtech'];
            $dump_gtech_pos_online->cn_number = $data['cn_number'];
            $dump_gtech_pos_online->credit_memo_no = $data['credit_memo_no'];
            $dump_gtech_pos_online->credit_memo_amount = $data['credit_memo_amount'];
            $dump_gtech_pos_online->credit_memo_order_no = $data['credit_memo_order_no'];
            $dump_gtech_pos_online->service_fee = $data['service_fee'];
            $dump_gtech_pos_online->barcode = ( isset($data['barcode']) ? $data['barcode'] : "" );;
            $dump_gtech_pos_online->item_name = $data['item_name'];
            $dump_gtech_pos_online->qty = $data['qty'];
            $dump_gtech_pos_online->act_price = ( isset($data['act_price']) && $data['act_price'] != NULL  ? $data['act_price'] : 0 );;
            $dump_gtech_pos_online->gstp = $data['gstp'];
            $dump_gtech_pos_online->selling_price = ( isset($data['selling_price']) && $data['selling_price'] != NULL  ? $data['selling_price'] : 0 );
            $dump_gtech_pos_online->discount_percent = $data['discount_percent'];
            $dump_gtech_pos_online->barcode_discount = $data['barcode_discount'];
            $dump_gtech_pos_online->retail = $data['retail'];
            $dump_gtech_pos_online->gst = $data['gst'];
            $dump_gtech_pos_online->gross_sales = $data['gross_sales'];
            $dump_gtech_pos_online->discount_amount = $data['discount_amount'];
            $dump_gtech_pos_online->net_sale = $data['net_sale'];
            $dump_gtech_pos_online->stock = $data['stock'];
            $dump_gtech_pos_online->pct_code = ( isset($data['pct_code']) ? $data['pct_code'] : "" );
            $dump_gtech_pos_online->fbr_desc =  ( isset($data['fbr_desc']) ? $data['fbr_desc'] : "" );

            $dump_gtech_pos_online->save();
        // }
        
        // return $dump_gtech_pos_online;

    }
    
    public static function organizeAndCreateRecord($data){

        $order = $data['order'];
        $shipment = $data['shipment'];
        $fulfillment_order = $data['fulfillment_order'];
        $sales_type = $data['sales_type'];
        $data = $data['dump_gtech_pos_onlines'];
    
        foreach($data['items_detail'] as $item){
            $organized_data = [];
            $organized_data['sales_type'] = $sales_type;
            $organized_data['seller_id'] = $order->seller_id;
            $organized_data['fulfillment_id'] =  $fulfillment_order->id;
            $organized_data['order_id'] = $order->id;
            $organized_data['shipment_id'] =  $shipment->id;
            $organized_data['bill_no'] = $data['BillNo'];
            $organized_data['bill_date'] = $data['BillDate'];
            $organized_data['sales_man'] = $data['SalesMan'];
            $organized_data['terminal'] = $data['Terminal'];
            $organized_data['card_no'] = $data['CardNo'];
            $organized_data['on_account'] = $data['OnAccount'];
            $organized_data['description'] = $data['Description'];
            $organized_data['cash_amount'] = $data['CashAmount'];
            $organized_data['net_receavable_amount'] = $data['NetReceivableAmount'];
            $organized_data['drcr_amount'] = $data['DRCRAmount'];
            $organized_data['on_account_amount'] =  $data['OnAccountAmount'];
            $organized_data['return_amount'] =  $data['ReturnAmount'];
            $organized_data['balance_amount'] =   $data['BalanceAmount'];
            $organized_data['customer_name'] =  $data['Customer'];
            $organized_data['contact_no'] =   $data['ContactNo'];
            $organized_data['email'] =   $data['Email'];
            $organized_data['address'] =   $data['Address'];
            $organized_data['location'] =   $data['Location'];
            $organized_data['discount_type'] =   $data['DiscountType'];
            $organized_data['slip_time'] = $data['SlipTime'];
            $organized_data['web_order_no'] =   $data['WebOrderNo'];
            $organized_data['freight_amount'] =   $data['FreightAmount'];
            $organized_data['fbr_invoice_no'] =   $data['FBRINVNo'];
            $organized_data['user_id'] = $data['UserId'];
            $organized_data['buyer_cnic'] = $data['BuyerCNic'];
            $organized_data['return_no'] = $data['ReturnNo'];
            $organized_data['add_other_charges'] = $data['AddOtherCharges'];
            $organized_data['sales_type_gtech'] = $data['SalesType'];
            $organized_data['cn_number'] = $data['CNNumber'];
            $organized_data['credit_memo_no'] = $data['CreditMemono'];
            $organized_data['credit_memo_amount'] = $data['CreditmemoAmount'];
            $organized_data['credit_memo_order_no'] =  $data['CreditMemoOrderNo'];
            $organized_data['service_fee'] =  $data['ServiceFee'];            
            $organized_data['barcode'] =   $item['BarCode'];
            $organized_data['item_name'] =  $item['ItemName'];
            $organized_data['qty'] =   $item['Qty'];
            $organized_data['act_price'] =   $item['ActPrice'];
            $organized_data['gstp'] =   $item['GSTP'];
            $organized_data['selling_price'] =   $item['SellingPrice'];
            $organized_data['discount_percent'] =   $item['DiscPer'];
            $organized_data['barcode_discount'] =   $item['BarcodeDiscount'];
            $organized_data['retail'] =   $item['Retail'];
            $organized_data['gst'] =   $item['GST'];
            $organized_data['gross_sales'] =   $item['GrossSales'];
            $organized_data['discount_amount'] =   $item['DiscAmount'];
            $organized_data['net_sale'] =   $item['NetSale'];
            $organized_data['stock'] =   $item['Stock'];
            $organized_data['pct_code'] =   $item['PctCode'];
            $organized_data['fbr_desc'] =   $item['FBRDesc'];
            

            self::create($organized_data);
            // $final_organized_data[] = $organized_data;
        }
        // dd($final_organized_data);
    }
    
}