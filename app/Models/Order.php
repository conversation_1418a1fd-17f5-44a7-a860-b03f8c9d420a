<?php

/**
 * Created by Reliese Model.
 * Date: Sat, 11 Mar 2017 08:18:29 +0000.
 */

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Order extends Model
{
	protected $connection = 'mysql2';
	
	public $timestamps = false;

	protected $casts = [
		'id' => 'int',
		'marketplace_id' => 'int',
		'marketplace_reference_id' => 'string',
		'seller_location_id' => 'int',
		'cod_payment' => 'bool',
		'users_id' => 'int'
	];

	protected $dates = [
		'created_date'
	];

	protected $fillable = [
		'category',
		'marketplace_id',
		'marketplace_reference_id',
		'created_date',
		'customer_name',
		'destination_city',
		'seller_location_id',
		'shipping_address',
		'cod_payment',
		'status',
		'users_id'
	];

	public function items()
	{
		return $this->hasMany('App\Models\OrderItem');
	}
	public function shipments()
	{
		return $this->hasMany('App\Models\Shipment');
	}
	public function tag()
	{
		return $this->belongsToMany('App\Models\Tag', 'order_tags');
	}

	public function marketplace()
	{
		return $this->belongsTo('App\Models\Marketplace');
	}

	public function seller()
	{
		return $this->belongsTo('App\Models\Seller');
	}

	public function location()
	{
		return $this->belongsTo('App\Models\SellerLocation', 'seller_location_id');
	}
	public function source()
	{
		return $this->belongsTo('App\Models\Source');
	}
	public function city_exists()
	{
		return $this->belongsTo('App\Models\Courier\City', 'destination_city', 'name')->select('id','name');
	}

}
