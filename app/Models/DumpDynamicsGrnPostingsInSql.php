<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class DumpDynamicsGrnPostingsInSql extends Model
{
    protected $table = 'ur_dynamics_grn_postings';

    // Set the connection to the SQL Server
    protected $connection = 'odbc';

    protected $guarded = [];

    public static function createOrUpdate($data){
    
        $attributes = [
            'seller_id' => $data['seller_id'],
            'stock_order_id' => $data['stock_order_id'],
            'stock_reference_id' => $data['stock_reference_id'], 
            'barcode' => $data['barcode'], 
            'sku' => $data['sku'],
        ];

        $values = [
            'quantity' => $data['quantity'],
            'response_from_api' => $data['response_from_api'],
            'is_synced_dynamics' => $data['is_synced_dynamics'],
        ];

        // Update if the record exists'], otherwise create a new one
        self::updateOrCreate($attributes, $values);

 
    }


}
