<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class ReturnReceive extends Model
{
    protected $connection = 'mysql2';

    public function items()
    {
        return $this->hasMany(ReturnReceiveItem::class);
    }

    public function shipment()
    {
        return $this->belongsTo(Shipment::class);
    }

    public function order()
    {
        return $this->belongsTo(Order::class);
    }

    public function courier()
    {
        return $this->belongsTo(Courier::class);
    }

    public function fulfillmentOrder()
    {
        return $this->belongsTo(FulfillmentOrder::class);
    }

    public function stockOrder()
    {
        return $this->belongsTo(StockOrder::class);
    }

    public function sellerLocation()
    {
        return $this->belongsTo(SellerLocation::class);
    }

}
