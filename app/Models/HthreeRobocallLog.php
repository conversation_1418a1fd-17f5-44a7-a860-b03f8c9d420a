<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Log;

class HthreeRobocallLog extends Model
{
    protected $connection = 'mysql2';

    protected $table = 'hthree_robocall_logs';

    public static function addInput(array $data)
    {
        ///// Creating Order Comment in unity //////

        Log::info('Started | sending input to unity | '.$data['order_id']);

        $curl = curl_init();

        curl_setopt_array($curl, array(
            CURLOPT_URL => env('ENDPOINT_URL').'/api/robocall/hthree',
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => "",
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_CONNECTTIMEOUT => 10,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => "POST",
            CURLOPT_POSTFIELDS => json_encode($data),
            CURLOPT_HTTPHEADER => array(
            "Content-Type:application/json"
                                )
        ));

        $response = curl_exec($curl);
        $response = json_decode($response, true);
        $httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);

        Log::info($data['order_id']." | Http Code | ".$httpCode.' | '.$data['dtmf']);
        Log::info($data['order_id']." | Response | ".json_encode($response).' | '.$data['dtmf']);

        if ($httpCode == '200' && $response['error'] == 0) {
            Log::info('SUCCESS | Sending Order Comment in unity | '.$data['order_id'].' | '.$data['dtmf']);
        } else {
            Log::info('FAILED | Sending Order Comment in unity | '.$data['order_id'].' | '.$data['dtmf']);
        }

        curl_close($curl);
    }
    public static function addInputForMerchant(array $data)
    {
        ///// Creating Order Comment in unity //////

        Log::info('Started | sending input to unity hthree merchant | '.$data['order_id']);

        $curl = curl_init();

        curl_setopt_array($curl, array(
            CURLOPT_URL => env('ENDPOINT_URL').'/api/robocall/hthree-merchant',
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => "",
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_CONNECTTIMEOUT => 10,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => "POST",
            CURLOPT_POSTFIELDS => json_encode($data),
            CURLOPT_HTTPHEADER => array(
            "Content-Type:application/json"
                                )
        ));

        $response = curl_exec($curl);
        $response = json_decode($response, true);
        $httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);

        Log::info($data['order_id']." | Http Code | ".$httpCode.' | '.$data['dtmf']);
        Log::info($data['order_id']." | Response | ".json_encode($response).' | '.$data['dtmf']);

        if ($httpCode == '200' && $response['error'] == 0) {
            Log::info('SUCCESS | Sending Order Comment in unity | '.$data['order_id'].' | '.$data['dtmf']);
        } else {
            Log::info('FAILED | Sending Order Comment in unity | '.$data['order_id'].' | '.$data['dtmf']);
        }

        curl_close($curl);
    }
}
