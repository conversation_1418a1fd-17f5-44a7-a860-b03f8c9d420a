<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class FulfillmentOrder extends Model
{
    protected $connection = 'mysql2';

    protected $table = 'fulfillment_orders';

    public function items()
	{
		return $this->hasMany('App\Models\FulfillmentOrderItem');
	}

    public function location()
	{
		return $this->belongsTo('App\Models\SellerLocation','seller_location_id');
	}
    
}
