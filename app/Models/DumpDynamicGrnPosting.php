<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class DumpDynamicGrnPosting extends Model
{
    /**
     * The database table used by the model.
     *
     * @var string
     */
    protected $table = 'dump_dynamics_grn_posting';
    protected $connection = 'mysql2';

    public static function create($data){

        $dump_dynamics_grn_posting = new self;
        $dump_dynamics_grn_posting->stock_order_id = $data['stock_order_id'];
        $dump_dynamics_grn_posting->seller_id = $data['seller_id'];
        $dump_dynamics_grn_posting->stock_reference_id = $data['stock_reference_id'];
        $dump_dynamics_grn_posting->origin_location = $data['origin_location'];
        $dump_dynamics_grn_posting->destination_location = $data['destination_location'];
        $dump_dynamics_grn_posting->barcode = $data['barcode'];
        $dump_dynamics_grn_posting->sku = $data['sku'];
        $dump_dynamics_grn_posting->quantity = $data['quantity'];
        $dump_dynamics_grn_posting->save();

    }
    
    public static function organizeAndCreateRecord($data){
    
        foreach($data['items'] as $item){
            $organized_data = [];

            $organized_data['stock_order_id'] = $data['stock_order_id'];
            $organized_data['seller_id'] = $data['seller_id'];
            $organized_data['stock_reference_id'] =  $data['stock_reference_id'];
            $organized_data['origin_location'] =  $data['origin_location'];
            $organized_data['destination_location'] =  $data['destination_location'];
            $organized_data['barcode'] = $item['Barcode'];
            $organized_data['sku'] =  $item['sku'];
            $organized_data['quantity'] = $item['Quantity'];           

            self::create($organized_data);
            // $final_organized_data[] = $organized_data;
        }
        // dd($final_organized_data);
    }


}
