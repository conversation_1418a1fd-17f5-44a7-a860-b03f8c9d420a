<?php

namespace App\Models;

use Illuminate\Http\Request;
use Illuminate\Support\Str;
use Cookie;
use GuzzleHttp\Exception\RequestException;
use App\Traits\ExceptionTrait;
use Exception;
use GuzzleHttp\Client;
use Illuminate\Support\Facades\Log;

class ShopifyApp
{
    private $api_version = '2023-10';
 
    use ExceptionTrait;
       

        private function callFunctionByName($name, $parameters)
        {
            try {

                return $this->$name($parameters);

            } catch(RequestException $e) {

                if (method_exists($e, 'getResponse')) {

                    if ($e->getResponse()) {
                    
                        $error_response = json_decode(((string) $e->getResponse()->getBody()), true);

                        if ( isset($error_response['error']['message']) ) {
                            $error = $error_response['error']['message'];
                        } elseif ( isset($error_response['message']) ) {
                            $error = json_encode($error_response['message']);
                        } elseif ( isset($error_response['error']) ) {
                            $error = json_encode($error_response['error']);
                        } elseif ( isset($error_response['errors']) ) {
                            $error = json_encode($error_response['errors']);
                        } else {
                            $error = $e->getMessage();
                        }

                    } else {
                        $error = 'Get response message is null | '.$e->getMessage();
                    }

                } else {
                    $error = $e->getMessage();
                }

                if ($e->getCode() == '429') {
                    sleep(3);
                    // Log::critical($e->getResponse()->getHeaders());
                    return $this->callFunctionByName($name, $parameters);
                
                } else {
                    throw new Exception($error);
                }
            }
        }



        public function getCompareAtPrice($marketplace_product_id, $marketplace_variant_id, $seller_id)
        {
            sleep(3);
            $store = Setting::where('seller_id', $seller_id)->where('key','WC')->first();
            $accessToken = Setting::where('seller_id', $seller_id)->where('key','ShopifyToken')->first();

            if(!isset($accessToken)) {
                return ['error' => 1, 'message' => 'Shopify access token not found'];
            }

            try {

                $client = new Client();
                $url = 'https://'.$store->value.'/admin/api/'.$this->api_version.'/graphql.json';
                $headers = ['X-Shopify-Access-Token' => $accessToken->value, 'Content-Type' => 'application/json'];
                $items[] = "gid://shopify/Product/".$marketplace_product_id;

                $body = [
                    "query" => 'query getProductsByIds($ids: [ID!]!) { nodes(ids: $ids) { ... on Product { id title description handle productType status totalInventory vendor tags metafields(first: 250) { nodes { id key value } } variants(first: 250) { edges { node { id title displayName barcode price sku compareAtPrice inventoryQuantity createdAt updatedAt inventoryItem { id locationsCount { count } harmonizedSystemCode createdAt updatedAt } } } } category { name fullName } createdAt updatedAt } } }',
                    'variables' => [
                        "ids" => $items
                    ]
                ];

                $result = $this->callFunctionByName('getProducts', compact('client', 'headers', 'url', 'body') );

                foreach ($result['data']['nodes'] as $key => $products) {

                    foreach ($products['variants']['edges'] as $variant) {

                        $variant = $variant['node'];
                        $variant['id'] = Str::after($variant['id'], 'gid://shopify/ProductVariant/');

                        if ($variant['id'] == $marketplace_variant_id && $variant['compareAtPrice']) {
                            return ['error' => 0, 'compare_at_price' => $variant['compareAtPrice']];
                        }
                    }
                }

                return ['error' => 1, 'message' => "The product details were not received as expected"];

            } catch (Exception $e) {
                return ['error' => 1, 'message' => 'Exception | '.$e->getMessage(), 'trace' => $e->getTraceAsString()];
            }
        }

        private function getProductDetails($parameters)
        {
            $client = $parameters['client'];
            $headers = $parameters['headers'];
            $url = $parameters['url'];
            $store = $parameters['store'];

            $response = $client->request('GET', str_replace('{store}', $store->value, $url) , [ 'headers' => $headers ] );
            return json_decode($response->getBody(), true);
        }

        public function getProductCollections($marketplace_product_id, $seller_id)
        {
            sleep(3);
            $data = [];
            $store = Setting::where('seller_id', $seller_id)->where('key','WC')->first();
            $accessToken = Setting::where('seller_id', $seller_id)->where('key','ShopifyToken')->first();

            if(!isset($accessToken)) {
                return ['error' => 1, 'message' => 'Shopify access token not found'];
            }
            
            $url='https://{store}/admin/api/'.$this->api_version.'/smart_collections.json?product_id='.$marketplace_product_id;

            try {

                $client = new Client();
                $headers = ['X-Shopify-Access-Token' => $accessToken->value];
                
                $result = $this->callFunctionByName('getProductCollectionsDetails', compact('client', 'headers', 'url', 'store') );
                Log::info("Shopify :: getProductCollections :: response");
                Log::info($result);
                if(isset($result['variant'])){
                    return ['error' => 0, 'smart_collections' => $result['smart_collections']];
                }else{
                    return ['error' => 1, 'message' => "The product collection were not received as expected"];
                }

            } catch (Exception $e) {
                return ['error' => 1, 'message' => 'Exception | '.$e->getMessage(), 'trace' => $e->getTraceAsString()];
            }
        }

        private function getProductCollectionsDetails($parameters)
        {
            $client = $parameters['client'];
            $headers = $parameters['headers'];
            $url = $parameters['url'];
            $store = $parameters['store'];

            $response = $client->request('GET', str_replace('{store}', $store->value, $url) , [ 'headers' => $headers ] );
            return json_decode($response->getBody(), true);
        }

        public function getProductMetafields($marketplace_product_id, $seller_id)
        {
            sleep(3);
            $data = [];
            $store = Setting::where('seller_id', $seller_id)->where('key','WC')->first();
            $accessToken = Setting::where('seller_id', $seller_id)->where('key','ShopifyToken')->first();

            if(!isset($accessToken)) {
                return ['error' => 1, 'message' => 'Shopify access token not found'];
            }

            try {

                $client = new Client();
                $url = 'https://'.$store->value.'/admin/api/'.$this->api_version.'/graphql.json';
                $headers = ['X-Shopify-Access-Token' => $accessToken->value, 'Content-Type' => 'application/json'];                
                $items[] = "gid://shopify/Product/".$marketplace_product_id;

                $body = [
                    "query" => 'query getProductsByIds($ids: [ID!]!) { nodes(ids: $ids) { ... on Product { id title description handle productType status totalInventory vendor tags metafields(first: 250) { nodes { id key value } } variants(first: 250) { edges { node { id title displayName barcode price sku compareAtPrice inventoryQuantity createdAt updatedAt inventoryItem { id locationsCount { count } harmonizedSystemCode createdAt updatedAt } } } } category { name fullName } createdAt updatedAt } } }',
                    'variables' => [
                        "ids" => $items
                    ]
                ];

                $result = $this->callFunctionByName('getProducts', compact('client', 'headers', 'url', 'body') );
                $metafields = [];

                foreach ($result['data']['nodes'] as $key => $products) {
                    $metafields = $products['metafields']['nodes'];
                }
                
                if($metafields) {
                    return ['error' => 0, 'metafields' => $metafields];
                }else{
                    return ['error' => 1, 'message' => "The product collection were not received as expected"];
                }

            } catch (Exception $e) {
                return ['error' => 1, 'message' => 'Exception | '.$e->getMessage(), 'trace' => $e->getTraceAsString()];
            }
        }

        private function getProductMetafieldsDetails($parameters)
        {
            $client = $parameters['client'];
            $headers = $parameters['headers'];
            $url = $parameters['url'];
            $store = $parameters['store'];

            $response = $client->request('GET', str_replace('{store}', $store->value, $url) , [ 'headers' => $headers ] );
            return json_decode($response->getBody(), true);
        }

        private function getProducts($parameters)
        {
            $client = $parameters['client'];
            $headers = $parameters['headers'];
            $body = $parameters['body'];
            $url = $parameters['url'];


            $response = $client->post( $url, [
                'headers' => $headers,
                'body' => json_encode($body)
            ]);
        
            $response = json_decode( $response->getBody()->getContents() , true);
            $cursor = null;

            if (isset($response['data']['products']['pageInfo']) && $response['data']['products']['pageInfo']['hasNextPage']) {
                $cursor = $response['data']['products']['edges'][count($response['data']['products']['edges'])-1]['cursor'];
            }

            return ['data' => $response['data'], 'cursor' => $cursor];
        }
}

?>