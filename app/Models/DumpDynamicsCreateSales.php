<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class DumpDynamicsCreateSales extends Model
{
    /**
     * The database table used by the model.
     *
     * @var string
     */
    protected $table = 'dump_dynamics_create_sales';
    protected $connection = 'mysql2';

    public static function create($data){
        
        $dump_dynamics_create_sales = new self;
        $dump_dynamics_create_sales->order_type = $data['order_type'];
        $dump_dynamics_create_sales->seller_id = $data['seller_id'];
        $dump_dynamics_create_sales->fulfillment_id = $data['fulfillment_id'];
        $dump_dynamics_create_sales->order_id = $data['order_id'];
        $dump_dynamics_create_sales->shipment_id = $data['shipment_id'];
        $dump_dynamics_create_sales->dynamics_order_id = $data['dynamics_order_id'];
        $dump_dynamics_create_sales->payment_method = $data['payment_method'];
        $dump_dynamics_create_sales->location_id = $data['location_id'];
        $dump_dynamics_create_sales->shipping_charges = $data['shipping_charges'];
        $dump_dynamics_create_sales->customer_name = $data['customer_name'];
        $dump_dynamics_create_sales->customer_address = $data['customer_address'];
        $dump_dynamics_create_sales->city = $data['city'];
        $dump_dynamics_create_sales->country = $data['country'];
        $dump_dynamics_create_sales->customer_number = $data['customer_number'];  
        $dump_dynamics_create_sales->customer_email = $data['customer_email'];
        $dump_dynamics_create_sales->invoice_account = $data['invoice_account'];
        $dump_dynamics_create_sales->logistics_partner = $data['logistics_partner'];
        $dump_dynamics_create_sales->consignment_number = $data['consignment_number'];
        $dump_dynamics_create_sales->comments = $data['comments'];
        $dump_dynamics_create_sales->barcode = $data['barcode'];
        $dump_dynamics_create_sales->sku = $data['sku'];
        $dump_dynamics_create_sales->quantity = $data['quantity'];
        $dump_dynamics_create_sales->unit_price = $data['unit_price'];
        $dump_dynamics_create_sales->net_amount = $data['net_amount'];
        $dump_dynamics_create_sales->charges_code = $data['charges_code'];
        $dump_dynamics_create_sales->charges_amount = $data['charges_amount'];
        $dump_dynamics_create_sales->save();

    }
    
    public static function organizeAndCreateRecord($data){

        $order = $data['order'];
        $shipment = $data['shipment'];
        $fulfillment_order = $data['fulfillment_order'];
        $sales_type = $data['dump_sales']['OrderType'];
        $data = $data['dump_sales'];
    
        foreach($data['Items'] as $item){
            $organized_data = [];

            $organized_data['order_type'] = $sales_type;
            $organized_data['seller_id'] = $order->seller_id;
            $organized_data['fulfillment_id'] =  $fulfillment_order->id;
            $organized_data['order_id'] = $order->id;
            $organized_data['shipment_id'] =  $shipment->id;
            $organized_data['dynamics_order_id'] = $data['OrderId'];
            $organized_data['payment_method'] = $data['PaymentMethod'];
            $organized_data['location_id'] = $data['locationId'];
            $organized_data['shipping_charges'] = $data['ShippingCharges'];
            $organized_data['customer_name'] = $data['CustomerName'];
            $organized_data['customer_address'] = $data['CustomerAddress'];
            $organized_data['country'] = $data['Country'];
            $organized_data['city'] = $data['City'];
            $organized_data['customer_number'] = $data['CustomerContactNumber'];
            $organized_data['customer_email'] = $data['CustomerEmail'];
            $organized_data['invoice_account'] = $data['InvoiceAccount'];
            $organized_data['logistics_partner'] = $data['LogisticsPartner'];
            $organized_data['consignment_number'] = $data['ConsignmentNumber'];
            $organized_data['fbr_invoice_id'] = $data['FBRInvoiceId'];
            $organized_data['comments'] = (isset($data['Comment']) ? $data['Comment'] : "") ;
    
            $organized_data['barcode'] = $item['Barcode'];
            $organized_data['sku'] =  $item['Sku'];
            $organized_data['quantity'] = $item['Quantity'];
            $organized_data['unit_price'] = 0;
            $organized_data['net_amount'] = 0;
            $organized_data['charges_code'] = ( isset($item['ChargesCode']) ? $item['ChargesCode'] : "" );
            $organized_data['charges_amount'] = ( isset($item['ChargesAmount']) ? $item['ChargesAmount'] : 0 );

            self::create($organized_data);
            // $final_organized_data[] = $organized_data;
        }
        // dd($final_organized_data);
    }


}
