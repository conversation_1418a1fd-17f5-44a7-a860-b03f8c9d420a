<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class OrderItem extends Model
{
    protected $connection = 'mysql2';

    public $incrementing = false;

	protected $casts = [
		'id' => 'int',
		'product_id' => 'int',
		'unit_price' => 'float',
		'quantity' => 'int'
	];

	protected $fillable = [
		'category',
		'order_id',
		'product_id',
		'unit_price',
		'quantity',
		'sub_total',
		'status',
		'reason'
    ];
    
    public function order()
	{
		return $this->belongsTo('App\Models\Order');
	}
}
