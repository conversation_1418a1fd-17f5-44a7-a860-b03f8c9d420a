<?php

namespace App\Jobs;

use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Mail;

class ZeenOrder<PERSON>older<PERSON>ob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $request;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($request)
    {
        $this->request = $request;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $generate_again = false;

        try {

            $curl = curl_init();
            $endpoint_url = env('ENDPOINT_URL');

            curl_setopt_array($curl, array(
                CURLOPT_URL =>  $endpoint_url.'/api/zeen/order',
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_ENCODING => "",
                CURLOPT_MAXREDIRS => 10,
                CURLOPT_TIMEOUT => 60,
                CURLOPT_FOLLOWLOCATION => true,
                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                CURLOPT_CUSTOMREQUEST => "POST",
                CURLOPT_POSTFIELDS => json_encode($this->request['data']),
                CURLOPT_HTTPHEADER => array(
                    "Content-Type: application/json",
                    "X-Shopify-Shop-Domain: ".$this->request['shop'],
                    "X-Shopify-Hmac-Sha256: ".$this->request['token'],
                    "X-Shopify-Topic: ".$this->request['topic']
                ),
            ));

            $response = curl_exec($curl);
            $httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);

            if ($httpCode == '200') {
                Log::info('Zeen Order Holder Api Unity Endpoint Request Success');
                curl_close($curl);
                Log::info('Zeen Order Holder Api response '.$response);
            } else {
                $generate_again = true;

                Mail::raw('Zeen Order Holder Api Unity Endpoint Event Generated again | '.$httpCode.' | '.json_encode($response), function ($m)  {
                    $m->to('<EMAIL>')->subject('Zeen Order Holder Api');
                });
                
                Log::info('Zeen Order Holder Api Unity Endpoint Event Generated again | '.$httpCode.' | '.$response);
            }

        } catch(Exception $e) {

            Log::critical('Zeen Order Holder Api error '.$e->getMessage());
            Log::critical($e->getTraceAsString());

            $activity_id = activity()
            ->withProperties( json_encode($e->getMessage()) )
            ->log('Zeen Order Holder Api');

            $generate_again = true;
            
            Mail::raw('Zeen Order Holder Api Unity Endpoint Event Generated again | '.$e->getMessage(), function ($m)  {
                $m->to('<EMAIL>')->subject('Zeen Order Holder Api');
            });
        }

        if ($generate_again) {
            $request_data = array('data' => $this->request['data'], 'url' => $this->request['url'], 'token' => $this->request['token']);
            sleep(120);

            ZeenOrderHolderJob::dispatch($request_data)->onQueue('zeenOrderHolder');
        }
    }
}
