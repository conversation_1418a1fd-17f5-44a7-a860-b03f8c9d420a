<?php

namespace App\Exports;

use App\Models\Shipment;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;

class ReportExport implements FromCollection, WithHeadings, WithMapping
{
    /**
    * @return \Illuminate\Support\Collection
    */
    public function collection()
    {
        // return Shipment::all();

        $terminal_status = ['Delivered', 'Cancelled', 'Return', 'Lost'];

        return Shipment::whereNotIn('shipments.status',$terminal_status)
        ->join('couriers','shipments.courier_id','couriers.id')
        ->join('orders','shipments.order_id','orders.id')
        ->selectRaw('orders.entity_id as Magento_Order_ID,orders.marketplace_reference_id as Order_ID,shipments.tracking_number as CN,couriers.name as Courier_Name,shipments.status as Unity_Status')
        ->whereIn('shipments.seller_id',['120'])
        ->get();
    }

    public function headings(): array
    {
        return [
            'Entity ID',
            'Order ID',
            'CN',
            'Courier Name',
            'Unity Status',
            'Magento Status'
        ];
    }

    public function map($shipments): array
    {
        $api_status = '';
        $ready_for_dispatch = [
            'Ready for Dispatch'
        ]; // Unity Status

        $complete = [
            'Dispatched'
        ];

        $in_transit = [
            'Pending Delivery'
        ];

        $delivered = [
            'Delivered'
        ];

        $return_intransit = [
            'Pending Return'
        ];

        $returned = [
            'Return'
        ];

        $parcel_lost = [
            'Lost'
        ];

        $status = $shipments->Unity_Status;

        if (in_array($status, $ready_for_dispatch)) {  
            $api_status = 'Readyfordispatch';  
        } else if (in_array($status, $complete)) {          
            $api_status = 'complete';
        } else if (in_array($status, $in_transit)) {          
            $api_status = 'Intransit';
        } else if (in_array($status, $delivered)) {
            $api_status = 'delivered';
        } else if (in_array($status, $return_intransit)) {
            $api_status = 'return_instransit';
        } else if (in_array($status, $returned)) {
            $api_status = 'returned';
        } else if (in_array($status, $parcel_lost)) {
            $api_status = 'Parcel_lost';
        }

        return [
            $shipments->Magento_Order_ID,
            $shipments->Order_ID,
            $shipments->CN,
            $shipments->Courier_Name,
            $shipments->Unity_Status,
            $api_status
        ];
    }
}
