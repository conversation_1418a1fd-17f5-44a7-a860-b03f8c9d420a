<?php 

namespace App\Service;

use App\Helpers\BundledEmailFill;
use App\Helpers\OrderComment;
use App\Helpers\OrderStatusSync;
use App\Models\Shipment;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use App\Models\ShipmentCourierHistory;
use App\Traits\ExceptionTrait;
use Carbon\Carbon;
use Exception;
use DB;

class ShipmentStatusSync
{
    use ExceptionTrait;

    public function khaadiSync($event)
    {
        $request_url = null;
        $request_data = null;
        $response_data = null;
        $terminal = false;
        $terminal_status = ['Delivered', 'Cancelled', 'Return', 'Lost'];
        $force_sync = ( isset($event->force_sync) ? $event->force_sync : false );
        $ignore_status_sync = false;

        $unity = explode(',',env('UNITY_POC'));
        $others = explode(',',env('KHAADI_POC'));

        $dispatched_from_store_statuses = [
            'Dispatched',
            'Picked By Movex',
            'Shipment - Rider Picked',
            'PICKED FROM SHIPPER',

            //lcs
            'Shipment picked',
            //tcs
            'Received'
        ];

        $ready_for_dispatch_status = [
            'Ready for Dispatch'
        ]; // Unity Status


        $in_transit = [
            'Hold in MOVEX office',
            'Misroute',
            'Holiday Closed',
            'Shipment - On Hold',
            'Hold/Pending',
            'Shipment - In Transit',
            'Shipment - Arrived at Destination',
            // 'Shipment - Out for Delivery',//
            'Shipment - Rider Exchange',
            'Shipment - Not Attempted',
            'Shipment - Delivery Unsuccessful',
            'Shipment - Non-Service Area',
            'Shipment - Misrouted',
            'Shipment - Re-Attempt',
            'Shipment - On Hold for Self Collection',
            'Shipment - Misroute Forwarded',
            'Intercept Approved',
            'Replacement-Not Collected',
            'Replacement - In Transit',
            'Replacement - Arrived at Origin',
            'Replacement - Dispatched',
            'Replacement - Delivery Unsuccessful',
            'Replacement - Collected',
            'Replacement - Rider Exchange',
            'Awaiting Dispatch',
            // 'Rider is out for Delivery',
            'Delivery Attempt Failed',
            'Ready for Delivery',
            'Delivery in progress',
            'In Transit',
            'Awaiting Transit',
            'QC Rejected - Hub',
            // 'On Delivery',//
            'Loading',
            'Unloading',
            'Not Home',
            'Closed on Arrival',
            'Bad Address',
            'Delivery Attempt Failed',
            'Awaiting Dispatch',
            // 'Rider is out for Delivery',//
            'Delivery in progress',//
            'In Transit',
            'Return - Confirmation Pending',
            'Shipment - On Hold for Self Collection',
            'Shipment - Misroute Forwarded',
            'Shipment - Re-Attempt Requested',
            'Shipment - Misrouted',
            'Shipment - Non-Service Area',
            'Intercept Requested',
            'Intercept Approved',
            'Refused Delivery',
            'Arrived at OPS',
            'Shipment - Arrived at Origin',
            'Collected',
            "Collected by Rider",
            "Arrived at Rider's facility",


            'REACHED AT DEST. BRANCH', 
            'UNDELIVERED', 
            'REACHED at DEST. BRANCH', 
            'MOVED TO DEST. BRANCH', 
            // 'OUT FOR DELIVERY', 
            // 'Out For Delivery', 
            'Un Delivered' , 
            'MOVED to DEST. BRANCH', 
            'MOVED to ORIGIN BRANCH',


            'Bagging Process completed',
            'Loading process completed',
            'Arrival at HUB',
            'Arrival at Destination',
            // 'Process-On Route',
            'Address Closed',
            'Consignee Not Available',
            'Consignee Shifted',
            'Incomplete Address',
            'Address unlocated',
            'In-Transit',
            'No Such Consignee',
            'Cash Not Available',
            'Re-Forwarded',
            'Re-forwarded',
            'Friday Closed',
            'Restricted Area',
            'Open Box Delivery Demand',
            'Hold For Collection',
            'Consignee unreachable',
            'Consignee\'s mobile unreachable',
            'Future Delivery',
            'Saturday Closed',
            'Consignee out of City/Town',
            'Hold for Shipper advice',
            'Destination Arrival',
            'Re-Attempt By Customer',


            //lcs
            'Missroute', 
            'Dispatched',
            'Assigned to courier',
            // 'Assign to Courier',
            'Assigned to Courier',
            'Arrived at Station', 
            'Pending Refuse',
            'Refused',
            //tcs
            'Delivery address falls under TCS non service area',
            'Awaiting receiver collection',
            'Unable To Contact',
            'CLOSED ON ARRIVAL',
            'Consignee Not Available',
            'REFUSED DELIVERY',
            'Scheduled for delivery',
            'On hold at TCS facility',
            'In Transit',
            'Out For Delivery',
            'Courier out for delivery',
            'Arrived at TCS Facility',
            'Departed From Origin',
            //mnp new status
            'FORWARD TO',
            'Given to',
            'ROUTED',
            //tpl-rider
            'Transit to Hub',
            'Awaiting Linehaul Dispatch (Last Mile)',
            'Awaiting Linehaul Return',
            'Transit to Hub Return',
            'Awaiting Linehaul Dispatch (Sort)'


        ];

        $holded_instore_statuses = [];

        $delivered_by_store_statuses = [
            'Shipment - Delivered',
            'Delivered',
            'DELIVERED'
        ];

        $lost_statuses = [
            'Shipment - Lost',
            'Lost',
            'Shipment - Case Closed',
            'Untraceable'
        ];

        $return_to_store_statuses = [
            'Replacement - Delivered to Shipper',
            'Return - Delivered to Shipper',
            'Returned',
            'RETURNED',
            'Return to Shipper',
            'Direct Return to Vendor',
            'Return',

            'RETURN SUBMITTED',
            'RETURNED TO SHIPPER AFTER ARRIVED AT ORIGIN',

            //lcs
            'Returned to shipper',
            //tcs
            'RETURN TO SHIPPER',
            'Returned to sender',
            //mnp status switch from return in intrasit to return to store
            'DRTV'


        ];

        $returned_intransit_statuses = [
            'Return Unsuccessful for CX and Sales',
            'Return To Origin',
            'Awaiting Return',
            'Return in Progress',
            'Return Attempt Failed',
            'Return In Transit',
            'Ready to Return',
            'Return to Origin',
            'Return - Arrived at Origin',
            'Return - In Transit',
            'Return - Confirm',
            'Return - Dispatched',
            'Return - Delivery Unsuccessful',

            'OUT for RETURN SUBMISSION',
            'OUT FOR RETURN SUBMISSION',
            'MOVED TO ORIGIN BRANCH',
            'MOVED to ORIGIN BRANCH',
            'REACHED AT ORIGIN BRANCH',

            'Refused-Delivery',
            'Refused-Late Delivery',
            'Return to Origin',
            'Non Service Area',
            'Out Of Service Area',
            'Return in process',
            'Refused-Fake Order',
            'Refused-Duplicate Order',
            'Refused-Mind Change',
            'Refused-Already Cancelled',
            'Refused-Quality Issue',
            'Direct Return to Vendor',
            'Return Bagging Process completed',
            'Return Loading process completed',
            'Return Arrival at Origin',
            'Return Process-On Route',

            //lcs
            'Being Return',
            'Return To Sender',
            'Return to Origin', 
            'Ready for Return',
            'Shipment handover',
            //TCS
            'Receiver refused to accept shipment',
            'Undelivered due to incorrect address',
            'Receiver shifted from delivery address',
            'Retuned to Origin for P.O',
            'PH Not Responding / Unable to locate delivery address',
            'Ready for Return',
            'Returned to TCS origin',
            //mnp new
            'Refused to Received',
            'CONTACTING CONSIGNEE', 
            //Rider
            'Awaiting Linehaul Return',
            'Transit to Hub Return'

        ];

        $ignore_statuses = [
            'Shipment - Booked',
            'Shipment - Cancelled',
            'Booked',
            'Click to Call a Rider',
            'Rider on its way',
            'Booking',
            'No Such Consignee',
            'Non Service Area',
            'Unable to Locate',
            'Saturday Closed',
            'Friday Closed',
            'Consignee Shifted',
            'BAGGED',
            'MANIFESTED',
            'NCI',
            'UNDELIVERED',
            'DEBAG',
            'BOOKING',
            'LOADED',
            'Short Contents',
            ' ',
            '',
            'Shipment - Return Confirmation Pending',
            'Return - Rider Exchange',
            'Return Note Shifted',
            'Return - On Hold',
            'Shipment - Case Closed',
            'Cancelled',
            'Shipment - Arrival Service Center',
            'Return - Not Attempted',
            'Replacement - Not Collected',

            'CONSIGNMENT BOOKED',
            'ARRIVED AT ORIGIN BRANCH',

            'Pre-Booking data loaded',
            'Booked by Customer',
            'Arrival at Origin',


            'Pickup Request Sent',
            'Pickup Request not Send',
            //tcs
            'CANCELLED',
            //rider
            'Order Booked',
            //call courier
            'CONSIGNMENT CANCELLED'
        ];

        $out_for_delivery_statuses = [
            'Shipment - Out for Delivery',
            'On Delivery',
            'Rider is out for Delivery',
            'Process-On Route',

            'OUT FOR DELIVERY', 
            'Out For Delivery', 

            //lcs
            'Assign to Courier',

            //mnp new statuses
            'Incomplete Address',
            'Unable to Locate',
            'Reforward',
            'Closed on Arrival',
            'Consignee not Avaialble',
            'No Such Consignee',
            'Non Service Area',
            'Friday Closed',
            'Saturday Closed',
            'HOLD FOR COLLECTION'

        ];


        $mnp_status = [
            'Loading',
            'Unloading',
            'Arrived at OPS',
            'On Delivery'
        ];



        $LCS_dispatched_from_store_statuses = [
            'Dispatched',
            'Shipment Picked'
        ];

        $LCS_in_transit = [
            'Pending',
            'Arrived at Station',
            'Shipment picked in',
            'Assigned to courier',
            'Dispatched',
        ];

        $LCS_lost_statuses = [
            'lost'
        ];

        $LCS_delivered_by_store_statuses = [
            'Delivered'
        ];

        $LCS_ready_for_dispatch_status = [
            'Ready for Dispatch'
        ];

        $LCS_return_to_store_statuses = [
            'Returned',
            'Return to shipped',
        ];

        $LCS_returned_intransit_statuses = [
            'Ready for Return',
            'Return to Origin',
            'Return To Sender'
        ];
        
        $shipment_history = (object) $event->shipment;
        $status = $shipment_history->status;
        $api_status = '';
        Log::info($event->shipment);
        $rs_stat = 0;
        $after_delivery_attempt_status = 0;
        $delivered_stat = 0;

        $unity = explode(',',env('UNITY_POC'));
        $others = explode(',',env('KHAADI_POC'));

        if($shipment_history->type == null){
            
            $process_status = 'Failed';
            $message = "Tracking status not found in KHAADI mapped status list for <br> Tracking number : <b>".$shipment_history->tracking_number."</b> <br>".$shipment_history->name." Status : <b>".$status."</b>";


            $temp_shipment = Shipment::whereId($shipment_history->shipment_id)->first(['cod_received','return_receive_at_store']);
            
            if($temp_shipment->cod_received != 0) {
                $message = "Status not synced as the Shipment COD status is already synced <br> Tracking number : <b>".$shipment_history->tracking_number."</b> <br>".$shipment_history->name." Status : <b>".$status."</b>";
            } elseif ($temp_shipment->return_receive_at_store != 0) {
                $message = "Status not synced as the Shipment Return Posted status is already synced <br> Tracking number : <b>".$shipment_history->tracking_number."</b> <br>".$shipment_history->name." Status : <b>".$status."</b>";
            } else {


                if ($shipment_history->courier_id == 5) {
                    
                    if (in_array($status, $LCS_dispatched_from_store_statuses)) {
                        $api_status = 'dispatched_from_store';
                        
                    } else if ($this->stringMatch($status, $LCS_in_transit)) {
                        // $histories = ShipmentCourierHistory::where('shipment_id',$shipment_history->shipment_id)->orWhere('status','LIKE','Return To Origin%')->orWhere('status','LIKE','Ready for Return%');
                        $histories = ShipmentCourierHistory::where('shipment_id',$shipment_history->shipment_id)->where(function ($query) {
                            $query->where('status','LIKE','Return To Origin%')
                            ->orWhere('status','LIKE','Ready for Return%');
                        });

                        if($histories->exists()) {
                            Log::info('in if condition');
                            $api_status = 'returned_intransit_instore';
                        } else {
                            Log::info('not in if condition');
                            $api_status = 'intransit_store';
                        }
                                                
                    } else if ($this->stringMatch($status, $LCS_lost_statuses)) {
                        $terminal = true;
                        $api_status = 'parcel_lost';
                    
                    } else if ($this->stringMatch($status, $LCS_delivered_by_store_statuses)) {
                        $terminal = true;
                        $api_status = 'delivered_by_store';
                    
                    } else if ($this->stringMatch($status, $LCS_ready_for_dispatch_status)) {
                        $api_status = 'ready_for_dispatch_from_store';
                    
                    } else if ($this->stringMatch($status, $LCS_return_to_store_statuses)) {
                        $terminal = true;
                        $api_status = 'return_to_store';
                    
                    } else if ($this->stringMatch($status, $LCS_returned_intransit_statuses)) {
                        $api_status = 'returned_intransit_instore';
                    }
                    
                } else {
                    
                    if (in_array($status, $dispatched_from_store_statuses)) {
                        $api_status = 'dispatched_from_store';
                    
                    } else if (in_array($status, $in_transit)) {
                    
                        $attempt_counts = ShipmentCourierHistory::where('shipment_id',$shipment_history->shipment_id)->where('id','<=',$shipment_history->id)->whereIn('status',$out_for_delivery_statuses)->count();
                        if($attempt_counts > 0) {
                            // $api_status = env('KHAADI_DELIVERY_ATTEMPT_STATUS').($attempt_counts);
                            $after_delivery_attempt_status = 1;
                        } else {
                            $api_status = 'intransit_store';
                        }
    
                        if (in_array($status, $mnp_status)) {
                            if($shipment_history->courier_id == 4) {
                                $histories = ShipmentCourierHistory::where('shipment_id',$shipment_history->shipment_id)->whereIn('status',['Return To Origin','Ready to Return']);
                                if($histories->exists()) {
                                    Log::info('in if condition');
                                    $api_status = 'returned_intransit_instore';
                                } else {
                                    Log::info('not in if condition');
                                }
                            }
                        }
                    
                    } elseif(in_array($status, $out_for_delivery_statuses)) {
                    
                        $attempt_counts = ShipmentCourierHistory::where('shipment_id',$shipment_history->shipment_id)->whereIn('status',$out_for_delivery_statuses)->count();
                    
                        if($attempt_counts <= 2){
                            $api_status = env('KHAADI_DELIVERY_ATTEMPT_STATUS').($attempt_counts);
                        }
                        if($attempt_counts == 3){
                            $after_delivery_attempt_status = 1;
                            // Mail::raw('Alert - 3rd Delivery attempt | Order ID: '.$shipment_history->marketplace_reference_id, function ($m) use ($others,$unity) {
                            //     $m->to($others)
                            //     ->bcc($unity)
                            //     ->subject('Khaadi - Shipment Status Sync');
                            // });
                            BundledEmailFill::add(array('key' => config('enum.bundled_email')['DELIVERYATTEMPT'],'order_id' => $shipment_history->order_id,'shipment_id' => $shipment_history->shipment_id,'marketplace_reference_id' => $shipment_history->marketplace_reference_id,'tracking_number' => $shipment_history->tracking_number,'details' => null));
                        }
                        if($attempt_counts > 3){
                            $after_delivery_attempt_status = 1;
                        }
    
    
                        if (in_array($status, $mnp_status))
                        {
                        
                            if($shipment_history->courier_id == 4)
                            {
                        
                                $histories = ShipmentCourierHistory::where('shipment_id',$shipment_history->shipment_id)->whereIn('status',['Return To Origin','Ready to Return']);
                                if($histories->exists())
                                {
                                
                                        Log::info('in if condition');
                                        $api_status = 'returned_intransit_instore';
                                
                                }
                                else{
                                    Log::info('not in if condition');
                                }
                            
                            }
                        }
    
                    } else if (in_array($status, $lost_statuses)) {
                        $terminal = true;
                        $api_status = 'parcel_lost';
                
                    } else if (in_array($status, $delivered_by_store_statuses)) {
                        $terminal = true;
                        $api_status = 'delivered_by_store';
                
                    } else if (in_array($status, $ready_for_dispatch_status)) {
                        $api_status = 'ready_for_dispatch_from_store';
                
                    } else if (in_array($status, $return_to_store_statuses)) {
                        $terminal = true;
                        $api_status = 'return_to_store';
                
                    } else if (in_array($status, $returned_intransit_statuses)) {
                        $api_status = 'returned_intransit_instore';
                    }
                }
                

            }

            Log::info($shipment_history->tracking_number.' | khaadi_api_status |'.$api_status);

            /////////////////////////////////////////////////////////////////////////////////////////////////////

            if ($api_status) {
                
                try {
                    
                    if (    !$force_sync && 
                            !$terminal && 
                            in_array(DB::connection('mysql2')->table('shipments')->where('id', $shipment_history->shipment_id)->value('status'), $terminal_status) &&
                            ShipmentCourierHistory::where('shipment_id', $shipment_history->shipment_id)->where('storefront_sync',2)->whereIn('status', array_merge($lost_statuses, $delivered_by_store_statuses, $return_to_store_statuses))->exists() 
                        ) {
                            $ignore_status_sync = true;
                            $message = "Status not synced because Terminal tracking status is already synced for this <br> Tracking number : <b>".$shipment_history->tracking_number."</b> <br>".$shipment_history->name." Status : <b>".$status."</b>";
                    } else {
                        
                        $curl = curl_init();
                        $comment = "Tracking Update by <b>".$shipment_history->name."</b> for <br> Tracking number : <b>".$shipment_history->tracking_number."</b> <br> Hub : <b>".$shipment_history->location_name."</b> <br>
                        Khaadi Status : <b>".$api_status."</b> <br> ".$shipment_history->name." Status : <b>".$status."</b> <br>"
                        .$shipment_history->name." Status Time : <b>".$shipment_history->status_at."</b>";
            
                        $data = array("statusHistory" => array("comment" => $comment , "status" => $api_status) );
                        $request_data = $data;
    
                        $request_url = env('KHAADI_URL')."/rest/V1/orders/".$shipment_history->entity_id."/comments";
                        curl_setopt_array($curl, array(
                        CURLOPT_URL => $request_url,
                        CURLOPT_RETURNTRANSFER => true,
                        CURLOPT_ENCODING => "",
                        CURLOPT_MAXREDIRS => 10,
                        CURLOPT_TIMEOUT => 60,
                        CURLOPT_FOLLOWLOCATION => true,
                        CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                        CURLOPT_CUSTOMREQUEST => "POST",
                        CURLOPT_POSTFIELDS => json_encode($data),
                        CURLOPT_HTTPHEADER => array(
                            "Authorization: Bearer ".env('KHAADI_TOKEN'),
                            "Content-Type: application/json"
                        ),
                        ));
            
                        $response = curl_exec($curl);
                        $response_data = $response;
            
                        $httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
    
                        if ($httpCode == '200' && $response == 'true') {
                            Log::info('Khaadi Order Status Api Request Success');
                            $process_status = 'Success';
                            $message = $comment." <br>Comment API Response : <b>".$response."</b>  <br>Khaadi Status : <b>'.$api_status.'</b> ";

                            //send complete instore after delivered
                            //where cod < 1
                            if($api_status == 'delivered_by_store' && Shipment::whereId($shipment_history->shipment_id)->value('cod') < 1){
                                $delivered_stat = 1;
                            }

                        } elseif ($httpCode == '200' && json_encode($response) == '"\ntrue"') {
                            Log::info('Khaadi Order Status Api Request Success');
                            $process_status = 'Success';
                            $message = $comment." <br>Comment API Response : <b>".json_encode($response)."</b>  <br>Khaadi Status : <b>'.$api_status.'</b> ";

                            //send complete instore after delivered
                            //where cod < 1
                            if($api_status == 'delivered_by_store' && Shipment::whereId($shipment_history->shipment_id)->value('cod') < 1){
                                $delivered_stat = 1;
                            }
                        } else {
                            $message = 'Tracking status not updated at storefront | '.json_encode($response).' | '.$api_status.' | '.$shipment_history->marketplace_reference_id.' | '.$shipment_history->tracking_number;
                            // Mail::raw($message, function ($m)  {
                            //     $m->to(['<EMAIL>','<EMAIL>'])
                            //         ->bcc('<EMAIL>')
                            //         ->subject('Shipment Tracking Storefront Synced Process');
                            // });
    
                            if($others[0] != ""){
                                // Mail::raw($message, function ($m)  use($others,$unity) {
                                //     $m->to($others)
                                //         ->bcc($unity)
                                //         ->subject('Shipment Tracking Storefront Synced Process');
                                // });
                            }
                            else{
                                // Mail::raw($message, function ($m)  use($unity) {
                                //     $m->to($unity)
                                //         ->subject('Shipment Tracking Storefront Synced Process');
                                // });
                            }
    
    
                            $this->addException(    119,
                                                    config('enum.exception_types')['SHIPMENTSTATUSSYNC'],
                                                    ( strlen($message) > 250 ? substr($message,0,240) . "..." : $message ),
                                                    'Order',
                                                    $shipment_history->order_id,
                                                    $shipment_history->marketplace_reference_id,
                                                    json_encode(['message' => $message, 'request_url' => $request_url, 'query_data' => $event->shipment]),
                                                    ( $request_data ? json_encode($request_data) : null),
                                                    ( $response_data ? json_encode($response_data) : null)
                                                );
                        }
    
                        
                        curl_close($curl);
                        Log::info('Shipment Status Api response '.json_encode($response));
                    }
                    
                    
                } catch(\Exception $e){

                    $message = 'Tracking status not updated at storefront | '.$shipment_history->marketplace_reference_id;
                    $activity_id = activity()
                    ->withProperties(['response' => json_encode($e->getMessage()) , 'dump' => $e->getTraceAsString()])
                    ->log('Khaadi Order Status Api');

                    if($others[0] != ""){
                        // Mail::raw($message.' | '.$e->getMessage(), function ($m)  use($others,$unity) {
                        //     $m->to($others)
                        //         ->bcc($unity)
                        //         ->subject('Shipment Tracking Storefront Synced Process');
                        // });
                    }
                    else{
                        // Mail::raw($message.' | '.$e->getMessage(), function ($m)  use($unity) {
                        //     $m->to($unity)
                        //         ->subject('Shipment Tracking Storefront Synced Process');
                        // });
                    }

                    // Mail::raw($message.' | '.$e->getMessage(), function ($m)  {
                    //     $m->to(['<EMAIL>','<EMAIL>'])
                    //         ->bcc('<EMAIL>')
                    //         ->subject('Shipment Tracking Storefront Synced Process');
                    // });
                    
                    Log::info('Shipment Status Api error '.$e->getMessage());
                }
            } else if (in_array($status, $ignore_statuses)) {
                $ignore_status_sync = true;
            } elseif($after_delivery_attempt_status > 0){
                $ignore_status_sync = true;
                if(in_array($status, $out_for_delivery_statuses)){
                    Log::info('Delivery Attempts more than 2 with status | '.$status.' | '.$shipment_history->marketplace_reference_id.' | '.$shipment_history->tracking_number);
                    $message = "Status not synced as more than 2 delivery attempts have been made <br> Tracking number : <b>".$shipment_history->tracking_number."</b> <br>".$shipment_history->name." Status : <b>".$status."</b>";
                } else{
                    $message = "Status not synced as these are intransit after out for delivery attempts <br> Tracking number : <b>".$shipment_history->tracking_number."</b> <br>".$shipment_history->name." Status : <b>".$status."</b>";
                }
            } else {
                $ignore_status_sync = true;
                // Mail::raw($message, function ($m)  {
                //     $m->to('<EMAIL>')->subject('Shipment Tracking Storefront Synced Process');
                // });
            }

            $status = $process_status;
            $order_id = $shipment_history->order_id;
            $key = 'Shipment Tracking Storefront Synced Process';
            Log::info($message);
        }
        else{
            
            $ship_stat = Shipment::whereTrackingNumber($shipment_history->tracking_number)->whereOrderId($shipment_history->order_id)->where('type','reverse')->first();
            if(isset($ship_stat) && $ship_stat->status == 'Delivered'){
                try{
                    $process_status = 'Failed';
                    $request_url = config('enum.env_vars')['KHAADIURL'].'/rest/V1/rma/return-received-shipment';
                    Log::info('Reverse Shipment Delivered Status Sync Api Request Payload | Request_url: '.$request_url.' | Payload: {"header": ["Authorization: Bearer '.config('enum.env_vars')['REVERSESYNCTOKEN'].'","Content-Type: application/json"]},{"returns":[{"reversebookingcn": "'.$shipment_history->tracking_number.'","comments": "Delivered"}]}');


                    $curl = curl_init();
                    curl_setopt_array($curl, array(
                    CURLOPT_URL => $request_url,
                    CURLOPT_RETURNTRANSFER => true,
                    CURLOPT_ENCODING => '',
                    CURLOPT_MAXREDIRS => 10,
                    CURLOPT_TIMEOUT => 0,
                    CURLOPT_FOLLOWLOCATION => true,
                    CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                    CURLOPT_CUSTOMREQUEST => 'POST',
                    CURLOPT_POSTFIELDS =>'{
                        "returns": [
                            {
                                "reversebookingcn": "'.$shipment_history->tracking_number.'",
                                "comments": "Delivered"
                            }
                        ]
                    }',
                    CURLOPT_HTTPHEADER => array(
                        'Authorization: Bearer '.config('enum.env_vars')['REVERSESYNCTOKEN'],
                        'Content-Type: application/json'
                    ),
                    ));

                    // $response = curl_exec($curl);
                    $response = json_decode(curl_exec($curl),true);
                    $httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
                    
                    Log::info('Reverse Shipment Delivered Status Sync Api Response for cn: '.$shipment_history->tracking_number.' | HTTP Code | '.$httpCode.' | Response | '.json_encode($response));
                    if ($httpCode == '200') {
                        Log::info('Reverse Shipment Delivered Status Sync Api Request Success');
                        $process_status = 'Success';
                        $message = "Reverse Shipment Delivered Status Sync Success";
                    }
                    elseif($httpCode == '200' && isset($response['false'])){
                        Log::info('Reverse Shipment Delivered Status Sync Api Response for cn: '.$shipment_history->tracking_number.' in 1st else if');
                        $message = 'Reverse Shipment Delivered Status not updated at storefront | '.$response['false'].' | '.$shipment_history->marketplace_reference_id.' | '.$shipment_history->tracking_number;
                        // Mail::raw($message, function ($m)  {
                        //     $m->to(['<EMAIL>','<EMAIL>','<EMAIL>','<EMAIL>',env('KHAADI_POC')])->subject('Reverse Shipment Delivered Status Synced Process');
                        // });

                        // Mail::raw($message, function ($m)  {
                        //     $m->to(['<EMAIL>'])->subject('Reverse Shipment Delivered Status Synced Process');
                        // });

                        $this->addException(    119,
                                                config('enum.exception_types')['MAGENTOREVERSESHIPMENTSYNC'],
                                                ( strlen($message) > 250 ? substr($message,0,240) . "..." : $message ),
                                                'Order',
                                                $shipment_history->order_id,
                                                $shipment_history->marketplace_reference_id,
                                                json_encode(['message' => $message, 'request_url' => $request_url, 'query_data' => $event->shipment]),
                                                ( $request_url ? $request_url : null),
                                                ( $response ? json_encode($response) : null)
                                            );
                    } 
                    elseif($httpCode == '500'){
                        Log::info('Reverse Shipment Delivered Status Sync Api Response for cn: '.$shipment_history->tracking_number.' in 2nd else if');
                        throw new Exception('Reverse Shipment Delivered Status Synced Process | HTTP Code 500');
                    }
                    else {
                        Log::info('Reverse Shipment Delivered Status Sync Api Response for cn: '.$shipment_history->tracking_number.' in else only');
                        $message = 'Reverse Shipment Delivered Status not updated at storefront | '.json_encode($response).' | '.$shipment_history->marketplace_reference_id.' | '.$shipment_history->tracking_number;
                        // Mail::raw($message, function ($m)  {
                        //     $m->to(['<EMAIL>','<EMAIL>','<EMAIL>','<EMAIL>',env('KHAADI_POC')])->subject('Reverse Shipment Delivered Status Synced Process');
                        // });
                        // Mail::raw($message, function ($m)  {
                        //     $m->to(['<EMAIL>'])->subject('Reverse Shipment Delivered Status Synced Process');
                        // });


                        $this->addException(    119,
                                                config('enum.exception_types')['MAGENTOREVERSESHIPMENTSYNC'],
                                                ( strlen($message) > 250 ? substr($message,0,240) . "..." : $message ),
                                                'Order',
                                                $shipment_history->order_id,
                                                $shipment_history->marketplace_reference_id,
                                                json_encode(['message' => $message, 'request_url' => $request_url, 'query_data' => $event->shipment]),
                                                ( $request_url ? $request_url : null),
                                                ( $response ? json_encode($response) : null)
                                            );
                    }

                    $status = $process_status;
                    $order_id = $shipment_history->order_id;
                    $key = 'Reverse Shipment Delivered Status Synced Process';
                    Log::info($message);

                    curl_close($curl);

                } catch(Exception $e){
                    Log::info('Reverse Shipment Delivered Status Sync Api Response for cn: '.$shipment_history->tracking_number.' in catch block');
                    $message = 'Reverse Shipment Delivered Status not updated at storefront | '.$shipment_history->marketplace_reference_id;
                    $activity_id = activity()
                    ->withProperties(['response' => json_encode($e->getMessage()) , 'dump' => $e->getTraceAsString()])
                    ->log('Reverse Shipment Delivered Status Synced Process');

                    // Mail::raw($message.' | '.$e->getMessage(), function ($m)  {
                    //     $m->to(['<EMAIL>','<EMAIL>','<EMAIL>',env('KHAADI_POC')])->subject('Reverse Shipment Delivered Status Synced Process');
                    // });
                    // Mail::raw($message.' | '.$e->getMessage(), function ($m)  {
                    //         $m->to(['<EMAIL>'])->subject('Reverse Shipment Delivered Status Synced Process');
                    // });
                    Log::info('Reverse Shipment Delivered Status Synced Process Api error '.$e->getMessage());

                    $process_status = 'Failed';
                    $status = $process_status;
                    $order_id = $shipment_history->order_id;
                    $key = 'Reverse Shipment Delivered Status Synced Process';
                    Log::info($message);
                }
                
            }
            else{
                $rs_stat = 1;
                $ignore_status_sync = true;
                Log::info('Alfred | Reverse Shipment other than Delivered');
            }

        }

        if($rs_stat == 1){
            
        } else {
            OrderComment::add(compact('order_id','message','key','status'));
        }

        if($delivered_stat == 1){
            $res = OrderStatusSync::update(array('message' => 'Payment settled by '.$shipment_history->name, 'status' => 'complete_instore','entity_id' => $shipment_history->entity_id)); 
            if(isset($res) && $res['error'] == 0){
                OrderComment::add(array('order_id' => $shipment_history->order_id, 'message' => $res['message'], 'key' => 'Storefront Synced Process', 'status' => 'Success'));
            }
        }

        if (!$ignore_status_sync) {
            if ($status == 'Success') {
                ShipmentCourierHistory::where('id', $shipment_history->id)->update(['storefront_sync' => 2, 'storefront_sync_at' => Carbon::now()->toDateTimeString() ]);
            } else {
                ShipmentCourierHistory::where('id', $shipment_history->id)->update(['storefront_sync' => 0 ]);
            }
        } else {
            ShipmentCourierHistory::where('id', $shipment_history->id)->update(['storefront_sync' => 2, 'storefront_sync_at' => NULL]);
        }
        

        
    }


    private function stringMatch($string, $array)
    {
        foreach ($array as $value) {
            if (stripos( strtolower($string), strtolower($value)) !== false) {
                return true;
            }
        }
        return false;
        
    }
}