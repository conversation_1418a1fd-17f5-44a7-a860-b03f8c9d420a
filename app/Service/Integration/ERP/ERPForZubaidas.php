<?php

namespace App\Service\Integration\ERP;

use GuzzleHttp\Client;

class ERPForZubaidas
{
   
    protected $http_client;

    public function __construct()
    {
        $this->http_client = new Client([
            'base_uri' => env('ERP_ZUBAIDAS_API_URL'),
            'timeout'  => 10.0,
        ]);
    }

    public function postReturnOrder(array $order_data)
    {
        try {
            $response = $this->http_client->post('PostReturnOrder', [
                'headers' => [
                    'x-api-key' => env('ERP_ZUBAIDAS_KEY'),
                    'Content-Type' => 'application/json',
                ],
                'json' => $order_data,
            ]);

            return [
                'success' => true,
                'status_code' => $response->getStatusCode(),
                'data' => json_decode($response->getBody()->getContents(), true),
            ];
        } catch (\Exception $exception) {
            return [
                'success' => false,
                'status_code' => $exception instanceof \GuzzleHttp\Exception\RequestException
                    ? ($exception->getResponse() ? $exception->getResponse()->getStatusCode() : 500)
                    : 500,
                'message' => $exception->getMessage(),
            ];
        }
    }



    public function getStockTransferOrders(array $data)
    {
        try {
            $response = $this->http_client->post('GetSTockTransferOrderV2', [
                'headers' => [
                    'x-api-key' => env('ERP_ZUBAIDAS_KEY'),
                    'Content-Type' => 'application/json',
                ],
                'json' => $data,
            ]);

            return [
                'success' => true,
                'status_code' => $response->getStatusCode(),
                'data' => json_decode($response->getBody()->getContents(), true),
            ];

        } catch (\Exception $exception) {

            return [
                'success' => false,
                'status_code' => $exception instanceof \GuzzleHttp\Exception\RequestException
                    ? ($exception->getResponse() ? $exception->getResponse()->getStatusCode() : 500)
                    : 500,
                'message' => $exception->getMessage(),
            ];
        }
    }



    public function createTransferOrder(array $data)
    {
        try {
            $response = $this->http_client->post('CreateStockTransferOrder', [
                'headers' => [
                    'x-api-key' => env('ERP_ZUBAIDAS_KEY'),
                    'Content-Type' => 'application/json',
                ],
                'json' => $data,
            ]);

            return [
                'success' => true,
                'status_code' => $response->getStatusCode(),
                'data' => json_decode($response->getBody()->getContents(), true),
            ];

        } catch (\Exception $exception) {

            return [
                'success' => false,
                'status_code' => $exception instanceof \GuzzleHttp\Exception\RequestException
                    ? ($exception->getResponse() ? $exception->getResponse()->getStatusCode() : 500)
                    : 500,
                'message' => $exception->getMessage(),
            ];
        }
    }



    public function postGRNPostedOrders(array $data)
    {
        try {
            $response = $this->http_client->post('PostGRN', [
                'headers' => [
                    'x-api-key' => env('ERP_ZUBAIDAS_KEY'),
                    'Content-Type' => 'application/json',
                ],
                'json' => $data,
            ]);

            return [
                'success' => true,
                'status_code' => $response->getStatusCode(),
                'data' => json_decode($response->getBody()->getContents(), true),
            ];

        } catch (\Exception $exception) {

            return [
                'success' => false,
                'status_code' => $exception instanceof \GuzzleHttp\Exception\RequestException
                    ? ($exception->getResponse() ? $exception->getResponse()->getStatusCode() : 500)
                    : 500,
                'message' => $exception->getMessage(),
            ];
        }
    }
    


}
