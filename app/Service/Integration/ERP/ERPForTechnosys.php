<?php

namespace App\Service\Integration\ERP;

use App\Models\Setting;
use GuzzleHttp\Client;
use Illuminate\Support\Facades\Log;

class ERPForTechnosys
{
    protected $http_client;
    protected $seller_id;
    protected $api_url;
    protected $username;
    protected $password;
    protected $app_key;

    public function __construct($seller_id = null)
    {
        $this->seller_id = $seller_id;
        $this->api_url = $this->getSetting('ERP_TECHNOSYS_API_URL', env('ERP_TECHNOSYS_API_URL'));
        $this->username = $this->getSetting('ERP_TECHNOSYS_USERNAME', env('ERP_TECHNOSYS_USERNAME'));
        $this->password = $this->getSetting('ERP_TECHNOSYS_PASSWORD', env('ERP_TECHNOSYS_PASSWORD'));
        $this->app_key = $this->getSetting('ERP_TECHNOSYS_APP_KEY', env('ERP_TECHNOSYS_APP_KEY'));
 
        $this->http_client = new Client([
            'base_uri' => $this->api_url,
            'timeout'  => 10.0,
        ]);
    }

    private function getSetting($key, $default = null)
    {
        // Try to get from settings table for this seller
        $setting = null;
        if ($this->seller_id) {
            $setting = Setting::where('seller_id', $this->seller_id)->where('key', $key)->value('value');
        }
        // If not found, fallback to env value
        if ($setting === null || $setting === '') {
            // If $default is not provided, use env directly
            if ($default === null) {
                $default = env($key);
            }
            return $default;
        }
        return $setting;
    }

    public function postReturnOrder(array $order_data)
    {
        try {
            $username = $this->username;
            $password = $this->password;
            $appKey = $this->app_key;
            $authHeader = 'Basic ' . base64_encode($username . ':' . $password);

            $headers_info = [
                'TS-AppKey' => $appKey,
                'Authorization' => $authHeader,
                'Content-Type' => 'application/json',
            ];

            $response = $this->http_client->post('TSBE/EStore/Return', [
                'headers' => $headers_info,
                'json' => $order_data,
            ]);

            return [
                'success' => true,
                'status_code' => $response->getStatusCode(),
                'data' => json_decode($response->getBody()->getContents(), true),
            ];
        } catch (\Exception $exception) {
            return [
                'success' => false,
                'status_code' => $exception instanceof \GuzzleHttp\Exception\RequestException
                    ? ($exception->getResponse() ? $exception->getResponse()->getStatusCode() : 500)
                    : 500,
                'message' => $exception->getMessage(),
            ];
        }
    }



    public function createTransferOrder(array $order_data)
    {
        try {
            $username = $this->username;
            $password = $this->password;
            $appKey = $this->app_key;
            $authHeader = 'Basic ' . base64_encode($username . ':' . $password);

            $headers_info = [
                'TS-AppKey' => $appKey,
                'Authorization' => $authHeader,
                'Content-Type' => 'application/json',
            ];

            $response = $this->http_client->post('TSBE/Inventory/CreateStockIssuence', [
                'headers' => $headers_info,
                'json' =>  $order_data
            ]);

            return [
                'success' => true,
                'status_code' => $response->getStatusCode(),
                'data' => json_decode($response->getBody()->getContents(), true),
            ];

        } catch (\Exception $exception) {

            return [
                'success' => false,
                'status_code' => $exception instanceof \GuzzleHttp\Exception\RequestException
                    ? ($exception->getResponse() ? $exception->getResponse()->getStatusCode() : 500)
                    : 500,
                'message' => $exception->getMessage(),
            ];

        }
    }



    public function postGRNPostedOrders(array $data)
    {
        try {
            $username = $this->username;
            $password = $this->password;
            $appKey = $this->app_key;
            $authHeader = 'Basic ' . base64_encode($username . ':' . $password);

            $headers_info = [
                'TS-AppKey' => $appKey,
                'Authorization' => $authHeader,
                'Content-Type' => 'application/json',
            ];

            $response = $this->http_client->post('TSBE/Inventory/CreateReceiveNote', [
                'headers' => $headers_info,
                'json' => $data
            ]);

            return [
                'success' => true,
                'status_code' => $response->getStatusCode(),
                'data' => json_decode($response->getBody()->getContents(), true),
            ];

        } catch (\Exception $exception) {

            return [
                'success' => false,
                'status_code' => $exception instanceof \GuzzleHttp\Exception\RequestException
                    ? ($exception->getResponse() ? $exception->getResponse()->getStatusCode() : 500)
                    : 500,
                'message' => $exception->getMessage(),
            ];

        }
    }

}
