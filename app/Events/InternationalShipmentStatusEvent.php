<?php

namespace App\Events;

use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class InternationalShipmentStatusEvent
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public $shipment, $force_sync;
    /**
     * Create a new event instance.
     *
     * @return void
     */
    public function __construct($shipment, $force_sync = false)
    {
        $this->shipment = $shipment;
        $this->force_sync = $force_sync;
    }

    /**
     * Get the channels the event should broadcast on.
     *
     * @return \Illuminate\Broadcasting\Channel|array
     */
    public function broadcastOn()
    {
        return new PrivateChannel('channel-name');
    }
}
