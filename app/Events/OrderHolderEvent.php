<?php

namespace App\Events;

use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class OrderHolderEvent
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public $request, $staging;

    /**
     * Create a new event instance.
     *
     * @return void
     */
    public function __construct($request, $staging = false)
    {
        $this->request = $request;
        $this->staging = $staging;
    }

}
