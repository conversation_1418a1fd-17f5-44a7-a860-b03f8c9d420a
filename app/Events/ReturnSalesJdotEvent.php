<?php

namespace App\Events;

use App\Helpers\JdotERP;
use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class ReturnSalesJdotEvent
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public $data;
    public $jdot_erp_obj;


    /**
     * Create a new event instance.
     *
     * @return void
     */
    public function __construct($data,JdotERP $jdot_erp_obj)
    {
        $this->data = $data;
        $this->jdot_erp_obj = $jdot_erp_obj;
    }

    /**
     * Get the channels the event should broadcast on.
     *
     * @return \Illuminate\Broadcasting\Channel|array
     */
    public function broadcastOn()
    {
        return new PrivateChannel('channel-name');
    }
}
