<?php

namespace App\Events;

use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class OrderTrackingEvent
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    /**
     * Create a new event instance.
     *
     * @return void
     */

    public $id, $entity_id, $marketplace_reference_id;
    
    public function __construct($id, $entity_id, $marketplace_reference_id)
    {
        $this->id = $id;
        $this->entity_id = $entity_id;
        $this->marketplace_reference_id = $marketplace_reference_id;
    }
}
