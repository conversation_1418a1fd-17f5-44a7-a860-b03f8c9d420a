<?php
return [

    'exception_types' => [
        'FBR' => 1,
        '<PERSON><PERSON><PERSON><PERSON>2WAYSYNC' => 2,
        'SHOPIFY2WAYSYNC' => 3,
        'MNPSTATUSMISMATCH' => 4,
        'RIDEREXTRASTATUS' => 5,
        'RIDERSTATUSTIMESTAMP' => 6,
        'TRAXEXTRASTATUS' => 7,
        'SHIPMENTSTATUSSYNC' => 8,
        'D<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>' => 9,
        'DYNAMICSSENDSHIPMENTAPI' => 10,
        'DYNAMICSGETORIGINAPI' => 11,
        'EXTRA-STATUS-AFTER-TERMINAL' => 12,
        'OVERALL-TERMINAL-STATUS-MISMATCH' => 13,
        'STATUS-TIMESTAMP-IS-SMALLER' => 14,
        'FALSE-RESPONSE' => 15,
        'INVALID-SHIPMENT-120' => 16,
        'MAGENTOREVERSESHIPMENTSYNC' => 17,
        'RETURNRECEIVESTOREFRONTSYNC' => 18,
    ],

    'env_vars' => [
        'KHAADIURL' => env('KHAADI_URL'),
        'REVE<PERSON>ESYNCTOKEN' => env('REVERSE_SYNC_TOKEN'),
    ],

    'bundled_email' => [
        'DELIVERYATTEMPT' => 0
    ],


    'stock_order_types' => [
        'NORMAL' => 0,
        'TRANSFER' => 1,
        'RETURN' => 2,
        'REJECTED-FULFILLMENT-ORDER' => 3,
        'REJECTED-TRANSFER-FULFILLMENT-ORDER' => 4,
        'CANCELLED-RETURN' => 5,
    ],


    'api_keys' => [
        'ERP_API_KEY' => 'erpAPIKey',
        'GTECH_SYSTEM_API_KEY' => 'gtechSystemAPIKey',
        'GTECH_SYSTEM_API_URL' => 'gtechSystemAPIUrl'
    ],
    'fulfillment_order_status' => [
        'OPEN' => 0,
        'REJECT' => 1,
        'CLOSED' => 2,
    ],
    'fulfillment_erp_sync_status' => [
        'NOT_SYNCED' => 0,
        'CREATED_SYNCED' => 1,
        'REJECTED_SYNCED' => 2,
    ],

    'jdot_erp_courier_id' => [
        4 => "MNP_COURIER_ID",
        5 => "LCS_COURIER_ID",
        7 => "CALL_COURIER_ID",
        8 => "500000",
        11 => "RIDER_COURIER_ID",
        13 => "TCS_COURIER_ID",
    ],
    

    'jdot_erp_bank_account' => [
        1 => "MBL-67756",
        4 => "MBL-67756",
        5 => "MBL-67756",
        7 => "MBL-67756",
        8 => "MBL-67756",
        11 => "MBL-67756",
        13 => "MBL-67756",
    ],


    'jdot_magento_erp_bank_account' => [
        1 => "MBL-67756",
        4 => "MBL-67756",
        5 => "MBL-67756",
        7 => "MBL-67756",
        8 => "MBL-67756",
        10 => "MBL-67756",
        11 => "MBL-67756",
        13 => "MBL-67756",
    ],


    'jdot_shopify_erp_bank_account' => [
        1 => "********",
        4 => "********",
        5 => "********",
        7 => "********",
        8 => "********",
        10 => "********",
        11 => "********",
        13 => "********",
    ],


    'shipment_status' => [
        'PENDING' => "Pending",
        'DELIVERED' => "Delivered",
        'READY_FOR_PICKUP' => "Ready for Pickup",
        'READY_FOR_DISPATCH' => 'Ready for Dispatch',
        'BOOKED' => 'Booked',
        'DISPATCHED' => 'Dispatched',
        'PICKED' => 'Picked',
        'PENDING_PICKUP'=> 'Pending Pickup',
        'PENDING_DELIVERY'=> 'Pending Delivery',
        'CANCELLED'=> 'Cancelled',
        'RETURNED'=> 'Return',
        'PENDING_RETURN' => 'Pending Return',
        'LOST' => 'Lost',
        'RETURNED_RECEIVED'=> 'Return Received',
    ],

    'shipment_status_jdot_mapping' => [
        'Booked' => "forwarded_order",
        'Delivered' => "delivered",
        'Return' => 'closed',
        'Dispatched' => 'complete',
        'Ready for Dispatch' => 'under_inprocess',
        'Return Received' => 'closed',
        'Lost' => 'shipment_lost',
    ],

    'robocall_events_short_status' => [
        1 => 'A01',
        2 => 'A02',
        3 => 'A03',
        4 => 'A04',
        5 => 'B01'
    ],

    'robocall_events_long_status' => [
        1 => 'Call attempted, but no response',
        2 => 'Call successfully received but input is not given',
        3 => 'Call successfully received, and input is given',
        4 => 'Call attempted but declined by the customer',
        5 => 'Sms delivery report (Confirmation sms and OTP)'
    ],
    'gtech_erp_courier_account_ids' => [
        1 => "0090014",
        4 => "0090015",
        5 => "0090003",
        7 => "0090001",
        8 => "0090017",
        10 => "0090002",
        11 => "0090010",
        15 => "0090013",
        17 => "0090012",
    ],

    'gtech_erp_courier_account_name' => [
        1 => "None",
        4 => "M&P",
        5 => "Leopards",
        7 => "Call Courier",
        8 => "TRAX",
        10 => "blueEx",
        11 => "TPL",
        15 => "Swyft",
        17 => "dhl",
    ],

    'candela_erp_courier_name' => [
        1 => "SELF",
        4 => "M&P",
        5 => "Leopards",
        7 => "Call Courier",
        8 => "TRAX",
        10 => "blueEX",
        11 => "TPL",
        15 => "Swyft",
        17 => "dhl",
        35 => "Leopards",
    ],

    'gtech_erp_payment_gateway_account_name' => [
        49 => "Safepay",
        50 => "BaadMay",
        51 => "UnumPay",
        54 => "PAYMOB",
    ],

    'gtech_erp_payment_gateway_account_ids' => [
        49 => "0090011",
        50 => "0090022",
        51 => "0090020",
        54 => "0090021",
    ],

    'jdot_magento_erp_courier_id' => [
        1 => "",
        4 => "",
        5 => "J-C-*********",
        7 => "J-C-*********",
        8 => "J-C-*********",
        10 => "J-C-*********",
        11 => "",
        13 => "J-C-*********",
        17=> "",
        35 => "J-C-*********",
    ],

    'almirah_pament_method_erp_account_id' => [
        45 => "A-C-*********",
        43 => "A-C-*********",
        12 => "A-C-*********",
        13 => "A-C-*********",
        57 => "A-C-*********",
        89 => "A-C-*********",
    ],

    'jdot_pament_method_erp_account_id' => [
        80 => "J-C-*********",
        81 => "J-C-*********",
        82 => "J-C-*********",
        84 => "J-C-*********",
        85 => "J-C-*********",
        95 => "J-C-*********",
        107 => "J-C-*********",
        114 => "J-C-*********",
        115 => "J-C-*********"
    ],

    'jdot_shopify_erp_courier_id' => [
        1 => "A-C-*********",
        4 => "",
        5 => "A-C-*********",
        7 => "A-C-*********",
        8 => "A-C-*********",
        10 => "A-C-*********",
        11 => "A-C-*********",
        13 => "A-C-*********",
        15 => "A-C-0000043",
        17 => "A-C-*********",
    ],

    'jdot_paid_account_id' => "300000",
    'jdot_cod_payment_method_id' => 83,
    'almirah_cod_payment_method_id' => 44,
    'almirah_rma_customer_account_id' => "A-C-*********",
    'jdot_rma_customer_account_id' => "J-C-*********",

    'almirah_charges_code_mapping' => [
        "Ladies Stole" => "Ladies Stole",
        "Men" => "Men",
        "Shoes" => "Shoes",
        "Chappal" => "Chappal",
    ],

    'jdot_charges_code_mapping' => [
        "Ladies Stole" => "Ladies Stole",
        "Men" => "Men",
        "Shoes" => "Shoes",
        "Chappal" => "Chappal",
    ],

];

?>