<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateBundledEmailsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('bundled_emails', function (Blueprint $table) {
            $table->id();
            $table->integer('seller_id');
            $table->integer('key');
            $table->integer('order_id')->nullable();
            $table->integer('shipment_id')->nullable();
            $table->string('marketplace_reference_id')->nullable();
            $table->string('tracking_number')->nullable();
            $table->string('details')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('bundled_emails');
    }
}
