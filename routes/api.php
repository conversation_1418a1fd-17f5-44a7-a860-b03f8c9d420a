<?php

use App\Events\InternationalShipmentStatusEvent;
use App\Events\OrderHolderEvent;
use App\Events\ShipmentStatusEvent;
use App\Helpers\OrderComment;
use App\Helpers\OrderStatusSync;
use App\Jobs\JDotOrderHolderJob;
use App\Jobs\ZeenMainOrderHolderJob;
use App\Jobs\ZeenOrderHolderJob;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Route;

use App\Models\Order;
use App\Models\OrderItem;
use App\Models\RMAItems;
use App\Models\RMARequest;
use App\Models\RMASetting;
use App\Models\Shipment;
use App\Models\SellerLocation;
use App\Models\Setting;
use App\Models\ShipmentCourierHistory;
use App\Models\ShipmentHistory;
use Carbon\Carbon;
use App\Traits\ExceptionTrait;
use App\Http\Controllers\API\RobocallController;
use App\Jobs\JafferjeesOrderHolderJob;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/

Route::middleware('auth:api')->get('/user', function (Request $request) {
    return $request->user();
});

Route::post('staging-khaadi/order', function(Request $request) {

    $request_data = array('data' => $request->all(), 'url' => $request->header('X-Origin-Url'), 'token' => $request->header('X-Auth-Token'));
    event(new OrderHolderEvent($request_data, true));
    return "Unity Retail | Order added in queue and will be added in your account";
});

Route::post('khaadi/order', function(Request $request) {

    try {
        $request_data = array('data' => $request->all(), 'url' => $request->header('X-Origin-Url'), 'token' => $request->header('X-Auth-Token'));
        event(new OrderHolderEvent($request_data));
        return "Unity Retail | Order added in queue and will be added in your account";
    } catch (Exception $e) {

        Mail::raw($e->getMessage().' | '.$request->header('X-Origin-Url').' | '.$request->header('X-Auth-Token').' |||| '.json_encode($request->all()), function ($m)  {
            $m->to('<EMAIL>')->subject('Khaadi Order Holder Endpoint');
        });

        $activity_id = activity()
            ->withProperties(['response' => $e->getMessage() , 'dump' => $e->getTraceAsString()])
            ->log('Khaadi Order Holder Endpoint');

        
        return "Failed to save order | ".$e->getMessage();
    }
});

Route::post('jdot/order', function(Request $request) {

    Log::info("Unity Retail | JDot | Origin URL");
    Log::info($request->header('X-Origin-Url'));
    
    try {
        $request_data = array('data' => $request->all(), 'url' => $request->header('X-Origin-Url'), 'token' => $request->header('X-Auth-Token'));
        JDotOrderHolderJob::dispatch($request_data)->onQueue('jDotOrderHolder');
        return "Unity Retail | JDot | Order added in queue and will be added in your account";
    } catch (Exception $e) {

        Mail::raw($e->getMessage().' | '.$request->header('X-Origin-Url').' | '.$request->header('X-Auth-Token').' |||| '.json_encode($request->all()), function ($m)  {
            $m->to('<EMAIL>')->subject('JDot Order Holder Endpoint');
        });

        $activity_id = activity()
            ->withProperties(['response' => $e->getMessage() , 'dump' => $e->getTraceAsString()])
            ->log('JDot Order Holder Endpoint');

        
        return "Failed to save order | ".$e->getMessage();
    }
});

Route::post('zeen/order', function(Request $request) {

    try {
        $request_data = [
            'data' => $request->all(),
            'shop' => $request->header('X-Shopify-Shop-Domain'),
            'token' => $request->header('X-Shopify-Hmac-Sha256'),
            'topic' => $request->header('X-Shopify-Topic')
        ];
        ZeenOrderHolderJob::dispatch($request_data)->onQueue('zeenOrderHolder');
        return "Unity Retail | Zeen | Order added in queue and will be added in your account";
    } catch (Exception $e) {

        Mail::raw($e->getMessage().' | '.$request->header('X-Shopify-Shop-Domain').' | '.$request->header('X-Shopify-Hmac-Sha256').' | '.$request->header('X-Shopify-Topic').' |||| '.json_encode($request->all()), function ($m)  {
            $m->to('<EMAIL>')->subject('Zeen Order Holder Endpoint');
        });

        $activity_id = activity()
            ->withProperties(['response' => $e->getMessage() , 'dump' => $e->getTraceAsString()])
            ->log('Zeen Order Holder Endpoint');

        
        return "Failed to save order | ".$e->getMessage();
    }
});

Route::post('zeen-main/order', function(Request $request) {

    try {
        $request_data = [
            'data' => $request->all(),
            'shop' => $request->header('X-Shopify-Shop-Domain'),
            'token' => $request->header('X-Shopify-Hmac-Sha256'),
            'topic' => $request->header('X-Shopify-Topic')
        ];
        ZeenMainOrderHolderJob::dispatch($request_data)->onQueue('zeenMainOrderHolder');
        return "Unity Retail | Zeen Main | Order added in queue and will be added in your account";
    } catch (Exception $e) {

        Mail::raw($e->getMessage().' | '.$request->header('X-Shopify-Shop-Domain').' | '.$request->header('X-Shopify-Hmac-Sha256').' | '.$request->header('X-Shopify-Topic').' |||| '.json_encode($request->all()), function ($m)  {
            $m->to('<EMAIL>')->subject('Zeen Main Order Holder Endpoint');
        });

        $activity_id = activity()
            ->withProperties(['response' => $e->getMessage() , 'dump' => $e->getTraceAsString()])
            ->log('Zeen Main Order Holder Endpoint');

        
        return "Failed to save order | ".$e->getMessage();
    }
});


Route::post('jafferjees/order', function(Request $request) {

    try {
        $request_data = [
            'data' => $request->all(),
            'shop' => $request->header('X-Shopify-Shop-Domain'),
            'token' => $request->header('X-Shopify-Hmac-Sha256'),
            'topic' => $request->header('X-Shopify-Topic')
        ];
        JafferjeesOrderHolderJob::dispatch($request_data)->onQueue('jafferjeesOrderHolder');
        return "Unity Retail | Jafferjees | Order added in queue and will be added in your account";
    } catch (Exception $e) {

        Mail::raw($e->getMessage().' | '.$request->header('X-Shopify-Shop-Domain').' | '.$request->header('X-Shopify-Hmac-Sha256').' | '.$request->header('X-Shopify-Topic').' |||| '.json_encode($request->all()), function ($m)  {
            $m->to('<EMAIL>')->subject('Jafferjees Order Holder Endpoint');
        });

        $activity_id = activity()
            ->withProperties(['response' => $e->getMessage() , 'dump' => $e->getTraceAsString()])
            ->log('Jafferjees Order Holder Endpoint');

        
        return "Failed to save order | ".$e->getMessage();
    }
});



Route::post('order/status/sync', function(Request $request) {

    if (!isset($request->status)) {
        return array('error' => 1, 'message' => 'Status parameter not found');
    } elseif (!isset($request->message)) {
        return array('error' => 1, 'message' => 'Message parameter not found');
    } elseif (!isset($request->entity_id)) {
        return array('error' => 1, 'message' => 'Entity ID parameter not found');
    }

    return OrderStatusSync::update($request->all()); 
});

Route::post('shipment/force-status-sync', function(Request $request) {

    if (!isset($request->status_id)) {
        return array('error' => 1, 'message' => 'Status ID parameter not found');
    } elseif (!isset($request->seller_id)) {
        return array('error' => 1, 'message' => 'Seller ID parameter not found');
    }

    if ($request->seller_id == 119) {
        
        $shipments =  ShipmentCourierHistory::where('shipment_courier_histories.id',$request->status_id)
                                                ->join('shipments','shipment_courier_histories.shipment_id','shipments.id')
                                                ->join('couriers','shipments.courier_id','couriers.id')
                                                ->join('orders','shipments.order_id','orders.id')
                                                ->join('seller_locations','shipments.seller_location_id','seller_locations.id')
                                                ->selectRaw('shipment_courier_histories.*,shipments.tracking_number,shipments.seller_location_id,shipments.order_id,orders.entity_id,orders.marketplace_reference_id,couriers.name,shipments.courier_id,shipments.type,seller_locations.location_name')
                                                ->get();
        if($shipments) {
            foreach ($shipments as $shipment) {
                event(new ShipmentStatusEvent($shipment->toArray(), true));
            }
            ShipmentCourierHistory::whereIn('id', $shipments->pluck('id'))->update(['storefront_sync' => 1]);
        }
    } else {
        $shipments =  ShipmentHistory::where('shipment_histories.id',$request->status_id)
                                        ->join('shipments','shipment_histories.shipment_id','shipments.id')
                                        ->join('couriers','shipments.courier_id','couriers.id')
                                        ->join('orders','shipments.order_id','orders.id')
                                        ->selectRaw('shipment_histories.*,shipments.tracking_number,shipments.seller_location_id,shipments.order_id,orders.entity_id,orders.marketplace_reference_id,orders.country,couriers.name,shipments.courier_id,shipments.type')
                                        ->get();

        if($shipments) {
            foreach($shipments as $shipment) {
                event(new InternationalShipmentStatusEvent($shipment->toArray(), true));
            }
            ShipmentHistory::whereIn('id', $shipments->pluck('id'))->update(['storefront_sync' => 1]);
        }
    }
    
    return ['error' => 0, 'message' => 'Shipment status sync request added in queue'];
});


// Dynamic 365 pickup location api

// Route::post('order/location/sync', function(Request $request){
//     if(!isset($request->order_id))
//     {
//         return array('error' => 1, 'message' => 'Order ID parameter not found');
//     }
//     elseif(!isset($request->location_id))
//     {
//         return array('error' => 1, 'message' => 'Location ID parameter not found');
//     }

//     $order = Order::whereSellerId('1')->where('marketplace_reference_id',$request->order_id)->first();
    
//     $location = SellerLocation::whereSellerId('1')->where('seller_reference_id',$request->location_id)->first();


//     if(!$order)
//     {
//         return array('error' => 1, 'message' => 'Order does not exist');
//     }

//     $shipment = Shipment::whereOrderId($order->id)->first();
//     if($shipment)
//     {
//         return array('error' => 1, 'message' => 'Shipment is already created for this order');
//     }

//     if(!$location)
//     {
//         return array('error' => 1, 'message' => 'Location does not exist');
//     }
          
//     // my code


//     event(new OrderHolderEvent($request_data));
//     return array('error' => 0, 'message' => 'Location set successfully');

// });

Route::post('return-request/create',function(Request $request){
    // return $request->all();
    // return base64_encode(hash_hmac('sha256', strval($request->cn), $request->header('X-Auth-Secret'), true));
    Log::info("RMA Api | Create Request | payload = " .$request);
    $error = 0;
    $message = 'Unity Retail | Return Request Create | Error | ';

    $reasons_array = ["Arrived Too Late","Changed Mind","Damaged","Defective","Exchange","Missing Parts","Product not as described","Ordered wrong item","Received wrong item","Got product at better price elsewhere","Product no longer required"];

    if(!($request->header('X-Auth-Token')))
    {
        $error = 1;
        $message .= 'Auth Token Required, ';
    }
    elseif(!isset($request->cn) && !isset($request->reason) && !isset($request->items))
    {
        $error = 1;
        $message .= 'Received empty request error occured, ';
    }
    elseif(!isset($request->cn))
    {
        $error = 1;
        $message .= 'CN parameter not found, ';
    }
    elseif(!isset($request->reason))
    {
        $error = 1;
        $message .= 'Reason parameter not found, ';
    }
    elseif(!isset($request->items))
    {
        $error = 1;
        $message .= 'Items parameter not found, ';
    }

    if($error == 0){
        $chk_secret = Setting::where('seller_id', 119)->where('key','secretKey')->first();

        // Check for API secret key
        if($chk_secret){
            $auth_token = base64_encode(hash_hmac('sha256', strval($request->cn), $chk_secret->value, true));

            if($request->header('X-Auth-Token') != $auth_token){
                
                $error = 1;
                $message .= 'Invalid Auth Token, ';

                $activity_id = activity()
                        ->withProperties(['response' => $message , 'request' => $request])
                        ->log('RMA Api - Create Request');
                
                Mail::raw($message.' | Payload | '.$request, function ($m)  {
                    $m->to('<EMAIL>')
                        ->subject('RMA Api - Create Request');
                });

                return array('error' => 1, 'message' => $message);
            }
    
            //check if order exist
            $shipment = Shipment::whereTrackingNumber($request->cn)->whereStatus('Delivered')->where('seller_id',119)->first();
            if(!$shipment){
                $error = 1;
                $message .= 'Shipment either not exist or not in Delivered State, ';

                $activity_id = activity()
                        ->withProperties(['response' => $message , 'request' => $request])
                        ->log('RMA Api - Create Request');

                Mail::raw($message.' | Payload | '.$request, function ($m)  {
                    $m->to('<EMAIL>')
                        ->subject('RMA Api - Create Request');
                });
    
                return array('error' => 1, 'message' => $message);
            }
            else{
                foreach($request->items as $item){
                    if(!isset($item['SKU'])){
                        $error = 1;
                        $message .= 'Item sku parameter not found, ';
                    }
                    elseif(!isset($item['quantity'])){
                        $error = 1;
                        $message .= 'Item quantity parameter not found, ';
                    }
        
                    if($item['quantity'] < 1){
                        $error = 1;
                        $message .= 'Item quantity cannot be less than 1, ';
                    }
        
                    //check if item exist in this order
                    $order_item = OrderItem::whereOrderId($shipment->order_id)->where('SKU',$item['SKU'])->first();
                    if(!$order_item)
                    {
                        $error = 1;
                        $message .= 'Item '.$item['SKU'].' does not exist in this order, ';
                    }
        
                    if($item['quantity'] > $order_item->quantity){
                        $error = 1;
                        $message .= 'Quantity of Item '.$item['SKU'].' cannot be greater than '.$order_item->quantity.', ';
                    }
                }
                if(!in_array($request->reason,$reasons_array)){
                    $error = 1;
                    $message .= 'Unknown Reason';
                }
                /////////////////
                // Main work here
                /////////////////
                if($error == 0){
                    try{
                        $rma_check = RMARequest::whereSellerId($shipment->seller_id)->whereShipmentId($shipment->id);
                        if($rma_check->exists()){

                            $activity_id = activity()
                            ->withProperties(['response' => 'Unity Retail | Return Request Create | Error | Return Request of this shipment have already been created' , 'request' => $request])
                            ->log('RMA Api - Create Request');

                            // Mail::raw('Unity Retail | Return Request Create | Error | Return Request of this shipment have already been created | Payload | '.$request, function ($m)  {
                            //     $m->to('<EMAIL>')
                            //         ->subject('RMA Api - Create Request');
                            // });

                            return array('error' => 1, 'message' => 'Unity Retail | Return Request Create | Error | Return Request of this shipment have already been created');
                        }
                        else{
                            $rma_request = new RMARequest;
                            $rma_request->seller_id = $shipment->seller_id;
                            $rma_request->shipment_id = $shipment->id;
                            $rma_request->return_request_reason = $request->reason;
                            $rma_request->return_request_datetime = Carbon::now()->toDateTimeString();
                            $rma_request->save();
                
                            foreach($request->items as $item){
                
                                $order_item = OrderItem::whereOrderId($shipment->order_id)->where('SKU',$item['SKU'])->first();
                
                                $rma_item = new RMAItems;
                                $rma_item->rma_requests_id = $rma_request->id;
                                $rma_item->order_items_id = $order_item->id;
                                $rma_item->quantity = $item['quantity'];
                                $rma_item->save();
                            }
                
                            $status = 'Success';
                            $order_id = $shipment->order_id;
                            $key = 'Return Merchandise Authorization from API';
                            $comment_message = 'Return Request Initiated Against Shipment # '. $shipment->tracking_number  . '';
                
                
                            // OrderComment::add(compact('order_id','comment_message','key','status'));
                            OrderComment::add(array('order_id' => $order_id,'message' => $comment_message,'key' => $key,'status' => $status));
                
                            return array('error' => 0, 'message' => 'Unity Retail | Return Request Create | Success | Successfully created');
                        }
                    }
                    catch(Exception $e){
                        $status = 'Failed';
                        $order_id = $shipment->order_id;
                        $key = 'Return Merchandise Authorization from API';
                        $comment_message = 'Unity Retail | Return Request Create | Error | '.$e->getMessage();
            
                        // OrderComment::add(compact('order_id','comment_message','key','status'));
                        OrderComment::add(array('order_id' => $order_id,'message' => $comment_message,'key' => $key,'status' => $status));

                        $activity_id = activity()
                        ->withProperties(['response' => 'Unity Retail | Return Request Create | Catch Error | '.$e->getMessage() , 'request' => $request])
                        ->log('RMA Api - Create Request');

                        Mail::raw('Unity Retail | Return Request Create | Catch Error | '.$e->getMessage().' | Payload | '.$request, function ($m)  {
                            $m->to('<EMAIL>')
                                ->subject('RMA Api - Create Request');
                        });
        
                        return array('error' => 1, 'message' => 'Unity Retail | Return Request Create | Error | '.$e->getMessage());
                    }
                }
                else{
                    $status = 'Failed';
                    $order_id = $shipment->order_id;
                    $key = 'Return Merchandise Authorization from API';
                    $comment_message = $message;
        
                    // OrderComment::add(compact('order_id','comment_message','key','status'));
                    OrderComment::add(array('order_id' => $order_id,'message' => $comment_message,'key' => $key,'status' => $status));

                    $activity_id = activity()
                    ->withProperties(['response' => $message , 'request' => $request])
                    ->log('RMA Api - Create Request');

                    Mail::raw($message.' | Payload | '.$request, function ($m)  {
                        $m->to('<EMAIL>')
                            ->subject('RMA Api - Create Request');
                    });
        
                    return array('error' => 1, 'message' => $message);
                }
            } 
        }
        else{

            $activity_id = activity()
            ->withProperties(['response' => 'Unity Retail | Return Request Create | Error | Secret key not found in Unity' , 'request' => $request])
            ->log('RMA Api - Create Request');

            Mail::raw('Unity Retail | Return Request Create | Error | Secret key not found in Unity | Payload | '.$request, function ($m)  {
                $m->to('<EMAIL>')
                    ->subject('RMA Api - Create Request');
            });

            return array('error' => 1, 'message' => 'Unity Retail | Return Request Create | Error | Secret key not found in Unity');
        }
        
    }
    else{
        $activity_id = activity()
        ->withProperties(['response' => $message , 'request' => $request])
        ->log('RMA Api - Create Request');

        Mail::raw($message.' | Payload | '.$request, function ($m)  {
            $m->to('<EMAIL>')
                ->subject('RMA Api - Create Request');
        });
        return array('error' => 1, 'message' => $message);
    }
});

Route::post('return-request/authorize',function(Request $request){
    // return $request->all();
    Log::info("RMA Api | Authorize Request | payload = " .$request);
    $error = 0;
    $message = 'Unity Retail | Return Request Authorization | Error | ';

    if(!($request->header('X-Auth-Token')))
    {
        $error = 1;
        $message .= 'Auth Token Required, ';
    }
    elseif(!isset($request->cn) && !isset($request->status))
    {
        $error = 1;
        $message .= 'Received empty request error occured,, ';
    }
    elseif(!isset($request->cn))
    {
        $error = 1;
        $message .= 'CN parameter not found, ';
    }
    elseif(!isset($request->status))
    {
        $error = 1;
        $message .= 'Status parameter not found, ';
    }

    if($error == 0){

        $chk_secret = Setting::where('seller_id', 119)->where('key','secretKey')->first();

        if($chk_secret){
            $auth_token = base64_encode(hash_hmac('sha256', strval($request->cn), $chk_secret->value, true));

            if($request->header('X-Auth-Token') != $auth_token){
                $activity_id = activity()
                ->withProperties(['response' => 'Unity Retail | Return Request Authorization | Error | Invalid Auth Token' , 'request' => $request])
                ->log('RMA Api - Authorize Request');

                Mail::raw('Unity Retail | Return Request Authorization | Error | Invalid Auth Token | Payload | '.$request, function ($m)  {
                    $m->to('<EMAIL>')
                        ->subject('RMA Api - Authorize Request');
                });
                return array('error' => 1, 'message' => 'Unity Retail | Return Request Authorization | Error | Invalid Auth Token');
            }
    
            if($request->status == 0 || $request->status == 1)
            {
                if(!isset($request->remarks))
                {
                    return array('error' => 1, 'message' => 'Remarks parameter not found');
                }

                $shipment = Shipment::whereTrackingNumber($request->cn)->whereStatus('Delivered')->where('seller_id',119)->first();
                if($shipment){
                    $rma_request = RMARequest::whereShipmentId($shipment->id)->where('approved','Pending')->first();
                    if($rma_request){
                        if($request->status == 0){

                            $rma_request->approved = 'Rejected';
                            $rma_request->remarks = $request->remarks;
                            $rma_request->save();
                        
                            return array('error' => 0, 'message' => 'Unity Retail | Return Request is successfully Rejected');
                        }
                        elseif($request->status == 1){

                            try{
                                $auth_token = base64_encode(hash_hmac('sha256', strval($shipment->id), 'unity-mvp', true));


                                $curl = curl_init();

                                curl_setopt_array($curl, array(
                                CURLOPT_URL => env('ENDPOINT_URL')."/api/rma/reverse-shipment/book",
                                CURLOPT_RETURNTRANSFER => true,
                                CURLOPT_ENCODING => "",
                                CURLOPT_MAXREDIRS => 10,
                                CURLOPT_TIMEOUT => 60,
                                CURLOPT_FOLLOWLOCATION => true,
                                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                                CURLOPT_CUSTOMREQUEST => "POST",
                                CURLOPT_POSTFIELDS =>array('shipment_id' => $shipment->id),
                                CURLOPT_HTTPHEADER => array(
                                    "X-Auth-Token: ".$auth_token
                                ),
                                ));

                                $unity_response = json_decode(curl_exec($curl),true);

                                $httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);

                                curl_close($curl);

                                if($httpCode == '200')
                                {
                                    if($unity_response['error'] == 0)
                                    {
                                        Log::info('Got success from unity');
                                        $process_status = 'Success';
                                        // $message .= "Order is updated with <b>".$seller_location->location_name."</b> as its Pickup Location";

                                        $rma_request->approved = 'Approved';
                                        $rma_request->remarks = $request->remarks;
                                        $rma_request->save();

                                        $comment_message = 'Reverse Shipment Booked Successfully with <br>CNNumber: <b>'.$unity_response['cn'].'</b>';
                                        $status = 'Success';
                                        $order_id = $shipment->order->id;
                                        $key = 'Return Request Authorization API';

                                        // OrderComment::add(compact('order_id','comment_message','key','status'));
                                        OrderComment::add(array('order_id' => $order_id,'message' => $comment_message,'key' => $key,'status' => $status));
                                        
                                        return array('error' => 0, 'message' => 'Unity Retail | Return Request Authorization | Success | Return Request is successfully booked', 'CNNumber' => $unity_response['cn']);

                                    }
                                    else{
                                        $message .= $unity_response['message'];
                                        $error = 1;

                                        $comment_message = $message;
                                        $status = 'Failed';
                                        $order_id = $shipment->order->id;
                                        $key = 'Return Request Authorization API';

                                        $activity_id = activity()
                                        ->withProperties(['response' => 'Unity API Response | '.$message , 'request' => $request])
                                        ->log('RMA Api - Authorize Request');

                                        Mail::raw('Unity API Response | '.$message.' | Payload | '.$request, function ($m)  {
                                            $m->to('<EMAIL>')
                                                ->subject('RMA Api - Authorize Request');
                                        });

                                        // OrderComment::add(compact('order_id','comment_message','key','status'));
                                        OrderComment::add(array('order_id' => $order_id,'message' => $comment_message,'key' => $key,'status' => $status));

                                        return array('error' => 1, 'message' => 'Unity API Response | '.$message);

                                    }
                                    
                                }
                                else{
                                    $message .= $httpCode;
                                    $error = 1;

                                    $comment_message = $message;
                                    $status = 'Failed';
                                    $order_id = $shipment->order->id;
                                    $key = 'Return Request Authorization API';

                                    // OrderComment::add(compact('order_id','comment_message','key','status'));
                                    OrderComment::add(array('order_id' => $order_id,'message' => $comment_message,'key' => $key,'status' => $status));

                                    $activity_id = activity()
                                    ->withProperties(['response' => 'Unity API Response Http Code | '.$message , 'request' => $request])
                                    ->log('RMA Api - Authorize Request');

                                    Mail::raw('Unity API Response Http Code | '.$message.' | Payload | '.$request, function ($m)  {
                                        $m->to('<EMAIL>')
                                            ->subject('RMA Api - Authorize Request');
                                    });

                                    return array('error' => 1, 'message' => 'Unity API Response Http Code | '.$message);

                                }

                                if($error == 1){
                                    $comment_message = $message;
                                    $status = 'Failed';
                                    $order_id = $shipment->order->id;
                                    $key = 'Return Request Authorization API';
                                }


                            }catch(Exception $e){
                                $message .= 'Unity Reverse Shipment Booking API Error';
                                // $activity_id = activity()
                                // ->withProperties(['response' => json_encode($e->getMessage()) , 'dump' => $e->getTraceAsString()])
                                // ->log('Reverse Shipment Booking Api');
                    
                                // Mail::raw($message.' | '.$e->getMessage(), function ($m)  {
                                //     $m->to('<EMAIL>')->subject('Reverse Shipment Booking Api');
                                // });


                                $activity_id = activity()
                                ->withProperties(['response' => 'Unity Retail | Return Request Authorization | Error | '.$message.' | '.$e->getMessage() , 'request' => $request])
                                ->log('RMA Api - Authorize Request');

                                Mail::raw('Unity Retail | Return Request Authorization | Error | '.$message.' | '.$e->getMessage().' | Payload | '.$request, function ($m)  {
                                    $m->to('<EMAIL>')
                                        ->subject('RMA Api - Authorize Request');
                                });

                                $comment_message = $message;
                                $status = 'Failed';
                                $order_id = $shipment->order->id;
                                $key = 'Return Request Authorization API';

                                // OrderComment::add(compact('order_id','comment_message','key','status'));
                                OrderComment::add(array('order_id' => $order_id,'message' => $comment_message,'key' => $key,'status' => $status));

                                return array('error' => 1, 'message' => 'Unity Retail | Return Request Authorization | Error | '.$message.' | '.$e->getMessage());

                            }
                        }
                        
                    }
                    else{

                        $activity_id = activity()
                        ->withProperties(['response' => 'Unity Retail | Return Request Authorization | Error | Return Request against this shipment not found' , 'request' => $request])
                        ->log('RMA Api - Authorize Request');

                        // Mail::raw('Unity Retail | Return Request Authorization | Error | Return Request against this shipment not found | Payload | '.$request, function ($m)  {
                        //     $m->to('<EMAIL>')
                        //         ->subject('RMA Api - Authorize Request');
                        // });
                        return array('error' => 1, 'message' => 'Unity Retail | Return Request Authorization | Error | Return Request against this shipment not found');
                    }
                }
                else{

                    $activity_id = activity()
                    ->withProperties(['response' => 'Unity Retail | Return Request Authorization | Error | Shipment not found' , 'request' => $request])
                    ->log('RMA Api - Authorize Request');

                    Mail::raw('Unity Retail | Return Request Authorization | Error | Shipment not found | Payload | '.$request, function ($m)  {
                        $m->to('<EMAIL>')
                            ->subject('RMA Api - Authorize Request');
                    });
                    return array('error' => 1, 'message' => 'Unity Retail | Return Request Authorization | Error | Shipment not found');
                }
            }
            else{
                
                $activity_id = activity()
                ->withProperties(['response' => 'Unity Retail | Return Request Authorization | Error | Status is not Valid' , 'request' => $request])
                ->log('RMA Api - Authorize Request');

                Mail::raw('Unity Retail | Return Request Authorization | Error | Status is not Valid | Payload | '.$request, function ($m)  {
                    $m->to('<EMAIL>')
                        ->subject('RMA Api - Authorize Request');
                });
                return array('error' => 0, 'message' => 'Unity Retail | Return Request Authorization | Error | Status is not Valid');
            }
        }
        else{

            $activity_id = activity()
            ->withProperties(['response' => 'Unity Retail | Return Request Authorization | Error | '.$message.' | '.$e->getMessage() , 'request' => $request])
            ->log('RMA Api - Authorize Request');

            Mail::raw('Unity Retail | Return Request Authorization | Error | '.$message.' | '.$e->getMessage().' | Payload | '.$request, function ($m)  {
                $m->to('<EMAIL>')
                    ->subject('RMA Api - Authorize Request');
            });
            return array('error' => 1, 'message' => 'Unity Retail | Return Request Authorization | Error | Secret key not found in Unity');
        }
    }
    else{
        $activity_id = activity()
        ->withProperties(['response' => $message , 'request' => $request])
        ->log('RMA Api - Authorize Request');

        Mail::raw($message.' | Payload | '.$request, function ($m)  {
            $m->to('<EMAIL>')
                ->subject('RMA Api - Authorize Request');
        });

        return array('error' => 1, 'message' => $message);
    }
    
   
    
    

    

});

Route::post('/order/origin/get',function(Request $request){
    if(!($request->header('X-Auth-Token')))
    {
        return array('error' => 1, 'message' => 'Auth Token Required');
    }
    elseif(!isset($request->order_id))
    {
        return array('error' => 1, 'message' => 'Order ID parameter not found');
    }
    
    $auth_token = base64_encode(hash_hmac('sha256', strval($request->order_id), 'yoitsasecretkey', true));
    if($request->header('X-Auth-Token') != $auth_token){
        return array('error' => 1, 'message' => 'Invalid Auth Token');
    }
    else{
        return array('error' => 0, 'message' => 'Success', 'location_id' => '4023', 'order_id' => $request->order_id);
    }
    
});

Route::post('/order/booking-details',function(Request $request){
    if(!($request->header('X-Auth-Token')))
    {
        return array('error' => 1, 'message' => 'Auth Token Required');
    }
    elseif(!isset($request->order_id))
    {
        return array('error' => 1, 'message' => 'Order ID parameter not found');
    }
    elseif(!isset($request->cn_number))
    {
        return array('error' => 1, 'message' => 'CN Number parameter not found');
    }
    elseif(!isset($request->courier_code))
    {
        return array('error' => 1, 'message' => 'Courier Code parameter not found');
    }
    elseif(!isset($request->fbr_invoice_number))
    {
        return array('error' => 1, 'message' => 'FBR Invoice Number parameter not found');
    }

    $auth_token = base64_encode(hash_hmac('sha256', strval($request->order_id), 'yoitsasecretkey', true));
    if($request->header('X-Auth-Token') != $auth_token){
        return array('error' => 1, 'message' => 'Invalid Auth Token');
    }
    else{
        return array('error' => 0, 'message' => 'Booking Details saved successfully');
    }
    
});

Route::post('v1/robocall/logs/getEvents',[RobocallController::class,'getEvents'])->middleware('RobocallLogs');
Route::post('v1/robocall/logs/getItsEvents',[RobocallController::class,'getItsEvents'])->middleware('ItsRobocallLogs');
Route::post('v1/robocall/logs/getH3Events',[RobocallController::class,'getH3Events'])->middleware('HthreeRobocallLogs');
Route::post('v1/robocall/logs/get-h3-merchant-events',[RobocallController::class,'getH3MerchantEvents'])->middleware('HthreeRobocallLogs');

