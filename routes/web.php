<?php

use Illuminate\Routing\RouteGroup;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

Route::get('/synergy', 'SynergyController@index');

Route::get('/cron/shipment-status','ShipmentController@index');

Route::get('/cron/khaadi-return-status','ShipmentController@khaadiReturnStatus');

Route::get('/cron/international-shipment-status','ShipmentController@internationalStatusSync');

Route::get('/khaadi-status-sync-report','ShipmentController@temporaryStatusReport');

Route::get('/cron/order-tracking','ShipmentController@orderTracking');

Route::get('/cron/shipments-sync','ShipmentController@syncShipments');

//Route::get('/cron/shipment-latest-status','ShipmentController@latest_status');

Route::get('/cron/non-origin-orders','ShipmentController@nonOriginOrders');

Route::get('/cron/send-shipment-details','ShipmentController@sendShipmentDetails');

Route::get('/cron/delivery-attempt-emails','ShipmentController@deliveryAttemptEmail');

Route::get('/cron/jdot/get-stock-orders/{seller_id}','JdotController@getStockOrders');
Route::get('/cron/jdot/get-grn-posted-orders/{seller_id}','JdotController@getGRNPostedOrders');
Route::get('/cron/jdot/get-grn-partial-posted-orders/{seller_id}','JdotController@getGRNPartialPostedOrders');
Route::get('/cron/jdot/get-unassigned-orders/{seller_id}','JdotController@getUnassignedOrders');
Route::get('/cron/jdot/get-fulfilment-rejected-orders/{seller_id}','JdotController@getFulfilmentRejectedOrders');
Route::get('/cron/jdot/get-cod-recieved-orders/{seller_id}','JdotController@getCODRecievedOrders');
Route::get('/cron/jdot/get-all-cod-recieved-orders/{seller_id}','JdotController@getAllCODRecievedOrders');
Route::get('/cron/jdot/get-return-marked-orders/{seller_id}','JdotController@getReturnMarkedOrders');
Route::get('/cron/jdot/get-return-marked-orders-in-bulk/{seller_id}','JdotController@getReturnMarkedOrdersInBulk');
Route::get('/cron/jdot/bulk-order-status-sync/{seller_id}','JdotController@bulkShipmentStatusSyncMagento');

Route::get('/cron/jdot/get-transfer-orders-picked/{seller_id}','JdotController@getTransferOrderesThatArePicked');
Route::get('/cron/jdot/get-confirmed-marked-orders-shopify/{seller_id}','JdotController@getConfirmedMarkedOrdersForShopify');
Route::get('/cron/jdot/get-confirmed-marked-orders-magento/{seller_id}','JdotController@getConfirmedMarkedOrdersForMagento');
Route::get('/cron/jdot/get-shipment-updates/{seller_id}','JdotController@getShipmentUpdates');
Route::get('/cron/jdot/get-shipments-for-cn-updates/{seller_id}','JdotController@getShipmentsForCNUpdates');
Route::get('/cron/jdot/get-confirmed-marked-orders-shopify-bulk/{seller_id}','JdotController@getConfirmedMarkedOrdersForShopifyBulk');
Route::get('/cron/jdot/get-cancelled-orders/{seller_id}','JdotController@getCancelledOrders');
Route::get('/cron/jdot/get-picked-orders/{seller_id}','JdotController@getPickedOrders');
Route::get('/cron/jdot/get-orders-with-ordertag-unconfirmed1/{seller_id}','JdotController@getOrdersWithOrderTagUnconfirmed1');
Route::get('/cron/jdot/get-orders-with-ordertag-unconfirmed2/{seller_id}','JdotController@getOrdersWithOrderTagUnconfirmed2');
Route::get('/cron/jdot/get-orders-with-ordertag-unconfirmed3/{seller_id}','JdotController@getOrdersWithOrderTagUnconfirmed3');

/* Jdot D365 SQL sync */

Route::get('/cron/jdot/get-all-sales-push-dynamics/{seller_id}','JdotController@getAllSalesAndPushToDynamics');
Route::get('/cron/jdot/get-grn-postings-push-dynamics/{seller_id}','JdotController@getGrnPostingsAndPushToDynamics');
Route::get('/cron/jdot/get-transfer-orders-picked-push-dynamics/{seller_id}','JdotController@getTransferOrdersPickedAndPushToDynamics');


Route::get('/cron/gtech/send-delivered-shipment-details/{seller_id}','GTechController@sendDeliveredShipmentDetailsToGtech');
Route::get('/cron/gtech/send-return-shipment-details/{seller_id}','GTechController@sendReturnShipmentDetailsToGtech');
Route::get('/cron/gtech/get-transfer-orders/{seller_id}','GTechController@getTransferOrders');
Route::get('/cron/gtech/send-return-shipment-details-bulk','GTechController@sendReturnShipmentDetailsToGtechInBulk');
Route::get('/cron/gtech/send-cancelled-shipment-details/{seller_id}','GTechController@sendCancelledShipmentDetailsToGtech');
Route::get('/cron/gtech/send-cancelled-shipment-details-bulk','GTechController@sendCancelledShipmentDetailsToGtechInBulk');
Route::get('/cron/gtech/get-receive-finish/{seller_id}','GTechController@getReceiveFinish');
// Route::get('/cron/gtech/get-specific-transfer-orders/{seller_id}','GTechController@getSpecificTransferOrders');

/** Candela Routes */
Route::get('/cron/candela/send-return-shipment-details/{seller_id}','CandelaController@sendReturnShipmentDetailsToCandela');
Route::get('/cron/candela/send-cancelled-shipment-details/{seller_id}','CandelaController@sendCancelledShipmentDetailsToCandela');

/** Zubaidas Routes */
Route::get('/cron/zubaidas/send-return-shipment-details/{seller_id}','ZubaidasController@getReturnMarkedOrders');


/** Technosys Routes */
Route::get('/cron/technosys/send-return-shipment-details/{seller_id}','TechnosysController@getReturnMarkedOrders');
Route::get('/cron/technosys/get-transfer-orders/{seller_id}','TechnosysController@getTransferOrders');
Route::get('/cron/technosys/get-grn-posted-orders/{seller_id}','TechnosysController@getGRNPostedOrders');



Auth::routes();
Route::middleware(['auth'])->group(function () {
    Route::post('/force-shipment-status-sync/run','ShipmentController@forceShipmentStatusSync');
    Route::get('/force-shipment-status-sync','ShipmentController@forceShipmentStatusSyncPage');

    Route::get('/revert-wrong-cancellation','ShipmentController@revertWrongCancellation');

    Route::get('/old-cancelled-orders','ShipmentController@getOldCancelledOrders');

    Route::get('/avg-booking-time','ShipmentController@avgBookingTime');

    Route::get('/assign-cns','ShipmentController@assignCNs');

    Route::post('/force-dynamic-sync/run','ShipmentController@forceDynamicSync');
    Route::get('/force-dynamic-sync','ShipmentController@forceDynamicSyncPage');
});

Route::group(['prefix' => 'swich'], function() {
	Route::get('checkout', 'PaymentController@paymentDetailsSwichRuntime');
});
// Route::post('/force-shipment-status-sync/run','ShipmentController@forceShipmentStatusSync');
// Route::get('/force-shipment-status-sync','ShipmentController@forceShipmentStatusSyncPage');

// Route::get('/revert-wrong-cancellation','ShipmentController@revertWrongCancellation');

// Route::get('/old-cancelled-orders','ShipmentController@getOldCancelledOrders');

// Route::get('/avg-booking-time','ShipmentController@avgBookingTime');

// Route::get('/assign-cns','ShipmentController@assignCNs');

// Route::post('/force-dynamic-sync/run','ShipmentController@forceDynamicSync');
// Route::get('/force-dynamic-sync','ShipmentController@forceDynamicSyncPage');


// Route::get('/home', 'HomeController@index')->name('home');
